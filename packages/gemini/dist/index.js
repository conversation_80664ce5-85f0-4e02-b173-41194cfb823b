"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  GoogleGenaiClient: () => GoogleGenaiClient,
  uploadFileToGemini: () => uploadFileToGemini,
  useGemini: () => useGemini,
  useGeminiAccessToken: () => useGeminiAccessToken
});
module.exports = __toCommonJS(index_exports);

// src/createGeminiAxios.ts
var import_axios2 = __toESM(require("axios"));

// src/google-genai-client.ts
var import_axios = __toESM(require("axios"));
var GoogleGenaiClient = class {
  config;
  constructor(config) {
    this.config = config;
  }
  /**
   * 计算属性：通过 Cloudflare Gateway 调用的 URL
   */
  get gatewayUrl() {
    const randomRegion = this.config.regions[Math.floor(Math.random() * this.config.regions.length)];
    return `https://gateway.ai.cloudflare.com/v1/${this.config.cloudflareProjectId}/${this.config.cloudflareGatewayId}/google-vertex-ai/v1/projects/${this.config.googleProjectId}/locations/${randomRegion}/publishers/google/models`;
  }
  /**
   * 生成内容
   * @param modelId 模型 ID，如 'gemini-2.5-flash'
   * @param contents 内容数组
   * @param config 生成配置
   * @param timeout 超时时间（秒）
   * @returns 生成结果和状态码
   */
  async generateContent(contents, modelId = "gemini-2.5-flash", config, timeout = 30) {
    try {
      const requestBody = {
        contents,
        ...config && { generationConfig: config }
      };
      const jsonBody = JSON.stringify(requestBody, (key, value) => {
        if (value === null || value === void 0) {
          return void 0;
        }
        return value;
      }, 2);
      const url = `${this.gatewayUrl}/${modelId}:generateContent`;
      const response = await import_axios.default.post(
        url,
        jsonBody,
        {
          timeout: timeout * 1e3,
          // 转换为毫秒
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.config.accessToken}`
          }
        }
      );
      if (response.status !== 200) {
        return {
          response: null,
          statusCode: response.status
        };
      }
      return {
        response: response.data,
        statusCode: response.status
      };
    } catch (error) {
      if (error.response) {
        return {
          response: null,
          statusCode: error.response.status
        };
      } else if (error.request) {
        return {
          response: null,
          statusCode: 0
        };
      } else {
        return {
          response: null,
          statusCode: -1
        };
      }
    }
  }
  /**
   * 简化的文本生成方法
   * @param modelId 模型 ID
   * @param prompt 文本提示
   * @param config 生成配置
   * @returns 生成的文本内容
   */
  async generateText(prompt, modelId = "gemini-2.5-flash", config) {
    const contents = [
      {
        role: "user",
        parts: [{ text: prompt }]
      }
    ];
    const result = await this.generateContent(contents, modelId, config);
    if (result.statusCode === 200 && result.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      return result.response.candidates[0].content.parts[0].text;
    }
    return null;
  }
  /**
   * 获取配置信息
   */
  getConfig() {
    return { ...this.config };
  }
  /**
   * 更新访问令牌
   */
  updateAccessToken(accessToken) {
    this.config.accessToken = accessToken;
  }
};
function createDefaultGoogleGenaiClient(accessToken) {
  return new GoogleGenaiClient({
    cloudflareProjectId: "67720b647ff2b55cf37ba3ef9e677083",
    cloudflareGatewayId: "bowong-dev",
    googleProjectId: "gen-lang-client-0413414134",
    regions: ["us-central1"],
    // 使用稳定的区域
    accessToken
  });
}

// src/createGeminiAxios.ts
var import_form_data = require("form-data");
var MODAL_URL = `https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run`;
function useGeminiAxios() {
  return import_axios2.default.create({
    baseURL: MODAL_URL,
    headers: {
      Authorization: `Bearer bowong7777`
    }
  });
}
async function useGeminiAccessToken() {
  const geminiAxios = useGeminiAxios();
  const token = await geminiAxios.request({
    method: `get`,
    url: `/google/access-token`
  }).then((res) => res.data);
  return token;
}
async function uploadFileToGemini(bucket, prefix, formData) {
  const token = await useGeminiAccessToken();
  const genminiAxios = import_axios2.default.create({
    baseURL: MODAL_URL,
    headers: {
      ...formData.getHeaders(),
      Authorization: `Bearer ${token.access_token}`,
      [`x-google-api-key`]: token.access_token
    }
  });
  const result = await genminiAxios.request({
    method: `post`,
    url: `/google/vertex-ai/upload`,
    params: {
      bucket,
      prefix
    },
    data: formData
  });
  return result.data;
}
async function useGemini() {
  const token = await useGeminiAccessToken();
  return createDefaultGoogleGenaiClient(token.access_token);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  GoogleGenaiClient,
  uploadFileToGemini,
  useGemini,
  useGeminiAccessToken
});
