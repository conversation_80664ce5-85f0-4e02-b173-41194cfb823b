export interface User {
    id: string;
    name: string;
    email: string;
    createdAt: Date;
}
export interface Video {
    id: string;
    title: string;
    url: string;
    duration: number;
    userId: string;
    createdAt: Date;
}
export declare const formatDuration: (seconds: number) => string;
export declare const validateEmail: (email: string) => boolean;
export declare const API_ENDPOINTS: {
    readonly USERS: "/api/users";
    readonly VIDEOS: "/api/videos";
    readonly AUTH: "/api/auth";
};
export declare const VIDEO_FORMATS: readonly ["mp4", "webm", "avi", "mov"];
export type VideoFormat = (typeof VIDEO_FORMATS)[number];
//# sourceMappingURL=index.d.ts.map