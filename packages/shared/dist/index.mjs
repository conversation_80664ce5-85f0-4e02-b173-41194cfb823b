// src/index.ts
var formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor(seconds % 3600 / 60);
  const remainingSeconds = seconds % 60;
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  }
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};
var validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
var API_ENDPOINTS = {
  USERS: "/api/users",
  VIDEOS: "/api/videos",
  AUTH: "/api/auth"
};
var VIDEO_FORMATS = ["mp4", "webm", "avi", "mov"];
export {
  API_ENDPOINTS,
  VIDEO_FORMATS,
  formatDuration,
  validateEmail
};
