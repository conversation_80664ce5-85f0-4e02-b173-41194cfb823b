interface User {
    id: string;
    name: string;
    email: string;
    createdAt: Date;
}
interface Video {
    id: string;
    title: string;
    url: string;
    duration: number;
    userId: string;
    createdAt: Date;
}
declare const formatDuration: (seconds: number) => string;
declare const validateEmail: (email: string) => boolean;
declare const API_ENDPOINTS: {
    readonly USERS: "/api/users";
    readonly VIDEOS: "/api/videos";
    readonly AUTH: "/api/auth";
};
declare const VIDEO_FORMATS: readonly ["mp4", "webm", "avi", "mov"];
type VideoFormat = (typeof VIDEO_FORMATS)[number];

export { API_ENDPOINTS, type User, VIDEO_FORMATS, type Video, type VideoFormat, formatDuration, validateEmail };
