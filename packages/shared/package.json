{"name": "@mixvideo/shared", "version": "1.0.0", "description": "Shared utilities and types for MixVideo", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "devDependencies": {"typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}}