{"version": 3, "file": "video-uploader.js", "sourceRoot": "", "sources": ["../src/video-uploader.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AACtD,OAAO,EAA6B,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAExE,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACxC,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAoBhC;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAA8D;IAC9F,SAAS,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,cAAc;IAC3C,UAAU,EAAE,CAAC;IACb,OAAO,EAAE,MAAM,EAAE,YAAY;IAC7B,UAAU,EAAE,GAAG,EAAE,GAAE,CAAC;CACrB,CAAC;AA0BF;;GAEG;AACH,MAAM,OAAO,aAAa;IAGxB,YAAY,MAAoB;QAC9B,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,qBAAqB;YACxB,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAoB;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAElD,MAAM,QAAQ,GAAmB;YAC/B,IAAI,EAAE,kBAAkB;YACxB,QAAQ,EAAE,CAAC;YACX,WAAW,EAAE,SAAS,CAAC,IAAI;YAC3B,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,SAAS,CAAC,IAAI;SAC3B,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YAE5C,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC;YAC/B,QAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEjC,oBAAoB;YACpB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAEvD,QAAQ,CAAC,IAAI,GAAG,qBAAqB,CAAC;YACtC,QAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEjC,oBAAoB;YACpB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEhD,0BAA0B;YAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE1C,QAAQ,CAAC,IAAI,GAAG,kBAAkB,CAAC;YACnC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;YACxB,QAAQ,CAAC,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEjC,OAAO;gBACL,SAAS;gBACT,OAAO,EAAE,IAAI;gBACb,OAAO;gBACP,UAAU;gBACV,aAAa,EAAE,SAAS,CAAC,IAAI;gBAC7B,QAAQ,EAAE;oBACR,QAAQ;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;iBACnD;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE1C,OAAO;gBACL,SAAS;gBACT,OAAO,EAAE,KAAK;gBACd,UAAU;gBACV,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;gBAClC,QAAQ,EAAE;oBACR,QAAQ;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,KAAc;QACpC,OAAO,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,UAAuB;QACxC,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACjD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,0BAA0B;gBAC1B,MAAM,eAAe,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;oBACrB,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,QAAQ;oBACpD,QAAQ,EAAE,eAAe;oBACzB,WAAW,EAAE,SAAS,CAAC,IAAI;oBAC3B,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC;oBACnE,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;iBAC3D,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS;oBACT,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,CAAC;oBACb,aAAa,EAAE,CAAC;oBAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,SAAoB;QACtD,uBAAuB;QACvB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kBAAkB,CAC1B,mBAAmB,SAAS,CAAC,IAAI,EAAE,EACnC,gBAAgB,CACjB,CAAC;QACJ,CAAC;QAED,kBAAkB;QAClB,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,kBAAkB,CAC1B,kBAAkB,SAAS,CAAC,IAAI,EAAE,EAClC,YAAY,CACb,CAAC;QACJ,CAAC;QAED,iDAAiD;QACjD,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ;QAC3C,IAAI,SAAS,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,kBAAkB,CAC1B,mBAAmB,SAAS,CAAC,IAAI,gBAAgB,OAAO,SAAS,EACjE,gBAAgB,CACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,SAAoB;QAC9C,IAAI,CAAC;YACH,OAAO,MAAM,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kBAAkB,CAC1B,wBAAwB,SAAS,CAAC,IAAI,EAAE,EACxC,aAAa,EACb,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,UAAkB,EAClB,OAAe,EACf,QAAwB;QAExB,IAAI,SAAS,GAAiB,IAAI,CAAC;QAEnC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACnE,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,GAAG,kBAAkB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAEjC,MAAM,kBAAkB,CACtB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,OAAO,EACP,UAAU,CACX,CAAC;gBAEF,oBAAoB;gBACpB,OAAO;YAET,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBACrC,6CAA6C;oBAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,IAAI,kBAAkB,CAC1B,uBAAuB,IAAI,CAAC,MAAM,CAAC,UAAU,cAAc,SAAS,EAAE,OAAO,EAAE,EAC/E,eAAe,EACf,SAAS,CACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,SAAoB;QAC1C,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACrE,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,SAAS,IAAI,aAAa,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,SAAoB;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,UAAU,SAAS,IAAI,MAAM,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAc;QAC5C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,OAAuB;QAQzC,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;QAClC,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAChE,MAAM,aAAa,GAAG,UAAU,GAAG,iBAAiB,CAAC;QACrD,MAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAChF,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,iBAAiB,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhF,OAAO;YACL,UAAU;YACV,iBAAiB;YACjB,aAAa;YACb,kBAAkB;YAClB,iBAAiB;YACjB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAA6B;QACxC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,SAAoB,EACpB,MAAoB;IAEpB,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;IAC3C,OAAO,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,UAAuB,EACvB,MAAoB;IAEpB,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;IAC3C,OAAO,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AAC3C,CAAC"}