/**
 * Main VideoAnalyzer class - orchestrates all video analysis functionality
 */
import { FolderMatchConfig } from './folder-matcher';
import { ReportOptions } from './report-generator';
import { VideoFile, VideoScanOptions, VideoAnalysisResult, AnalysisMode, AnalysisOptions, AnalysisProgress, FolderMatchResult, VideoAnalyzerConfig } from './types';
/**
 * Main VideoAnalyzer class
 */
export declare class VideoAnalyzer {
    private config;
    private scanner;
    private uploader;
    private analysisEngine;
    private productAnalyzer;
    private folderMatcher;
    private reportGenerator;
    private frameAnalyzer;
    constructor(config?: VideoAnalyzerConfig);
    /**
     * Scan directory for video files
     */
    scanDirectory(directoryPath: string, options?: VideoScanOptions): Promise<VideoFile[]>;
    /**
     * Analyze a single video file
     */
    analyzeVideo(videoFile: VideoFile, mode: AnalysisMode, options?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult>;
    /**
     * Analyze multiple videos in a directory
     */
    analyzeDirectory(directoryPath: string, mode: AnalysisMode, scanOptions?: VideoScanOptions, analysisOptions?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult[]>;
    /**
     * Find matching folders for analysis results
     */
    findMatchingFolders(analysisResults: VideoAnalysisResult[], folderConfig: FolderMatchConfig): Promise<Record<string, FolderMatchResult[]>>;
    /**
     * Generate analysis report
     */
    generateReport(analysisResults: VideoAnalysisResult[], folderMatches: Record<string, FolderMatchResult[]> | undefined, reportOptions: ReportOptions): Promise<string>;
    /**
     * Complete workflow: scan, analyze, match folders, and generate report
     */
    analyzeDirectoryComplete(directoryPath: string, mode: AnalysisMode, options?: {
        scanOptions?: VideoScanOptions;
        analysisOptions?: AnalysisOptions;
        folderConfig?: FolderMatchConfig;
        reportOptions?: ReportOptions;
        onProgress?: (progress: AnalysisProgress) => void;
    }): Promise<{
        analysisResults: VideoAnalysisResult[];
        folderMatches: Record<string, FolderMatchResult[]>;
        reportPath?: string;
    }>;
    /**
     * Initialize uploader with configuration
     */
    private initializeUploader;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<VideoAnalyzerConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): VideoAnalyzerConfig;
    /**
     * Get analysis statistics
     */
    getAnalysisStatistics(results: VideoAnalysisResult[]): {
        totalVideos: number;
        totalProcessingTime: number;
        averageProcessingTime: number;
        totalScenes: number;
        totalObjects: number;
        averageQualityScore: number;
    };
}
/**
 * Convenience function to create VideoAnalyzer instance
 */
export declare function createVideoAnalyzer(config?: VideoAnalyzerConfig): VideoAnalyzer;
//# sourceMappingURL=video-analyzer.d.ts.map