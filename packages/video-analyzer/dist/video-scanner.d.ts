/**
 * Video file scanner and validation utilities
 */
import { VideoFile, VideoScanOptions } from './types';
/**
 * <PERSON><PERSON><PERSON> supported video file extensions
 */
export declare const DEFAULT_VIDEO_EXTENSIONS: string[];
/**
 * Default scan options
 */
export declare const DEFAULT_SCAN_OPTIONS: Required<VideoScanOptions>;
/**
 * Video file scanner class
 */
export declare class VideoScanner {
    private options;
    constructor(options?: VideoScanOptions);
    /**
     * Scan a directory for video files
     */
    scanDirectory(directoryPath: string): Promise<VideoFile[]>;
    /**
     * Recursively scan directory for video files
     */
    private scanDirectoryRecursive;
    /**
     * Check if a file is a video file based on extension
     */
    isVideoFile(filePath: string): Promise<boolean>;
    /**
     * Create VideoFile object from file path
     */
    createVideoFile(filePath: string): Promise<VideoFile>;
    /**
     * Validate video file against size constraints
     */
    isValidVideoFile(videoFile: VideoFile): boolean;
    /**
     * Check if directory exists and is accessible
     */
    isValidDirectory(dirPath: string): Promise<boolean>;
    /**
     * Get video file metadata using basic file system info
     */
    getBasicMetadata(videoFile: VideoFile): Promise<VideoFile>;
    /**
     * Validate multiple video files
     */
    validateVideoFiles(videoFiles: VideoFile[]): Promise<{
        valid: VideoFile[];
        invalid: Array<{
            file: VideoFile;
            reason: string;
        }>;
    }>;
    /**
     * Check if file exists
     */
    private fileExists;
    /**
     * Get scan statistics
     */
    getScanStatistics(directoryPath: string): Promise<{
        totalFiles: number;
        totalVideoFiles: number;
        totalSize: number;
        averageSize: number;
        formatDistribution: Record<string, number>;
    }>;
    /**
     * Update scan options
     */
    updateOptions(options: Partial<VideoScanOptions>): void;
    /**
     * Get current scan options
     */
    getOptions(): VideoScanOptions;
}
/**
 * Convenience function to scan directory with default options
 */
export declare function scanVideoDirectory(directoryPath: string, options?: VideoScanOptions): Promise<VideoFile[]>;
/**
 * Convenience function to get video scan statistics
 */
export declare function getVideoScanStatistics(directoryPath: string, options?: VideoScanOptions): Promise<{
    totalFiles: number;
    totalVideoFiles: number;
    totalSize: number;
    averageSize: number;
    formatDistribution: Record<string, number>;
}>;
//# sourceMappingURL=video-scanner.d.ts.map