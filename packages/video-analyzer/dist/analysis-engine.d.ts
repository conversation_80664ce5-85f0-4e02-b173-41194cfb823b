/**
 * Core video analysis engine using Gemini AI
 */
import { VideoFile, VideoAnalysisResult, AnalysisMode, AnalysisOptions, AnalysisProgress } from './types';
/**
 * Analysis prompts for different types of content analysis
 */
export declare const ANALYSIS_PROMPTS: {
    COMPREHENSIVE: string;
    PRODUCT_FOCUSED: string;
    SCENE_DETECTION: string;
    OBJECT_DETECTION: string;
};
/**
 * Video analysis engine class
 */
export declare class AnalysisEngine {
    private geminiClient;
    constructor();
    /**
     * Initialize Gemini client
     */
    private initializeGeminiClient;
    /**
     * Analyze video using Gemini AI
     */
    analyzeVideo(videoFile: VideoFile, gcsPath: string, mode: AnalysisMode, options?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult>;
    /**
     * Generate analysis prompts based on options
     */
    private generateAnalysisPrompts;
    /**
     * Perform comprehensive analysis using multiple prompts
     */
    private performComprehensiveAnalysis;
    /**
     * Run a single analysis with a specific prompt
     */
    private runSingleAnalysis;
    /**
     * Parse analysis response from Gemini
     */
    private parseAnalysisResponse;
    /**
     * Parse text response when JSON parsing fails
     */
    private parseTextResponse;
    /**
     * Merge multiple analysis results
     */
    private mergeAnalysisResults;
    /**
     * Build video metadata
     */
    private buildVideoMetadata;
    /**
     * Create default summary when none is provided
     */
    private createDefaultSummary;
    /**
     * Calculate quality metrics for analysis results
     */
    private calculateQualityMetrics;
}
/**
 * Convenience function to analyze a video
 */
export declare function analyzeVideoWithGemini(videoFile: VideoFile, gcsPath: string, mode: AnalysisMode, options?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult>;
//# sourceMappingURL=analysis-engine.d.ts.map