"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  ANALYSIS_PROMPTS: () => ANALYSIS_PROMPTS,
  AnalysisEngine: () => AnalysisEngine,
  DEFAULT_FOLDER_MATCH_CONFIG: () => DEFAULT_FOLDER_MATCH_CONFIG,
  DEFAULT_SCAN_OPTIONS: () => DEFAULT_SCAN_OPTIONS,
  DEFAULT_UPLOAD_CONFIG: () => DEFAULT_UPLOAD_CONFIG,
  DEFAULT_VIDEO_EXTENSIONS: () => DEFAULT_VIDEO_EXTENSIONS,
  FolderMatcher: () => FolderMatcher,
  FrameAnalyzer: () => FrameAnalyzer,
  PRODUCT_ANALYSIS_PROMPTS: () => PRODUCT_ANALYSIS_PROMPTS,
  ProductAnalyzer: () => ProductAnalyzer,
  ReportGenerator: () => ReportGenerator,
  VideoAnalyzer: () => VideoAnalyzer,
  VideoAnalyzerError: () => VideoAnalyzerError,
  VideoScanner: () => VideoScanner,
  VideoUploader: () => VideoUploader,
  analyzeProductInVideo: () => analyzeProductInVideo,
  analyzeVideoFrames: () => analyzeVideoFrames,
  analyzeVideoWithGemini: () => analyzeVideoWithGemini,
  createVideoAnalyzer: () => createVideoAnalyzer,
  findMatchingFoldersForVideo: () => findMatchingFoldersForVideo,
  generateAnalysisReport: () => generateAnalysisReport,
  getVideoScanStatistics: () => getVideoScanStatistics,
  scanVideoDirectory: () => scanVideoDirectory,
  uploadVideoToGemini: () => uploadVideoToGemini,
  uploadVideosToGemini: () => uploadVideosToGemini
});
module.exports = __toCommonJS(index_exports);

// src/types.ts
var VideoAnalyzerError = class _VideoAnalyzerError extends Error {
  constructor(message, code, details, file, stage) {
    super(message);
    this.name = "VideoAnalyzerError";
    this.code = code;
    this.details = details;
    this.file = file;
    this.stage = stage;
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, _VideoAnalyzerError);
    }
  }
};

// src/video-scanner.ts
var fs = __toESM(require("fs"));
var path = __toESM(require("path"));
var import_util = require("util");
var stat2 = (0, import_util.promisify)(fs.stat);
var readdir2 = (0, import_util.promisify)(fs.readdir);
var DEFAULT_VIDEO_EXTENSIONS = [
  ".mp4",
  ".mov",
  ".avi",
  ".mkv",
  ".wmv",
  ".flv",
  ".webm",
  ".m4v",
  ".3gp",
  ".ogv"
];
var DEFAULT_SCAN_OPTIONS = {
  extensions: DEFAULT_VIDEO_EXTENSIONS,
  recursive: true,
  maxFileSize: 500 * 1024 * 1024,
  // 500MB
  minFileSize: 1024,
  // 1KB
  onProgress: () => {
  }
};
var VideoScanner = class {
  constructor(options = {}) {
    this.options = { ...DEFAULT_SCAN_OPTIONS, ...options };
  }
  /**
   * Scan a directory for video files
   */
  async scanDirectory(directoryPath) {
    if (!await this.isValidDirectory(directoryPath)) {
      throw new VideoAnalyzerError(
        `Invalid directory path: ${directoryPath}`,
        "INVALID_DIRECTORY"
      );
    }
    const videoFiles = [];
    const progress = {
      step: "Initializing scan",
      progress: 0,
      filesFound: 0,
      directoriesScanned: 0
    };
    this.options.onProgress(progress);
    try {
      await this.scanDirectoryRecursive(directoryPath, videoFiles, progress);
      progress.step = "Scan completed";
      progress.progress = 100;
      this.options.onProgress(progress);
      return videoFiles;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Failed to scan directory: ${errorMessage}`,
        "SCAN_FAILED",
        error
      );
    }
  }
  /**
   * Recursively scan directory for video files
   */
  async scanDirectoryRecursive(dirPath, videoFiles, progress) {
    progress.directoriesScanned++;
    progress.step = `Scanning: ${path.basename(dirPath)}`;
    this.options.onProgress(progress);
    const entries = await readdir2(dirPath, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      if (entry.isDirectory() && this.options.recursive) {
        await this.scanDirectoryRecursive(fullPath, videoFiles, progress);
      } else if (entry.isFile()) {
        progress.currentFile = entry.name;
        this.options.onProgress(progress);
        if (this.isVideoFile(fullPath)) {
          try {
            const videoFile = await this.createVideoFile(fullPath);
            if (this.isValidVideoFile(videoFile)) {
              videoFiles.push(videoFile);
              progress.filesFound++;
              progress.progress = Math.min(95, progress.filesFound / 10 * 100);
              this.options.onProgress(progress);
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.warn(`Failed to process video file ${fullPath}:`, errorMessage);
          }
        }
      }
    }
  }
  /**
   * Check if a file is a video file based on extension (synchronous)
   */
  isVideoFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return this.options.extensions.includes(ext);
  }
  /**
   * Check if a file is a video file based on extension (async version for compatibility)
   */
  async isVideoFileAsync(filePath) {
    return this.isVideoFile(filePath);
  }
  /**
   * Create VideoFile object from file path
   */
  async createVideoFile(filePath) {
    const stats = await stat2(filePath);
    const parsedPath = path.parse(filePath);
    return {
      path: filePath,
      name: parsedPath.name,
      size: stats.size,
      format: parsedPath.ext.toLowerCase().substring(1),
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }
  /**
   * Validate video file against size constraints
   */
  isValidVideoFile(videoFile) {
    return videoFile.size >= this.options.minFileSize && videoFile.size <= this.options.maxFileSize;
  }
  /**
   * Check if directory exists and is accessible
   */
  async isValidDirectory(dirPath) {
    try {
      const stats = await stat2(dirPath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }
  /**
   * Get video file metadata using basic file system info
   */
  async getBasicMetadata(videoFile) {
    try {
      const stats = await stat2(videoFile.path);
      return {
        ...videoFile,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Failed to get metadata for ${videoFile.path}: ${errorMessage}`,
        "METADATA_FAILED",
        error
      );
    }
  }
  /**
   * Validate multiple video files
   */
  async validateVideoFiles(videoFiles) {
    const valid = [];
    const invalid = [];
    for (const videoFile of videoFiles) {
      try {
        if (!await this.fileExists(videoFile.path)) {
          invalid.push({ file: videoFile, reason: "File not found" });
          continue;
        }
        if (!this.isValidVideoFile(videoFile)) {
          invalid.push({
            file: videoFile,
            reason: `File size ${videoFile.size} bytes is outside allowed range`
          });
          continue;
        }
        if (!this.isVideoFile(videoFile.path)) {
          invalid.push({ file: videoFile, reason: "Not a supported video format" });
          continue;
        }
        valid.push(videoFile);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        invalid.push({ file: videoFile, reason: errorMessage });
      }
    }
    return { valid, invalid };
  }
  /**
   * Check if file exists
   */
  async fileExists(filePath) {
    try {
      await stat2(filePath);
      return true;
    } catch {
      return false;
    }
  }
  /**
   * Get scan statistics
   */
  async getScanStatistics(directoryPath) {
    const videoFiles = await this.scanDirectory(directoryPath);
    const totalFiles = videoFiles.length;
    const totalSize = videoFiles.reduce((sum, file) => sum + file.size, 0);
    const averageSize = totalFiles > 0 ? totalSize / totalFiles : 0;
    const formatDistribution = {};
    videoFiles.forEach((file) => {
      const format = file.format || "unknown";
      formatDistribution[format] = (formatDistribution[format] || 0) + 1;
    });
    return {
      totalFiles,
      totalVideoFiles: totalFiles,
      totalSize,
      averageSize,
      formatDistribution
    };
  }
  /**
   * Update scan options
   */
  updateOptions(options) {
    this.options = { ...this.options, ...options };
  }
  /**
   * Get current scan options
   */
  getOptions() {
    return { ...this.options };
  }
};
async function scanVideoDirectory(directoryPath, options) {
  const scanner = new VideoScanner(options);
  return scanner.scanDirectory(directoryPath);
}
async function getVideoScanStatistics(directoryPath, options) {
  const scanner = new VideoScanner(options);
  return scanner.getScanStatistics(directoryPath);
}

// src/video-uploader.ts
var fs2 = __toESM(require("fs"));
var path2 = __toESM(require("path"));
var import_util2 = require("util");

// ../gemini/dist/index.mjs
var import_axios = __toESM(require("axios"), 1);
var import_axios2 = __toESM(require("axios"), 1);
var import_form_data = require("form-data");
var GoogleGenaiClient = class {
  constructor(config) {
    __publicField(this, "config");
    this.config = config;
  }
  /**
   * 计算属性：通过 Cloudflare Gateway 调用的 URL
   */
  get gatewayUrl() {
    const randomRegion = this.config.regions[Math.floor(Math.random() * this.config.regions.length)];
    return `https://gateway.ai.cloudflare.com/v1/${this.config.cloudflareProjectId}/${this.config.cloudflareGatewayId}/google-vertex-ai/v1/projects/${this.config.googleProjectId}/locations/${randomRegion}/publishers/google/models`;
  }
  /**
   * 生成内容
   * @param modelId 模型 ID，如 'gemini-2.5-flash'
   * @param contents 内容数组
   * @param config 生成配置
   * @param timeout 超时时间（秒）
   * @returns 生成结果和状态码
   */
  async generateContent(contents, modelId = "gemini-2.5-flash", config, timeout = 30) {
    try {
      const requestBody = {
        contents,
        ...config && { generationConfig: config }
      };
      const jsonBody = JSON.stringify(requestBody, (key, value) => {
        if (value === null || value === void 0) {
          return void 0;
        }
        return value;
      }, 2);
      const url = `${this.gatewayUrl}/${modelId}:generateContent`;
      const response = await import_axios2.default.post(
        url,
        jsonBody,
        {
          timeout: timeout * 1e3,
          // 转换为毫秒
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.config.accessToken}`
          }
        }
      );
      if (response.status !== 200) {
        return {
          response: null,
          statusCode: response.status
        };
      }
      return {
        response: response.data,
        statusCode: response.status
      };
    } catch (error) {
      if (error.response) {
        return {
          response: null,
          statusCode: error.response.status
        };
      } else if (error.request) {
        return {
          response: null,
          statusCode: 0
        };
      } else {
        return {
          response: null,
          statusCode: -1
        };
      }
    }
  }
  /**
   * 简化的文本生成方法
   * @param modelId 模型 ID
   * @param prompt 文本提示
   * @param config 生成配置
   * @returns 生成的文本内容
   */
  async generateText(prompt, modelId = "gemini-2.5-flash", config) {
    const contents = [
      {
        role: "user",
        parts: [{ text: prompt }]
      }
    ];
    const result = await this.generateContent(contents, modelId, config);
    if (result.statusCode === 200 && result.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      return result.response.candidates[0].content.parts[0].text;
    }
    return null;
  }
  /**
   * 获取配置信息
   */
  getConfig() {
    return { ...this.config };
  }
  /**
   * 更新访问令牌
   */
  updateAccessToken(accessToken) {
    this.config.accessToken = accessToken;
  }
};
function createDefaultGoogleGenaiClient(accessToken) {
  return new GoogleGenaiClient({
    cloudflareProjectId: "67720b647ff2b55cf37ba3ef9e677083",
    cloudflareGatewayId: "bowong-dev",
    googleProjectId: "gen-lang-client-0413414134",
    regions: ["us-central1"],
    // 使用稳定的区域
    accessToken
  });
}
var MODAL_URL = `https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run`;
function useGeminiAxios() {
  return import_axios.default.create({
    baseURL: MODAL_URL,
    headers: {
      Authorization: `Bearer bowong7777`
    }
  });
}
async function useGeminiAccessToken() {
  const geminiAxios = useGeminiAxios();
  const token = await geminiAxios.request({
    method: `get`,
    url: `/google/access-token`
  }).then((res) => res.data);
  return token;
}
async function uploadFileToGemini(bucket, prefix, formData) {
  const token = await useGeminiAccessToken();
  const genminiAxios = import_axios.default.create({
    baseURL: MODAL_URL,
    headers: {
      ...formData.getHeaders(),
      Authorization: `Bearer ${token.access_token}`,
      [`x-google-api-key`]: token.access_token
    }
  });
  const result = await genminiAxios.request({
    method: `post`,
    url: `/google/vertex-ai/upload`,
    params: {
      bucket,
      prefix
    },
    data: formData
  });
  return result.data;
}
async function useGemini() {
  const token = await useGeminiAccessToken();
  return createDefaultGoogleGenaiClient(token.access_token);
}

// src/video-uploader.ts
var import_form_data2 = __toESM(require("form-data"));
var import_path = require("path");
var import_mime_types = __toESM(require("mime-types"));
var readFile2 = (0, import_util2.promisify)(fs2.readFile);
var writeFile2 = (0, import_util2.promisify)(fs2.writeFile);
var stat4 = (0, import_util2.promisify)(fs2.stat);
var access2 = (0, import_util2.promisify)(fs2.access);
var DEFAULT_UPLOAD_CONFIG = {
  chunkSize: 10 * 1024 * 1024,
  // 10MB chunks
  maxRetries: 3,
  timeout: 3e5,
  // 5 minutes
  onProgress: () => {
  },
  enableCache: true,
  cacheDir: ".video-upload-cache",
  cacheExpiry: 24 * 60 * 60 * 1e3
  // 24 hours
};
var VideoUploader = class {
  constructor(config) {
    this.config = {
      ...DEFAULT_UPLOAD_CONFIG,
      ...config
    };
  }
  /**
   * Upload a single video file to Gemini
   */
  async uploadVideo(videoFile) {
    const startTime = Date.now();
    const uploadId = this.generateUploadId(videoFile);
    const progress = {
      step: "Preparing upload",
      progress: 0,
      currentFile: videoFile.name,
      bytesUploaded: 0,
      totalBytes: videoFile.size
    };
    this.config.onProgress(progress);
    try {
      progress.step = "Checking cache";
      progress.progress = 5;
      this.config.onProgress(progress);
      const cachedResult = await this.checkLocalCache(videoFile);
      if (cachedResult) {
        const uploadTime2 = Date.now() - startTime;
        progress.step = "Using cached result";
        progress.progress = 100;
        progress.bytesUploaded = videoFile.size;
        this.config.onProgress(progress);
        return {
          videoFile,
          success: true,
          gcsPath: cachedResult.urn,
          uploadTime: uploadTime2,
          bytesUploaded: videoFile.size,
          metadata: {
            uploadId,
            timestamp: /* @__PURE__ */ new Date(),
            checksum: await this.calculateChecksum(await this.readVideoFile(videoFile))
          }
        };
      }
      await this.validateFileForUpload(videoFile);
      progress.step = "Reading file";
      progress.progress = 10;
      this.config.onProgress(progress);
      const fileBuffer = await this.readVideoFile(videoFile);
      const mimeType = this.getMimeType(videoFile.path);
      progress.step = "Uploading to Gemini";
      progress.progress = 20;
      this.config.onProgress(progress);
      const gcsPath = this.generateGcsPath(videoFile);
      const uploadResult = await this.uploadWithRetry(fileBuffer, videoFile, progress, mimeType, gcsPath);
      const uploadTime = Date.now() - startTime;
      progress.step = "Upload completed";
      progress.progress = 100;
      progress.bytesUploaded = videoFile.size;
      this.config.onProgress(progress);
      return {
        videoFile,
        success: true,
        gcsPath: uploadResult.urn,
        uploadTime,
        bytesUploaded: videoFile.size,
        metadata: {
          uploadId,
          timestamp: /* @__PURE__ */ new Date(),
          checksum: await this.calculateChecksum(fileBuffer)
        }
      };
    } catch (error) {
      const uploadTime = Date.now() - startTime;
      return {
        videoFile,
        success: false,
        uploadTime,
        bytesUploaded: progress.bytesUploaded,
        error: this.getErrorMessage(error),
        metadata: {
          uploadId,
          timestamp: /* @__PURE__ */ new Date()
        }
      };
    }
  }
  getErrorMessage(error) {
    return error instanceof Error ? error.message : String(error);
  }
  /**
   * Upload multiple video files
   */
  async uploadVideos(videoFiles) {
    const results = [];
    for (let i = 0; i < videoFiles.length; i++) {
      const videoFile = videoFiles[i];
      try {
        const result = await this.uploadVideo(videoFile);
        results.push(result);
        const overallProgress = (i + 1) / videoFiles.length * 100;
        this.config.onProgress({
          step: `Uploaded ${i + 1}/${videoFiles.length} files`,
          progress: overallProgress,
          currentFile: videoFile.name,
          bytesUploaded: results.reduce((sum, r) => sum + r.bytesUploaded, 0),
          totalBytes: videoFiles.reduce((sum, f) => sum + f.size, 0)
        });
      } catch (error) {
        results.push({
          videoFile,
          success: false,
          uploadTime: 0,
          bytesUploaded: 0,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
    return results;
  }
  /**
   * Validate file before upload
   */
  async validateFileForUpload(videoFile) {
    try {
      await stat4(videoFile.path);
    } catch (error) {
      throw new VideoAnalyzerError(
        `File not found: ${videoFile.path}`,
        "FILE_NOT_FOUND"
      );
    }
    if (videoFile.size === 0) {
      throw new VideoAnalyzerError(
        `File is empty: ${videoFile.path}`,
        "EMPTY_FILE"
      );
    }
    const maxSize = 500 * 1024 * 1024;
    if (videoFile.size > maxSize) {
      throw new VideoAnalyzerError(
        `File too large: ${videoFile.size} bytes (max: ${maxSize} bytes)`,
        "FILE_TOO_LARGE"
      );
    }
  }
  /**
   * Read video file into buffer
   */
  async readVideoFile(videoFile) {
    try {
      return await readFile2(videoFile.path);
    } catch (error) {
      throw new VideoAnalyzerError(
        `Failed to read file: ${videoFile.path}`,
        "READ_FAILED",
        error
      );
    }
  }
  getMimeType(fileName) {
    const extension = (0, import_path.extname)(fileName).slice(1);
    switch (extension) {
      case "mp4":
        return `video/mp4`;
      default:
        return import_mime_types.default.lookup(extension) || "application/octet-stream";
    }
  }
  /**
   * 将上传结果保存到本地缓存
   */
  async saveToLocalDb(videoFile, result) {
    if (!this.config.enableCache) {
      return;
    }
    try {
      await this.ensureCacheDir();
      const fileBuffer = await readFile2(videoFile.path);
      const checksum = await this.calculateChecksum(fileBuffer);
      const cacheEntry = {
        videoFile,
        result,
        timestamp: Date.now(),
        checksum
      };
      const cacheKey = this.generateCacheKey(videoFile.path);
      const cacheFilePath = path2.join(this.config.cacheDir, `${cacheKey}.json`);
      await writeFile2(cacheFilePath, JSON.stringify(cacheEntry, null, 2), "utf8");
      console.log(`\u2705 \u7F13\u5B58\u5DF2\u4FDD\u5B58: ${videoFile.name} -> ${cacheFilePath}`);
    } catch (error) {
      console.warn(`\u26A0\uFE0F \u4FDD\u5B58\u7F13\u5B58\u5931\u8D25: ${videoFile.name}`, error);
    }
  }
  /**
   * 从本地缓存检查是否已上传
   */
  async checkLocalCache(videoFile) {
    if (!this.config.enableCache) {
      return null;
    }
    try {
      const cacheKey = this.generateCacheKey(videoFile.path);
      const cacheFilePath = path2.join(this.config.cacheDir, `${cacheKey}.json`);
      await access2(cacheFilePath, fs2.constants.F_OK);
      const cacheContent = await readFile2(cacheFilePath, "utf8");
      const cacheEntry = JSON.parse(cacheContent);
      const now = Date.now();
      if (now - cacheEntry.timestamp > this.config.cacheExpiry) {
        console.log(`\u23F0 \u7F13\u5B58\u5DF2\u8FC7\u671F: ${videoFile.name}`);
        await fs2.promises.unlink(cacheFilePath).catch(() => {
        });
        return null;
      }
      const fileBuffer = await readFile2(videoFile.path);
      const currentChecksum = await this.calculateChecksum(fileBuffer);
      if (currentChecksum !== cacheEntry.checksum) {
        console.log(`\u{1F504} \u6587\u4EF6\u5DF2\u53D8\u66F4: ${videoFile.name}`);
        await fs2.promises.unlink(cacheFilePath).catch(() => {
        });
        return null;
      }
      console.log(`\u{1F3AF} \u4F7F\u7528\u7F13\u5B58\u7ED3\u679C: ${videoFile.name}`);
      return cacheEntry.result;
    } catch (error) {
      return null;
    }
  }
  /**
   * 确保缓存目录存在
   */
  async ensureCacheDir() {
    try {
      await access2(this.config.cacheDir, fs2.constants.F_OK);
    } catch {
      await fs2.promises.mkdir(this.config.cacheDir, { recursive: true });
      console.log(`\u{1F4C1} \u521B\u5EFA\u7F13\u5B58\u76EE\u5F55: ${this.config.cacheDir}`);
    }
  }
  /**
   * 生成缓存键
   */
  generateCacheKey(filePath) {
    const crypto = require("crypto");
    return crypto.createHash("md5").update(filePath).digest("hex");
  }
  /**
   * 清理过期的缓存文件
   */
  async cleanExpiredCache() {
    if (!this.config.enableCache) {
      return;
    }
    try {
      await this.ensureCacheDir();
      const files = await fs2.promises.readdir(this.config.cacheDir);
      const now = Date.now();
      let cleanedCount = 0;
      for (const file of files) {
        if (!file.endsWith(".json")) continue;
        const filePath = path2.join(this.config.cacheDir, file);
        try {
          const content = await readFile2(filePath, "utf8");
          const cacheEntry = JSON.parse(content);
          if (now - cacheEntry.timestamp > this.config.cacheExpiry) {
            await fs2.promises.unlink(filePath);
            cleanedCount++;
          }
        } catch (error) {
          await fs2.promises.unlink(filePath).catch(() => {
          });
          cleanedCount++;
        }
      }
      if (cleanedCount > 0) {
        console.log(`\u{1F9F9} \u6E05\u7406\u4E86 ${cleanedCount} \u4E2A\u8FC7\u671F\u7F13\u5B58\u6587\u4EF6`);
      }
    } catch (error) {
      console.warn("\u6E05\u7406\u7F13\u5B58\u5931\u8D25:", error);
    }
  }
  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    if (!this.config.enableCache) {
      return { totalFiles: 0, totalSize: 0, oldestEntry: null, newestEntry: null };
    }
    try {
      await this.ensureCacheDir();
      const files = await fs2.promises.readdir(this.config.cacheDir);
      let totalFiles = 0;
      let totalSize = 0;
      let oldestTimestamp = Infinity;
      let newestTimestamp = 0;
      for (const file of files) {
        if (!file.endsWith(".json")) continue;
        const filePath = path2.join(this.config.cacheDir, file);
        try {
          const stats = await stat4(filePath);
          const content = await readFile2(filePath, "utf8");
          const cacheEntry = JSON.parse(content);
          totalFiles++;
          totalSize += stats.size;
          oldestTimestamp = Math.min(oldestTimestamp, cacheEntry.timestamp);
          newestTimestamp = Math.max(newestTimestamp, cacheEntry.timestamp);
        } catch (error) {
        }
      }
      return {
        totalFiles,
        totalSize,
        oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),
        newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)
      };
    } catch (error) {
      return { totalFiles: 0, totalSize: 0, oldestEntry: null, newestEntry: null };
    }
  }
  /**
   * Upload with retry logic
   */
  async uploadWithRetry(fileBuffer, videoFile, progress, mimeType, gcsPath) {
    let lastError = null;
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        progress.step = `Upload attempt ${attempt}/${this.config.maxRetries}`;
        this.config.onProgress(progress);
        const formData = new import_form_data2.default();
        formData.append("file", fileBuffer, {
          filename: gcsPath,
          contentType: mimeType
        });
        const result = await uploadFileToGemini(
          this.config.bucketName,
          this.config.filePrefix,
          formData
        );
        await this.saveToLocalDb(videoFile, result);
        return result;
      } catch (error) {
        console.log(`Upload attempt ${attempt} failed:`, error);
        lastError = error instanceof Error ? error : new Error(String(error));
        if (attempt < this.config.maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1e3;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
    throw new VideoAnalyzerError(
      `Upload failed after ${this.config.maxRetries} attempts: ${lastError?.message}`,
      "UPLOAD_FAILED",
      lastError
    );
  }
  /**
   * Generate GCS path for video file
   */
  generateGcsPath(videoFile) {
    const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-");
    const sanitizedName = videoFile.name.replace(/[^a-zA-Z0-9.-]/g, "_");
    return `${this.config.filePrefix}${timestamp}_${sanitizedName}.${videoFile.format}`;
  }
  /**
   * Generate unique upload ID
   */
  generateUploadId(videoFile) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const sanitizedName = videoFile.name.replace(/[^a-zA-Z0-9]/g, "_");
    return `upload_${timestamp}_${sanitizedName}_${random}`;
  }
  /**
   * Calculate file checksum for verification
   */
  async calculateChecksum(buffer) {
    const crypto = await import("crypto");
    return crypto.createHash("md5").update(buffer).digest("hex");
  }
  /**
   * Get upload statistics
   */
  getUploadStatistics(results) {
    const totalFiles = results.length;
    const successfulUploads = results.filter((r) => r.success).length;
    const failedUploads = totalFiles - successfulUploads;
    const totalBytesUploaded = results.reduce((sum, r) => sum + r.bytesUploaded, 0);
    const totalUploadTime = results.reduce((sum, r) => sum + r.uploadTime, 0);
    const averageUploadTime = totalFiles > 0 ? totalUploadTime / totalFiles : 0;
    const successRate = totalFiles > 0 ? successfulUploads / totalFiles * 100 : 0;
    return {
      totalFiles,
      successfulUploads,
      failedUploads,
      totalBytesUploaded,
      averageUploadTime,
      successRate
    };
  }
  /**
   * Update upload configuration
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }
  /**
   * Get current upload configuration
   */
  getConfig() {
    return { ...this.config };
  }
};
async function uploadVideoToGemini(videoFile, config) {
  const uploader = new VideoUploader(config);
  return uploader.uploadVideo(videoFile);
}
async function uploadVideosToGemini(videoFiles, config) {
  const uploader = new VideoUploader(config);
  return uploader.uploadVideos(videoFiles);
}

// src/analysis-engine.ts
var ANALYSIS_PROMPTS = {
  COMPREHENSIVE: `\u8BF7\u5BF9\u8FD9\u4E2A\u89C6\u9891\u8FDB\u884C\u5168\u9762\u5206\u6790\uFF0C\u5305\u62EC\uFF1A
1. \u573A\u666F\u68C0\u6D4B\uFF1A\u8BC6\u522B\u89C6\u9891\u4E2D\u7684\u4E0D\u540C\u573A\u666F\uFF0C\u5305\u62EC\u5F00\u59CB\u65F6\u95F4\u3001\u7ED3\u675F\u65F6\u95F4\u548C\u573A\u666F\u63CF\u8FF0
2. \u7269\u4F53\u8BC6\u522B\uFF1A\u8BC6\u522B\u89C6\u9891\u4E2D\u51FA\u73B0\u7684\u4E3B\u8981\u7269\u4F53\u3001\u4EBA\u7269\u548C\u5143\u7D20
3. \u5185\u5BB9\u603B\u7ED3\uFF1A\u63D0\u4F9B\u89C6\u9891\u7684\u6574\u4F53\u63CF\u8FF0\u3001\u5173\u952E\u4EAE\u70B9\u548C\u4E3B\u8981\u4E3B\u9898
4. \u60C5\u611F\u57FA\u8C03\uFF1A\u5206\u6790\u89C6\u9891\u7684\u60C5\u611F\u6C1B\u56F4\u548C\u98CE\u683C
5. \u5173\u952E\u8BCD\u63D0\u53D6\uFF1A\u63D0\u53D6\u6700\u76F8\u5173\u7684\u5173\u952E\u8BCD

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u7ED3\u679C\uFF0C\u5305\u542Bscenes\u3001objects\u3001summary\u7B49\u5B57\u6BB5\u3002`,
  PRODUCT_FOCUSED: `\u8BF7\u4E13\u95E8\u5206\u6790\u8FD9\u4E2A\u89C6\u9891\u4E2D\u7684\u4EA7\u54C1\u76F8\u5173\u5185\u5BB9\uFF1A
1. \u4EA7\u54C1\u5916\u89C2\uFF1A\u989C\u8272\u3001\u5F62\u72B6\u3001\u5C3A\u5BF8\u3001\u98CE\u683C
2. \u6750\u8D28\u5206\u6790\uFF1A\u8BC6\u522B\u4EA7\u54C1\u4F7F\u7528\u7684\u6750\u6599
3. \u529F\u80FD\u7279\u5F81\uFF1A\u4EA7\u54C1\u5C55\u793A\u7684\u529F\u80FD\u548C\u7279\u6027
4. \u4F7F\u7528\u573A\u666F\uFF1A\u4EA7\u54C1\u7684\u4F7F\u7528\u73AF\u5883\u548C\u573A\u666F
5. \u76EE\u6807\u53D7\u4F17\uFF1A\u5206\u6790\u4EA7\u54C1\u7684\u76EE\u6807\u7528\u6237\u7FA4\u4F53
6. \u54C1\u724C\u5143\u7D20\uFF1A\u8BC6\u522B\u54C1\u724C\u6807\u8BC6\u3001logo\u7B49\u5143\u7D20

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u8BE6\u7EC6\u7684\u4EA7\u54C1\u5206\u6790\u7ED3\u679C\u3002`,
  SCENE_DETECTION: `\u8BF7\u8BE6\u7EC6\u5206\u6790\u89C6\u9891\u4E2D\u7684\u573A\u666F\u53D8\u5316\uFF1A
1. \u8BC6\u522B\u6BCF\u4E2A\u72EC\u7ACB\u7684\u573A\u666F
2. \u6807\u8BB0\u573A\u666F\u7684\u5F00\u59CB\u548C\u7ED3\u675F\u65F6\u95F4
3. \u63CF\u8FF0\u6BCF\u4E2A\u573A\u666F\u7684\u5185\u5BB9\u548C\u7279\u5F81
4. \u8BC4\u4F30\u573A\u666F\u8F6C\u6362\u7684\u6D41\u7545\u6027
5. \u8BC6\u522B\u5173\u952E\u5E27\u65F6\u95F4\u70B9

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u573A\u666F\u5206\u6790\u7ED3\u679C\u3002`,
  OBJECT_DETECTION: `\u8BF7\u8BC6\u522B\u548C\u5206\u6790\u89C6\u9891\u4E2D\u7684\u6240\u6709\u91CD\u8981\u7269\u4F53\uFF1A
1. \u7269\u4F53\u540D\u79F0\u548C\u7C7B\u522B
2. \u7269\u4F53\u5728\u89C6\u9891\u4E2D\u51FA\u73B0\u7684\u65F6\u95F4\u8303\u56F4
3. \u7269\u4F53\u7684\u91CD\u8981\u6027\u548C\u76F8\u5173\u6027\u8BC4\u5206
4. \u7269\u4F53\u4E4B\u95F4\u7684\u5173\u7CFB\u548C\u4EA4\u4E92
5. \u7269\u4F53\u7684\u5C5E\u6027\u548C\u7279\u5F81

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u7269\u4F53\u68C0\u6D4B\u7ED3\u679C\u3002`
};
var AnalysisEngine = class {
  constructor() {
    this.geminiClient = null;
  }
  /**
   * Initialize Gemini client
   */
  async initializeGeminiClient() {
    if (!this.geminiClient) {
      this.geminiClient = await useGemini();
    }
  }
  /**
   * Analyze video using Gemini AI
   */
  async analyzeVideo(videoFile, gcsPath, mode, options = {}, onProgress) {
    const startTime = Date.now();
    const progress = {
      step: "Initializing analysis",
      progress: 0,
      currentFile: videoFile.name,
      stage: "processing"
    };
    onProgress?.(progress);
    try {
      await this.initializeGeminiClient();
      progress.step = "Preparing analysis prompts";
      progress.progress = 10;
      onProgress?.(progress);
      const prompts = this.generateAnalysisPrompts(options);
      progress.step = "Analyzing video content";
      progress.progress = 20;
      progress.stage = "analysis";
      onProgress?.(progress);
      const analysisResults = await this.performComprehensiveAnalysis(
        gcsPath,
        prompts,
        options,
        onProgress
      );
      progress.step = "Processing analysis results";
      progress.progress = 90;
      onProgress?.(progress);
      const result = {
        metadata: await this.buildVideoMetadata(videoFile),
        analysisMode: mode,
        scenes: analysisResults.scenes || [],
        objects: analysisResults.objects || [],
        productFeatures: analysisResults.productFeatures,
        summary: analysisResults.summary || this.createDefaultSummary(),
        frameAnalysis: analysisResults.frameAnalysis,
        analyzedAt: /* @__PURE__ */ new Date(),
        processingTime: Date.now() - startTime,
        qualityMetrics: this.calculateQualityMetrics(analysisResults)
      };
      progress.step = "Analysis completed";
      progress.progress = 100;
      progress.stage = "complete";
      onProgress?.(progress);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Analysis failed for ${videoFile.name}: ${errorMessage}`,
        "ANALYSIS_FAILED",
        error
      );
    }
  }
  /**
   * Generate analysis prompts based on options
   */
  generateAnalysisPrompts(options) {
    const prompts = [];
    if (options.enableSceneDetection) {
      prompts.push(ANALYSIS_PROMPTS.SCENE_DETECTION);
    }
    if (options.enableObjectDetection) {
      prompts.push(ANALYSIS_PROMPTS.OBJECT_DETECTION);
    }
    if (options.enableProductAnalysis) {
      prompts.push(ANALYSIS_PROMPTS.PRODUCT_FOCUSED);
    }
    if (options.enableSummarization || prompts.length === 0) {
      prompts.push(ANALYSIS_PROMPTS.COMPREHENSIVE);
    }
    if (options.customPrompts) {
      prompts.push(...options.customPrompts);
    }
    return prompts;
  }
  /**
   * Perform comprehensive analysis using multiple prompts
   */
  async performComprehensiveAnalysis(gcsPath, prompts, options, onProgress) {
    const results = {
      scenes: [],
      objects: [],
      summary: null,
      productFeatures: null
    };
    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i];
      const progressPercent = 20 + (i + 1) / prompts.length * 60;
      onProgress?.({
        step: `Running analysis ${i + 1}/${prompts.length}`,
        progress: progressPercent,
        stage: "analysis"
      });
      try {
        const analysisResult = await this.runSingleAnalysis(gcsPath, prompt, options);
        this.mergeAnalysisResults(results, analysisResult);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Analysis prompt ${i + 1} failed:`, errorMessage);
      }
    }
    return results;
  }
  /**
   * Run a single analysis with a specific prompt
   */
  async runSingleAnalysis(gcsPath, prompt, options) {
    try {
      const contents = [
        {
          role: "user",
          parts: [
            {
              text: prompt
            },
            {
              fileData: {
                mimeType: "video/mp4",
                // Adjust based on actual video format
                fileUri: gcsPath
              }
            }
          ]
        }
      ];
      const response = await this.geminiClient.generateContent(
        contents,
        "gemini-2.5-flash",
        {
          temperature: 0.3,
          maxOutputTokens: 4096,
          topP: 0.8
        }
      );
      if (response.statusCode === 200 && response.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
        const responseText = response.response.candidates[0].content.parts[0].text;
        console.log(responseText);
        return this.parseAnalysisResponse(responseText);
      }
      throw new Error("Invalid response from Gemini API");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Gemini analysis failed: ${errorMessage}`,
        "GEMINI_ANALYSIS_FAILED",
        error
      );
    }
  }
  /**
   * Parse analysis response from Gemini
   */
  parseAnalysisResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return this.parseTextResponse(responseText);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.warn("Failed to parse JSON response, using text parsing:", errorMessage);
      return this.parseTextResponse(responseText);
    }
  }
  /**
   * Parse text response when JSON parsing fails
   */
  parseTextResponse(text) {
    const result = {
      scenes: [],
      objects: [],
      summary: {
        description: text.substring(0, 500),
        highlights: [],
        topics: [],
        keywords: []
      }
    };
    const sceneMatches = text.match(/场景\s*\d+[：:](.*?)(?=场景\s*\d+|$)/g);
    if (sceneMatches) {
      result.scenes = sceneMatches.map((match, index) => ({
        startTime: index * 10,
        // Estimate timing
        endTime: (index + 1) * 10,
        duration: 10,
        description: match.replace(/场景\s*\d+[：:]/, "").trim(),
        confidence: 0.7
      }));
    }
    const objectMatches = text.match(/(?:物体|对象|元素)[：:]([^。\n]+)/g);
    if (objectMatches) {
      result.objects = objectMatches.map((match) => ({
        name: match.replace(/(?:物体|对象|元素)[：:]/, "").trim(),
        category: "general",
        confidence: 0.6
      }));
    }
    return result;
  }
  /**
   * Merge multiple analysis results
   */
  mergeAnalysisResults(target, source) {
    if (source.scenes && Array.isArray(source.scenes)) {
      target.scenes.push(...source.scenes);
    }
    if (source.objects && Array.isArray(source.objects)) {
      target.objects.push(...source.objects);
    }
    if (source.summary && !target.summary) {
      target.summary = source.summary;
    }
    if (source.productFeatures && !target.productFeatures) {
      target.productFeatures = source.productFeatures;
    }
  }
  /**
   * Build video metadata
   */
  async buildVideoMetadata(videoFile) {
    return {
      file: videoFile,
      technical: {
        codec: "unknown",
        container: videoFile.format || "unknown",
        hasAudio: true,
        // Assume has audio
        audioCodec: "unknown"
      },
      content: {
        title: videoFile.name,
        description: `Video analysis for ${videoFile.name}`,
        tags: []
      }
    };
  }
  /**
   * Create default summary when none is provided
   */
  createDefaultSummary() {
    return {
      description: "Video content analysis completed",
      highlights: [],
      topics: [],
      keywords: []
    };
  }
  /**
   * Calculate quality metrics for analysis results
   */
  calculateQualityMetrics(results) {
    let overallScore = 0.5;
    let detectionAccuracy = 0.5;
    let analysisDepth = 0.5;
    if (results.scenes && results.scenes.length > 0) {
      overallScore += 0.2;
      detectionAccuracy += 0.2;
    }
    if (results.objects && results.objects.length > 0) {
      overallScore += 0.2;
      detectionAccuracy += 0.2;
    }
    if (results.summary && results.summary.description) {
      overallScore += 0.1;
      analysisDepth += 0.3;
    }
    if (results.productFeatures) {
      analysisDepth += 0.2;
    }
    return {
      overallScore: Math.min(1, overallScore),
      detectionAccuracy: Math.min(1, detectionAccuracy),
      analysisDepth: Math.min(1, analysisDepth)
    };
  }
};
async function analyzeVideoWithGemini(videoFile, gcsPath, mode, options, onProgress) {
  const engine = new AnalysisEngine();
  return engine.analyzeVideo(videoFile, gcsPath, mode, options, onProgress);
}

// src/product-analyzer.ts
var PRODUCT_ANALYSIS_PROMPTS = {
  APPEARANCE: `\u8BF7\u8BE6\u7EC6\u5206\u6790\u89C6\u9891\u4E2D\u4EA7\u54C1\u7684\u5916\u89C2\u7279\u5F81\uFF1A
1. \u989C\u8272\uFF1A\u4E3B\u8981\u989C\u8272\u3001\u914D\u8272\u65B9\u6848\u3001\u989C\u8272\u642D\u914D
2. \u5F62\u72B6\uFF1A\u6574\u4F53\u5F62\u72B6\u3001\u8BBE\u8BA1\u98CE\u683C\u3001\u51E0\u4F55\u7279\u5F81
3. \u5C3A\u5BF8\uFF1A\u76F8\u5BF9\u5927\u5C0F\u3001\u6BD4\u4F8B\u5173\u7CFB
4. \u98CE\u683C\uFF1A\u8BBE\u8BA1\u98CE\u683C\uFF08\u73B0\u4EE3\u3001\u7ECF\u5178\u3001\u7B80\u7EA6\u7B49\uFF09
5. \u8868\u9762\u5904\u7406\uFF1A\u5149\u6CFD\u5EA6\u3001\u7EB9\u7406\u3001\u56FE\u6848

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u8BE6\u7EC6\u7684\u5916\u89C2\u5206\u6790\u7ED3\u679C\u3002`,
  MATERIALS: `\u8BF7\u5206\u6790\u89C6\u9891\u4E2D\u4EA7\u54C1\u4F7F\u7528\u7684\u6750\u6599\uFF1A
1. \u4E3B\u8981\u6750\u6599\uFF1A\u91D1\u5C5E\u3001\u5851\u6599\u3001\u5E03\u6599\u3001\u76AE\u9769\u3001\u73BB\u7483\u7B49
2. \u6750\u6599\u8D28\u611F\uFF1A\u5149\u6ED1\u3001\u7C97\u7CD9\u3001\u67D4\u8F6F\u3001\u575A\u786C\u7B49
3. \u6750\u6599\u8D28\u91CF\uFF1A\u9AD8\u7AEF\u3001\u4E2D\u7AEF\u3001\u7ECF\u6D4E\u578B
4. \u7279\u6B8A\u6750\u6599\uFF1A\u73AF\u4FDD\u6750\u6599\u3001\u9AD8\u79D1\u6280\u6750\u6599\u7B49
5. \u6750\u6599\u7EC4\u5408\uFF1A\u591A\u79CD\u6750\u6599\u7684\u642D\u914D\u4F7F\u7528

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u6750\u6599\u5206\u6790\u7ED3\u679C\u3002`,
  FUNCTIONALITY: `\u8BF7\u5206\u6790\u4EA7\u54C1\u5C55\u793A\u7684\u529F\u80FD\u7279\u5F81\uFF1A
1. \u4E3B\u8981\u529F\u80FD\uFF1A\u4EA7\u54C1\u7684\u6838\u5FC3\u529F\u80FD\u548C\u7528\u9014
2. \u7279\u8272\u529F\u80FD\uFF1A\u72EC\u7279\u7684\u529F\u80FD\u7279\u70B9
3. \u64CD\u4F5C\u65B9\u5F0F\uFF1A\u5982\u4F55\u4F7F\u7528\u548C\u64CD\u4F5C
4. \u6280\u672F\u7279\u5F81\uFF1A\u6D89\u53CA\u7684\u6280\u672F\u548C\u521B\u65B0\u70B9
5. \u6027\u80FD\u8868\u73B0\uFF1A\u901F\u5EA6\u3001\u6548\u7387\u3001\u7CBE\u5EA6\u7B49

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u529F\u80FD\u5206\u6790\u7ED3\u679C\u3002`,
  USAGE_SCENARIOS: `\u8BF7\u5206\u6790\u4EA7\u54C1\u7684\u4F7F\u7528\u573A\u666F\u548C\u73AF\u5883\uFF1A
1. \u4F7F\u7528\u73AF\u5883\uFF1A\u5BA4\u5185\u3001\u6237\u5916\u3001\u529E\u516C\u5BA4\u3001\u5BB6\u5EAD\u7B49
2. \u4F7F\u7528\u65F6\u673A\uFF1A\u65E5\u5E38\u3001\u7279\u6B8A\u573A\u5408\u3001\u5B63\u8282\u6027\u7B49
3. \u7528\u6237\u884C\u4E3A\uFF1A\u5982\u4F55\u4F7F\u7528\u3001\u4F7F\u7528\u9891\u7387
4. \u642D\u914D\u4F7F\u7528\uFF1A\u4E0E\u5176\u4ED6\u4EA7\u54C1\u7684\u914D\u5408
5. \u9002\u7528\u4EBA\u7FA4\uFF1A\u5E74\u9F84\u3001\u6027\u522B\u3001\u804C\u4E1A\u7B49

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u4F7F\u7528\u573A\u666F\u5206\u6790\u7ED3\u679C\u3002`,
  BRAND_ELEMENTS: `\u8BF7\u8BC6\u522B\u89C6\u9891\u4E2D\u7684\u54C1\u724C\u5143\u7D20\uFF1A
1. Logo\u6807\u8BC6\uFF1A\u54C1\u724Clogo\u7684\u4F4D\u7F6E\u548C\u5C55\u793A
2. \u54C1\u724C\u8272\u5F69\uFF1A\u54C1\u724C\u4E13\u7528\u8272\u5F69
3. \u5305\u88C5\u8BBE\u8BA1\uFF1A\u4EA7\u54C1\u5305\u88C5\u7684\u54C1\u724C\u7279\u5F81
4. \u54C1\u724C\u6587\u5B57\uFF1A\u54C1\u724C\u540D\u79F0\u3001\u6807\u8BED\u7B49
5. \u54C1\u724C\u98CE\u683C\uFF1A\u6574\u4F53\u54C1\u724C\u8C03\u6027\u548C\u98CE\u683C

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u54C1\u724C\u5143\u7D20\u5206\u6790\u7ED3\u679C\u3002`,
  ATMOSPHERE: `\u8BF7\u5206\u6790\u89C6\u9891\u7684\u6574\u4F53\u6C1B\u56F4\u548C\u60C5\u611F\u8868\u8FBE\uFF1A
1. \u89C6\u89C9\u6C1B\u56F4\uFF1A\u660E\u4EAE\u3001\u6E29\u99A8\u3001\u4E13\u4E1A\u3001\u65F6\u5C1A\u7B49
2. \u60C5\u611F\u57FA\u8C03\uFF1A\u5FEB\u4E50\u3001\u4E25\u8083\u3001\u8F7B\u677E\u3001\u9AD8\u7AEF\u7B49
3. \u97F3\u4E50\u98CE\u683C\uFF1A\u80CC\u666F\u97F3\u4E50\u7684\u98CE\u683C\u548C\u60C5\u611F
4. \u62CD\u6444\u98CE\u683C\uFF1A\u955C\u5934\u8BED\u8A00\u3001\u6784\u56FE\u98CE\u683C
5. \u76EE\u6807\u60C5\u611F\uFF1A\u60F3\u8981\u4F20\u8FBE\u7684\u60C5\u611F\u548C\u611F\u53D7

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u6C1B\u56F4\u5206\u6790\u7ED3\u679C\u3002`
};
var ProductAnalyzer = class {
  constructor() {
    this.geminiClient = null;
  }
  /**
   * Initialize Gemini client
   */
  async initializeGeminiClient() {
    if (!this.geminiClient) {
      this.geminiClient = await useGemini();
    }
  }
  /**
   * Analyze product features in video
   */
  async analyzeProductFeatures(videoFile, gcsPath, options = {}) {
    try {
      await this.initializeGeminiClient();
      const analysisResults = await Promise.allSettled([
        this.analyzeAppearance(gcsPath),
        this.analyzeMaterials(gcsPath),
        this.analyzeFunctionality(gcsPath),
        this.analyzeUsageScenarios(gcsPath),
        this.analyzeBrandElements(gcsPath),
        this.analyzeAtmosphere(gcsPath)
      ]);
      return this.combineProductAnalysis(analysisResults);
    } catch (error) {
      throw new VideoAnalyzerError(
        `Product analysis failed for ${videoFile.name}: ${this.getErrorMessage(error)}`,
        "PRODUCT_ANALYSIS_FAILED",
        error
      );
    }
  }
  getErrorMessage(error) {
    return error instanceof Error ? error.message : String(error);
  }
  /**
   * Analyze product appearance
   */
  async analyzeAppearance(gcsPath) {
    return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.APPEARANCE);
  }
  /**
   * Analyze product materials
   */
  async analyzeMaterials(gcsPath) {
    return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.MATERIALS);
  }
  /**
   * Analyze product functionality
   */
  async analyzeFunctionality(gcsPath) {
    return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.FUNCTIONALITY);
  }
  /**
   * Analyze usage scenarios
   */
  async analyzeUsageScenarios(gcsPath) {
    return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.USAGE_SCENARIOS);
  }
  /**
   * Analyze brand elements
   */
  async analyzeBrandElements(gcsPath) {
    return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.BRAND_ELEMENTS);
  }
  /**
   * Analyze video atmosphere
   */
  async analyzeAtmosphere(gcsPath) {
    return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.ATMOSPHERE);
  }
  /**
   * Run a single product analysis
   */
  async runProductAnalysis(gcsPath, prompt) {
    try {
      const contents = [
        {
          role: "user",
          parts: [
            {
              text: prompt
            },
            {
              fileData: {
                mimeType: "video/mp4",
                fileUri: `gs://${gcsPath}`
              }
            }
          ]
        }
      ];
      const response = await this.geminiClient.generateContent(
        contents,
        "gemini-2.5-flash",
        {
          temperature: 0.2,
          // Lower temperature for more consistent analysis
          maxOutputTokens: 2048,
          topP: 0.8
        }
      );
      if (response.statusCode === 200 && response.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
        const responseText = response.response.candidates[0].content.parts[0].text;
        return this.parseProductAnalysisResponse(responseText);
      }
      return null;
    } catch (error) {
      console.warn("Product analysis step failed:", this.getErrorMessage(error));
      return null;
    }
  }
  /**
   * Parse product analysis response
   */
  parseProductAnalysisResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return this.parseProductTextResponse(responseText);
    } catch (error) {
      console.warn("Failed to parse product analysis JSON:", this.getErrorMessage(error));
      return this.parseProductTextResponse(responseText);
    }
  }
  /**
   * Parse text response for product analysis
   */
  parseProductTextResponse(text) {
    const result = {};
    const colorMatches = text.match(/颜色[：:]([^。\n]+)/g);
    if (colorMatches) {
      result.colors = colorMatches.map(
        (match) => match.replace(/颜色[：:]/, "").trim()
      );
    }
    const materialMatches = text.match(/材料[：:]([^。\n]+)/g);
    if (materialMatches) {
      result.materials = materialMatches.map(
        (match) => match.replace(/材料[：:]/, "").trim()
      );
    }
    const functionMatches = text.match(/功能[：:]([^。\n]+)/g);
    if (functionMatches) {
      result.functionality = functionMatches.map(
        (match) => match.replace(/功能[：:]/, "").trim()
      );
    }
    return result;
  }
  /**
   * Combine all product analysis results
   */
  combineProductAnalysis(results) {
    const productFeatures = {
      appearance: {
        colors: [],
        shape: "",
        size: "",
        style: ""
      },
      materials: [],
      functionality: [],
      usageScenarios: [],
      targetAudience: "",
      brandElements: []
    };
    results.forEach((result, index) => {
      if (result.status === "fulfilled" && result.value) {
        const data = result.value;
        switch (index) {
          case 0:
            if (data.colors) productFeatures.appearance.colors = data.colors;
            if (data.shape) productFeatures.appearance.shape = data.shape;
            if (data.size) productFeatures.appearance.size = data.size;
            if (data.style) productFeatures.appearance.style = data.style;
            break;
          case 1:
            if (data.materials) productFeatures.materials = data.materials;
            break;
          case 2:
            if (data.functionality) productFeatures.functionality = data.functionality;
            break;
          case 3:
            if (data.usageScenarios) productFeatures.usageScenarios = data.usageScenarios;
            if (data.targetAudience) productFeatures.targetAudience = data.targetAudience;
            break;
          case 4:
            if (data.brandElements) productFeatures.brandElements = data.brandElements;
            break;
          case 5:
            break;
        }
      }
    });
    return productFeatures;
  }
  /**
   * Analyze product for e-commerce categorization
   */
  async analyzeForEcommerce(videoFile, gcsPath) {
    const prompt = `\u8BF7\u5206\u6790\u8FD9\u4E2A\u4EA7\u54C1\u89C6\u9891\uFF0C\u4E3A\u7535\u5546\u5E73\u53F0\u5206\u7C7B\uFF1A
1. \u4EA7\u54C1\u7C7B\u522B\uFF1A\u786E\u5B9A\u4E3B\u8981\u4EA7\u54C1\u7C7B\u522B
2. \u5B50\u7C7B\u522B\uFF1A\u66F4\u5177\u4F53\u7684\u4EA7\u54C1\u5206\u7C7B
3. \u6807\u7B7E\uFF1A\u76F8\u5173\u7684\u4EA7\u54C1\u6807\u7B7E\u548C\u5173\u952E\u8BCD
4. \u4EF7\u683C\u8303\u56F4\uFF1A\u6839\u636E\u4EA7\u54C1\u7279\u5F81\u63A8\u6D4B\u4EF7\u683C\u6863\u6B21
5. \u76EE\u6807\u5E02\u573A\uFF1A\u76EE\u6807\u6D88\u8D39\u7FA4\u4F53\u548C\u5E02\u573A\u5B9A\u4F4D

\u8BF7\u4EE5JSON\u683C\u5F0F\u8FD4\u56DE\u7535\u5546\u5206\u7C7B\u7ED3\u679C\u3002`;
    try {
      const result = await this.runProductAnalysis(gcsPath, prompt);
      return {
        category: result?.category || "unknown",
        subcategory: result?.subcategory || "unknown",
        tags: result?.tags || [],
        priceRange: result?.priceRange || "unknown",
        targetMarket: result?.targetMarket || "unknown"
      };
    } catch (error) {
      throw new VideoAnalyzerError(
        `E-commerce analysis failed: ${this.getErrorMessage(error)}`,
        "ECOMMERCE_ANALYSIS_FAILED",
        error
      );
    }
  }
  /**
   * Generate product description from analysis
   */
  generateProductDescription(features) {
    const parts = [];
    if (features.appearance.colors.length > 0) {
      parts.push(`\u989C\u8272\uFF1A${features.appearance.colors.join("\u3001")}`);
    }
    if (features.appearance.style) {
      parts.push(`\u98CE\u683C\uFF1A${features.appearance.style}`);
    }
    if (features.materials.length > 0) {
      parts.push(`\u6750\u8D28\uFF1A${features.materials.join("\u3001")}`);
    }
    if (features.functionality.length > 0) {
      parts.push(`\u529F\u80FD\uFF1A${features.functionality.join("\u3001")}`);
    }
    if (features.usageScenarios.length > 0) {
      parts.push(`\u9002\u7528\u573A\u666F\uFF1A${features.usageScenarios.join("\u3001")}`);
    }
    return parts.join("\uFF1B");
  }
};
async function analyzeProductInVideo(videoFile, gcsPath, options) {
  const analyzer = new ProductAnalyzer();
  return analyzer.analyzeProductFeatures(videoFile, gcsPath, options);
}

// src/folder-matcher.ts
var fs3 = __toESM(require("fs"));
var path3 = __toESM(require("path"));
var import_util3 = require("util");
var readdir4 = (0, import_util3.promisify)(fs3.readdir);
var stat6 = (0, import_util3.promisify)(fs3.stat);
var DEFAULT_FOLDER_MATCH_CONFIG = {
  maxDepth: 3,
  minConfidence: 0.3,
  maxMatches: 5,
  enableSemanticAnalysis: true
};
var FolderMatcher = class {
  constructor(config) {
    this.geminiClient = null;
    this.folderCache = /* @__PURE__ */ new Map();
    this.config = { ...DEFAULT_FOLDER_MATCH_CONFIG, ...config };
  }
  /**
   * Initialize Gemini client
   */
  async initializeGeminiClient() {
    if (!this.geminiClient) {
      this.geminiClient = await useGemini();
    }
  }
  /**
   * Find matching folders for video analysis result
   */
  async findMatchingFolders(analysisResult) {
    try {
      const folders = await this.scanFolders();
      const contentDescription = this.generateContentDescription(analysisResult);
      const matches = await this.performFolderMatching(contentDescription, folders);
      return matches.filter((match) => match.confidence >= this.config.minConfidence).sort((a, b) => b.confidence - a.confidence).slice(0, this.config.maxMatches);
    } catch (error) {
      throw new VideoAnalyzerError(
        `Folder matching failed: ${this.getErrorMessage(error)}`,
        "FOLDER_MATCHING_FAILED",
        error
      );
    }
  }
  /**
   * Scan for available folders
   */
  async scanFolders() {
    const cacheKey = this.config.baseDirectory;
    if (this.folderCache.has(cacheKey)) {
      return this.folderCache.get(cacheKey);
    }
    const folders = [];
    await this.scanFoldersRecursive(this.config.baseDirectory, folders, 0);
    this.folderCache.set(cacheKey, folders);
    return folders;
  }
  /**
   * Recursively scan folders
   */
  async scanFoldersRecursive(dirPath, folders, currentDepth) {
    if (currentDepth >= this.config.maxDepth) {
      return;
    }
    try {
      const entries = await readdir4(dirPath, { withFileTypes: true });
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const fullPath = path3.join(dirPath, entry.name);
          folders.push(fullPath);
          await this.scanFoldersRecursive(fullPath, folders, currentDepth + 1);
        }
      }
    } catch (error) {
      console.warn(`Failed to scan directory ${dirPath}:`, this.getErrorMessage(error));
    }
  }
  /**
   * Generate content description for matching
   */
  generateContentDescription(analysisResult) {
    const parts = [];
    if (analysisResult.summary.description) {
      parts.push(`\u5185\u5BB9\u63CF\u8FF0\uFF1A${analysisResult.summary.description}`);
    }
    if (analysisResult.summary.keywords.length > 0) {
      parts.push(`\u5173\u952E\u8BCD\uFF1A${analysisResult.summary.keywords.join("\u3001")}`);
    }
    if (analysisResult.summary.topics.length > 0) {
      parts.push(`\u4E3B\u9898\uFF1A${analysisResult.summary.topics.join("\u3001")}`);
    }
    if (analysisResult.productFeatures) {
      const features = analysisResult.productFeatures;
      if (features.appearance.colors.length > 0) {
        parts.push(`\u989C\u8272\uFF1A${features.appearance.colors.join("\u3001")}`);
      }
      if (features.materials.length > 0) {
        parts.push(`\u6750\u8D28\uFF1A${features.materials.join("\u3001")}`);
      }
      if (features.functionality.length > 0) {
        parts.push(`\u529F\u80FD\uFF1A${features.functionality.join("\u3001")}`);
      }
    }
    if (analysisResult.scenes.length > 0) {
      const sceneDescriptions = analysisResult.scenes.map((scene) => scene.description).slice(0, 3);
      parts.push(`\u573A\u666F\uFF1A${sceneDescriptions.join("\u3001")}`);
    }
    if (analysisResult.objects.length > 0) {
      const objectNames = analysisResult.objects.map((obj) => obj.name).slice(0, 5);
      parts.push(`\u7269\u4F53\uFF1A${objectNames.join("\u3001")}`);
    }
    return parts.join("\n");
  }
  /**
   * Perform folder matching using AI analysis
   */
  async performFolderMatching(contentDescription, folders) {
    const matches = [];
    const ruleBasedMatches = this.performRuleBasedMatching(contentDescription, folders);
    matches.push(...ruleBasedMatches);
    if (this.config.enableSemanticAnalysis) {
      const semanticMatches = await this.performSemanticMatching(contentDescription, folders);
      matches.push(...semanticMatches);
    }
    return this.mergeMatches(matches);
  }
  /**
   * Perform rule-based folder matching
   */
  performRuleBasedMatching(contentDescription, folders) {
    const matches = [];
    const contentLower = contentDescription.toLowerCase();
    for (const folderPath of folders) {
      const folderName = path3.basename(folderPath).toLowerCase();
      const confidence = this.calculateRuleBasedConfidence(contentLower, folderName);
      if (confidence > 0) {
        matches.push({
          folderPath,
          folderName: path3.basename(folderPath),
          confidence,
          reasons: this.generateRuleBasedReasons(contentLower, folderName),
          semanticScore: 0,
          relevanceScore: confidence,
          action: this.determineAction(confidence)
        });
      }
    }
    return matches;
  }
  /**
   * Calculate rule-based confidence score
   */
  calculateRuleBasedConfidence(content, folderName) {
    let confidence = 0;
    const keywords = this.extractKeywords(content);
    for (const keyword of keywords) {
      if (folderName.includes(keyword)) {
        confidence += 0.3;
      }
    }
    const categories = this.extractCategories(content);
    for (const category of categories) {
      if (folderName.includes(category)) {
        confidence += 0.4;
      }
    }
    const colors = this.extractColors(content);
    for (const color of colors) {
      if (folderName.includes(color)) {
        confidence += 0.2;
      }
    }
    return Math.min(1, confidence);
  }
  /**
   * Generate reasons for rule-based matching
   */
  generateRuleBasedReasons(content, folderName) {
    const reasons = [];
    const keywords = this.extractKeywords(content);
    for (const keyword of keywords) {
      if (folderName.includes(keyword)) {
        reasons.push(`\u5173\u952E\u8BCD\u5339\u914D\uFF1A${keyword}`);
      }
    }
    const categories = this.extractCategories(content);
    for (const category of categories) {
      if (folderName.includes(category)) {
        reasons.push(`\u7C7B\u522B\u5339\u914D\uFF1A${category}`);
      }
    }
    return reasons;
  }
  /**
   * Perform semantic matching using Gemini AI
   */
  async performSemanticMatching(contentDescription, folders) {
    try {
      await this.initializeGeminiClient();
      const folderNames = folders.map((f) => path3.basename(f));
      const prompt = `\u8BF7\u5206\u6790\u4EE5\u4E0B\u89C6\u9891\u5185\u5BB9\u63CF\u8FF0\uFF0C\u5E76\u4E3A\u5176\u63A8\u8350\u6700\u5408\u9002\u7684\u6587\u4EF6\u5939\uFF1A

\u89C6\u9891\u5185\u5BB9\u63CF\u8FF0\uFF1A
${contentDescription}

\u53EF\u9009\u6587\u4EF6\u5939\uFF1A
${folderNames.map((name, index) => `${index + 1}. ${name}`).join("\n")}

\u8BF7\u4E3A\u6BCF\u4E2A\u6587\u4EF6\u5939\u8BC4\u5206\uFF080-1\uFF09\uFF0C\u5E76\u8BF4\u660E\u5339\u914D\u539F\u56E0\u3002\u8FD4\u56DEJSON\u683C\u5F0F\uFF1A
{
  "matches": [
    {
      "folderName": "\u6587\u4EF6\u5939\u540D\u79F0",
      "score": 0.8,
      "reasons": ["\u5339\u914D\u539F\u56E01", "\u5339\u914D\u539F\u56E02"]
    }
  ]
}`;
      const response = await this.geminiClient.generateText(
        prompt,
        "gemini-2.5-flash",
        {
          temperature: 0.3,
          maxOutputTokens: 2048
        }
      );
      if (response) {
        return this.parseSemanticMatchingResponse(response, folders);
      }
      return [];
    } catch (error) {
      console.warn("Semantic matching failed:", this.getErrorMessage(error));
      return [];
    }
  }
  /**
   * Parse semantic matching response
   */
  parseSemanticMatchingResponse(response, folders) {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) return [];
      const data = JSON.parse(jsonMatch[0]);
      const matches = [];
      if (data.matches && Array.isArray(data.matches)) {
        for (const match of data.matches) {
          const folderPath = folders.find((f) => path3.basename(f) === match.folderName);
          if (folderPath && match.score > 0) {
            matches.push({
              folderPath,
              folderName: match.folderName,
              confidence: match.score,
              reasons: match.reasons || [],
              semanticScore: match.score,
              relevanceScore: match.score,
              action: this.determineAction(match.score)
            });
          }
        }
      }
      return matches;
    } catch (error) {
      console.warn("Failed to parse semantic matching response:", this.getErrorMessage(error));
      return [];
    }
  }
  getErrorMessage(error) {
    return error instanceof Error ? error.message : String(error);
  }
  /**
   * Merge and deduplicate matches
   */
  mergeMatches(matches) {
    const mergedMap = /* @__PURE__ */ new Map();
    for (const match of matches) {
      const existing = mergedMap.get(match.folderPath);
      if (existing) {
        existing.confidence = Math.max(existing.confidence, match.confidence);
        existing.semanticScore = Math.max(existing.semanticScore, match.semanticScore);
        existing.relevanceScore = Math.max(existing.relevanceScore, match.relevanceScore);
        existing.reasons = [.../* @__PURE__ */ new Set([...existing.reasons, ...match.reasons])];
        existing.action = this.determineAction(existing.confidence);
      } else {
        mergedMap.set(match.folderPath, { ...match });
      }
    }
    return Array.from(mergedMap.values());
  }
  /**
   * Determine recommended action based on confidence
   */
  determineAction(confidence) {
    if (confidence >= 0.8) return "move";
    if (confidence >= 0.6) return "copy";
    if (confidence >= 0.4) return "link";
    return "ignore";
  }
  /**
   * Extract keywords from content
   */
  extractKeywords(content) {
    const keywords = [];
    const productKeywords = ["\u4EA7\u54C1", "\u5546\u54C1", "\u7269\u54C1", "\u8BBE\u5907", "\u5DE5\u5177", "\u88C5\u7F6E"];
    for (const keyword of productKeywords) {
      if (content.includes(keyword)) {
        keywords.push(keyword);
      }
    }
    return keywords;
  }
  /**
   * Extract categories from content
   */
  extractCategories(content) {
    const categories = [];
    const categoryMap = {
      "\u7535\u5B50": ["\u7535\u5B50", "\u6570\u7801", "\u624B\u673A", "\u7535\u8111", "\u76F8\u673A"],
      "\u670D\u88C5": ["\u670D\u88C5", "\u8863\u670D", "\u978B\u5B50", "\u5305\u5305", "\u914D\u9970"],
      "\u5BB6\u5C45": ["\u5BB6\u5C45", "\u5BB6\u5177", "\u88C5\u9970", "\u53A8\u5177", "\u5E8A\u54C1"],
      "\u7F8E\u5986": ["\u7F8E\u5986", "\u5316\u5986\u54C1", "\u62A4\u80A4", "\u9999\u6C34", "\u5F69\u5986"],
      "\u98DF\u54C1": ["\u98DF\u54C1", "\u96F6\u98DF", "\u996E\u6599", "\u8336\u53F6", "\u5496\u5561"]
    };
    for (const [category, keywords] of Object.entries(categoryMap)) {
      for (const keyword of keywords) {
        if (content.includes(keyword)) {
          categories.push(category);
          break;
        }
      }
    }
    return categories;
  }
  /**
   * Extract colors from content
   */
  extractColors(content) {
    const colors = ["\u7EA2", "\u84DD", "\u7EFF", "\u9EC4", "\u9ED1", "\u767D", "\u7070", "\u7D2B", "\u7C89", "\u6A59"];
    return colors.filter((color) => content.includes(color));
  }
  /**
   * Clear folder cache
   */
  clearCache() {
    this.folderCache.clear();
  }
  /**
   * Update configuration
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    this.clearCache();
  }
};
async function findMatchingFoldersForVideo(analysisResult, config) {
  const matcher = new FolderMatcher(config);
  return matcher.findMatchingFolders(analysisResult);
}

// src/report-generator.ts
var fs4 = __toESM(require("fs"));
var path4 = __toESM(require("path"));
var import_util4 = require("util");
var writeFile4 = (0, import_util4.promisify)(fs4.writeFile);
var mkdir2 = (0, import_util4.promisify)(fs4.mkdir);
var ReportGenerator = class {
  /**
   * Generate comprehensive analysis report
   */
  async generateReport(videoResults, folderMatches = {}, options) {
    try {
      await this.ensureOutputDirectory(options.outputPath);
      const report = this.buildReport(videoResults, folderMatches, options);
      let reportContent;
      switch (options.format) {
        case "xml":
          reportContent = this.generateXMLReport(report);
          break;
        case "json":
          reportContent = this.generateJSONReport(report);
          break;
        case "csv":
          reportContent = this.generateCSVReport(report);
          break;
        case "html":
          reportContent = this.generateHTMLReport(report);
          break;
        default:
          throw new Error(`Unsupported format: ${options.format}`);
      }
      await writeFile4(options.outputPath, reportContent, "utf-8");
      return options.outputPath;
    } catch (error) {
      throw new VideoAnalyzerError(
        `Report generation failed: ${this.getErrorMessage(error)}`,
        "REPORT_GENERATION_FAILED",
        error
      );
    }
  }
  getErrorMessage(error) {
    return error instanceof Error ? error.message : String(error);
  }
  /**
   * Build report data structure
   */
  buildReport(videoResults, folderMatches, options) {
    const totalProcessingTime = videoResults.reduce(
      (sum, result) => sum + result.processingTime,
      0
    );
    const totalScenes = videoResults.reduce(
      (sum, result) => sum + result.scenes.length,
      0
    );
    const totalObjects = videoResults.reduce(
      (sum, result) => sum + result.objects.length,
      0
    );
    const allKeywords = videoResults.flatMap((result) => result.summary.keywords);
    const keywordCounts = this.countOccurrences(allKeywords);
    const commonThemes = Object.entries(keywordCounts).sort(([, a], [, b]) => b - a).slice(0, 10).map(([keyword]) => keyword);
    const allTopics = videoResults.flatMap((result) => result.summary.topics);
    const topicCounts = this.countOccurrences(allTopics);
    const recommendedCategories = Object.entries(topicCounts).sort(([, a], [, b]) => b - a).slice(0, 5).map(([topic]) => topic);
    return {
      metadata: {
        generatedAt: /* @__PURE__ */ new Date(),
        version: "1.0.0",
        totalVideos: videoResults.length,
        totalProcessingTime
      },
      videoResults,
      folderMatches: options.includeFolderMatching ? folderMatches : void 0,
      summary: {
        totalScenes,
        totalObjects,
        commonThemes,
        recommendedCategories
      },
      exportOptions: {
        format: options.format,
        includeImages: false,
        includeThumbnails: options.includeThumbnails || false
      }
    };
  }
  /**
   * Generate XML report
   */
  generateXMLReport(report) {
    const xml = ['<?xml version="1.0" encoding="UTF-8"?>'];
    xml.push("<VideoAnalysisReport>");
    xml.push("  <Metadata>");
    xml.push(`    <GeneratedAt>${report.metadata.generatedAt.toISOString()}</GeneratedAt>`);
    xml.push(`    <Version>${report.metadata.version}</Version>`);
    xml.push(`    <TotalVideos>${report.metadata.totalVideos}</TotalVideos>`);
    xml.push(`    <TotalProcessingTime>${report.metadata.totalProcessingTime}</TotalProcessingTime>`);
    xml.push("  </Metadata>");
    xml.push("  <Summary>");
    xml.push(`    <TotalScenes>${report.summary.totalScenes}</TotalScenes>`);
    xml.push(`    <TotalObjects>${report.summary.totalObjects}</TotalObjects>`);
    xml.push("    <CommonThemes>");
    report.summary.commonThemes.forEach((theme) => {
      xml.push(`      <Theme>${this.escapeXML(theme)}</Theme>`);
    });
    xml.push("    </CommonThemes>");
    xml.push("    <RecommendedCategories>");
    report.summary.recommendedCategories.forEach((category) => {
      xml.push(`      <Category>${this.escapeXML(category)}</Category>`);
    });
    xml.push("    </RecommendedCategories>");
    xml.push("  </Summary>");
    xml.push("  <VideoResults>");
    report.videoResults.forEach((result, index) => {
      xml.push(`    <Video id="${index + 1}">`);
      xml.push(`      <FileName>${this.escapeXML(result.metadata.file.name)}</FileName>`);
      xml.push(`      <FilePath>${this.escapeXML(result.metadata.file.path)}</FilePath>`);
      xml.push(`      <FileSize>${result.metadata.file.size}</FileSize>`);
      xml.push(`      <AnalyzedAt>${result.analyzedAt.toISOString()}</AnalyzedAt>`);
      xml.push(`      <ProcessingTime>${result.processingTime}</ProcessingTime>`);
      xml.push("      <Summary>");
      xml.push(`        <Description>${this.escapeXML(result.summary.description)}</Description>`);
      xml.push("        <Keywords>");
      result.summary.keywords.forEach((keyword) => {
        xml.push(`          <Keyword>${this.escapeXML(keyword)}</Keyword>`);
      });
      xml.push("        </Keywords>");
      xml.push("        <Topics>");
      result.summary.topics.forEach((topic) => {
        xml.push(`          <Topic>${this.escapeXML(topic)}</Topic>`);
      });
      xml.push("        </Topics>");
      xml.push("      </Summary>");
      xml.push("      <Scenes>");
      result.scenes.forEach((scene, sceneIndex) => {
        xml.push(`        <Scene id="${sceneIndex + 1}">`);
        xml.push(`          <StartTime>${scene.startTime}</StartTime>`);
        xml.push(`          <EndTime>${scene.endTime}</EndTime>`);
        xml.push(`          <Duration>${scene.duration}</Duration>`);
        xml.push(`          <Description>${this.escapeXML(scene.description)}</Description>`);
        xml.push(`          <Confidence>${scene.confidence}</Confidence>`);
        xml.push("        </Scene>");
      });
      xml.push("      </Scenes>");
      xml.push("      <Objects>");
      result.objects.forEach((object, objectIndex) => {
        xml.push(`        <Object id="${objectIndex + 1}">`);
        xml.push(`          <Name>${this.escapeXML(object.name)}</Name>`);
        xml.push(`          <Category>${this.escapeXML(object.category)}</Category>`);
        xml.push(`          <Confidence>${object.confidence}</Confidence>`);
        xml.push("        </Object>");
      });
      xml.push("      </Objects>");
      if (result.productFeatures) {
        xml.push("      <ProductFeatures>");
        xml.push("        <Appearance>");
        xml.push("          <Colors>");
        result.productFeatures.appearance.colors.forEach((color) => {
          xml.push(`            <Color>${this.escapeXML(color)}</Color>`);
        });
        xml.push("          </Colors>");
        xml.push(`          <Shape>${this.escapeXML(result.productFeatures.appearance.shape)}</Shape>`);
        xml.push(`          <Size>${this.escapeXML(result.productFeatures.appearance.size)}</Size>`);
        xml.push(`          <Style>${this.escapeXML(result.productFeatures.appearance.style)}</Style>`);
        xml.push("        </Appearance>");
        xml.push("        <Materials>");
        result.productFeatures.materials.forEach((material) => {
          xml.push(`          <Material>${this.escapeXML(material)}</Material>`);
        });
        xml.push("        </Materials>");
        xml.push("        <Functionality>");
        result.productFeatures.functionality.forEach((func) => {
          xml.push(`          <Function>${this.escapeXML(func)}</Function>`);
        });
        xml.push("        </Functionality>");
        xml.push("      </ProductFeatures>");
      }
      xml.push("    </Video>");
    });
    xml.push("  </VideoResults>");
    if (report.folderMatches) {
      xml.push("  <FolderMatches>");
      Object.entries(report.folderMatches).forEach(([videoPath, matches]) => {
        xml.push(`    <VideoMatches videoPath="${this.escapeXML(videoPath)}">`);
        matches.forEach((match, matchIndex) => {
          xml.push(`      <Match id="${matchIndex + 1}">`);
          xml.push(`        <FolderPath>${this.escapeXML(match.folderPath)}</FolderPath>`);
          xml.push(`        <FolderName>${this.escapeXML(match.folderName)}</FolderName>`);
          xml.push(`        <Confidence>${match.confidence}</Confidence>`);
          xml.push(`        <Action>${match.action}</Action>`);
          xml.push("        <Reasons>");
          match.reasons.forEach((reason) => {
            xml.push(`          <Reason>${this.escapeXML(reason)}</Reason>`);
          });
          xml.push("        </Reasons>");
          xml.push("      </Match>");
        });
        xml.push("    </VideoMatches>");
      });
      xml.push("  </FolderMatches>");
    }
    xml.push("</VideoAnalysisReport>");
    return xml.join("\n");
  }
  /**
   * Generate JSON report
   */
  generateJSONReport(report) {
    return JSON.stringify(report, null, 2);
  }
  /**
   * Generate CSV report
   */
  generateCSVReport(report) {
    const headers = [
      "FileName",
      "FilePath",
      "FileSize",
      "ProcessingTime",
      "ScenesCount",
      "ObjectsCount",
      "Keywords",
      "Topics",
      "Description"
    ];
    const rows = [headers.join(",")];
    report.videoResults.forEach((result) => {
      const row = [
        this.escapeCSV(result.metadata.file.name),
        this.escapeCSV(result.metadata.file.path),
        result.metadata.file.size.toString(),
        result.processingTime.toString(),
        result.scenes.length.toString(),
        result.objects.length.toString(),
        this.escapeCSV(result.summary.keywords.join("; ")),
        this.escapeCSV(result.summary.topics.join("; ")),
        this.escapeCSV(result.summary.description)
      ];
      rows.push(row.join(","));
    });
    return rows.join("\n");
  }
  /**
   * Generate HTML report
   */
  generateHTMLReport(report) {
    const html = ["<!DOCTYPE html>"];
    html.push('<html lang="zh-CN">');
    html.push("<head>");
    html.push('  <meta charset="UTF-8">');
    html.push('  <meta name="viewport" content="width=device-width, initial-scale=1.0">');
    html.push("  <title>\u89C6\u9891\u5206\u6790\u62A5\u544A</title>");
    html.push("  <style>");
    html.push("    body { font-family: Arial, sans-serif; margin: 20px; }");
    html.push("    .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }");
    html.push("    .summary { margin: 20px 0; }");
    html.push("    .video-result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }");
    html.push("    .scenes, .objects { margin: 10px 0; }");
    html.push("    .scene, .object { background: #f9f9f9; padding: 5px; margin: 5px 0; border-radius: 3px; }");
    html.push("  </style>");
    html.push("</head>");
    html.push("<body>");
    html.push('  <div class="header">');
    html.push("    <h1>\u89C6\u9891\u5206\u6790\u62A5\u544A</h1>");
    html.push(`    <p>\u751F\u6210\u65F6\u95F4\uFF1A${report.metadata.generatedAt.toLocaleString("zh-CN")}</p>`);
    html.push(`    <p>\u603B\u89C6\u9891\u6570\uFF1A${report.metadata.totalVideos}</p>`);
    html.push(`    <p>\u603B\u5904\u7406\u65F6\u95F4\uFF1A${report.metadata.totalProcessingTime}ms</p>`);
    html.push("  </div>");
    html.push('  <div class="summary">');
    html.push("    <h2>\u5206\u6790\u6458\u8981</h2>");
    html.push(`    <p>\u603B\u573A\u666F\u6570\uFF1A${report.summary.totalScenes}</p>`);
    html.push(`    <p>\u603B\u7269\u4F53\u6570\uFF1A${report.summary.totalObjects}</p>`);
    html.push(`    <p>\u5E38\u89C1\u4E3B\u9898\uFF1A${report.summary.commonThemes.join("\u3001")}</p>`);
    html.push("  </div>");
    html.push('  <div class="video-results">');
    html.push("    <h2>\u89C6\u9891\u5206\u6790\u7ED3\u679C</h2>");
    report.videoResults.forEach((result, index) => {
      html.push(`    <div class="video-result">`);
      html.push(`      <h3>${index + 1}. ${this.escapeHTML(result.metadata.file.name)}</h3>`);
      html.push(`      <p><strong>\u6587\u4EF6\u8DEF\u5F84\uFF1A</strong>${this.escapeHTML(result.metadata.file.path)}</p>`);
      html.push(`      <p><strong>\u6587\u4EF6\u5927\u5C0F\uFF1A</strong>${result.metadata.file.size} bytes</p>`);
      html.push(`      <p><strong>\u5904\u7406\u65F6\u95F4\uFF1A</strong>${result.processingTime}ms</p>`);
      html.push(`      <p><strong>\u63CF\u8FF0\uFF1A</strong>${this.escapeHTML(result.summary.description)}</p>`);
      if (result.scenes.length > 0) {
        html.push('      <div class="scenes">');
        html.push("        <h4>\u573A\u666F\u5206\u6790</h4>");
        result.scenes.forEach((scene) => {
          html.push(`        <div class="scene">`);
          html.push(`          <strong>${scene.startTime}s - ${scene.endTime}s:</strong> ${this.escapeHTML(scene.description)}`);
          html.push("        </div>");
        });
        html.push("      </div>");
      }
      if (result.objects.length > 0) {
        html.push('      <div class="objects">');
        html.push("        <h4>\u7269\u4F53\u8BC6\u522B</h4>");
        result.objects.forEach((object) => {
          html.push(`        <div class="object">`);
          html.push(`          <strong>${this.escapeHTML(object.name)}</strong> (${object.category}) - \u7F6E\u4FE1\u5EA6: ${object.confidence.toFixed(2)}`);
          html.push("        </div>");
        });
        html.push("      </div>");
      }
      html.push("    </div>");
    });
    html.push("  </div>");
    html.push("</body>");
    html.push("</html>");
    return html.join("\n");
  }
  /**
   * Ensure output directory exists
   */
  async ensureOutputDirectory(filePath) {
    const dir = path4.dirname(filePath);
    try {
      await mkdir2(dir, { recursive: true });
    } catch (error) {
    }
  }
  /**
   * Count occurrences of items in array
   */
  countOccurrences(items) {
    const counts = {};
    items.forEach((item) => {
      counts[item] = (counts[item] || 0) + 1;
    });
    return counts;
  }
  /**
   * Escape XML special characters
   */
  escapeXML(text) {
    return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
  }
  /**
   * Escape HTML special characters
   */
  escapeHTML(text) {
    return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;");
  }
  /**
   * Escape CSV special characters
   */
  escapeCSV(text) {
    if (text.includes(",") || text.includes('"') || text.includes("\n")) {
      return `"${text.replace(/"/g, '""')}"`;
    }
    return text;
  }
};
async function generateAnalysisReport(videoResults, folderMatches, options) {
  const generator = new ReportGenerator();
  return generator.generateReport(videoResults, folderMatches, options);
}

// src/frame-analyzer.ts
var FrameAnalyzer = class {
  /**
   * Extract and analyze frames from video
   * Note: This is a placeholder implementation
   * In a real-world scenario, you would use ffmpeg or similar tools
   */
  async analyzeFrames(videoFile, options = {}) {
    try {
      const frameSamplingInterval = options.frameSamplingInterval || 1;
      const maxFrames = options.maxFrames || 30;
      const frameAnalysis = [];
      const estimatedDuration = 60;
      const totalFrames = Math.min(
        Math.floor(estimatedDuration / frameSamplingInterval),
        maxFrames
      );
      for (let i = 0; i < totalFrames; i++) {
        const timestamp = i * frameSamplingInterval;
        frameAnalysis.push({
          timestamp,
          description: `Frame at ${timestamp}s - placeholder description`,
          objects: [
            {
              name: "placeholder_object",
              category: "general",
              confidence: 0.8
            }
          ],
          quality: 0.8,
          type: i === 0 ? "keyframe" : "regular"
        });
      }
      return frameAnalysis;
    } catch (error) {
      throw new VideoAnalyzerError(
        `Frame analysis failed for ${videoFile.name}: ${this.getErrorMessage(error)}`,
        "FRAME_ANALYSIS_FAILED",
        error
      );
    }
  }
  getErrorMessage(error) {
    return error instanceof Error ? error.message : String(error);
  }
  /**
   * Extract key frames from video
   * Note: This is a placeholder implementation
   */
  async extractKeyFrames(_videoFile, interval = 1) {
    const keyFrames = [];
    for (let i = 0; i < 30; i += interval) {
      keyFrames.push({
        timestamp: i,
        quality: 0.8 + Math.random() * 0.2
        // Random quality between 0.8-1.0
      });
    }
    return keyFrames;
  }
  /**
   * Analyze single frame
   * Note: This would integrate with GPT-4 Vision API in a real implementation
   */
  async analyzeSingleFrame(_frameData, timestamp) {
    return {
      timestamp,
      description: `Placeholder frame analysis at ${timestamp}s`,
      objects: [
        {
          name: "detected_object",
          category: "general",
          confidence: 0.7
        }
      ],
      quality: 0.8,
      type: "regular"
    };
  }
};
async function analyzeVideoFrames(videoFile, options) {
  const analyzer = new FrameAnalyzer();
  return analyzer.analyzeFrames(videoFile, options);
}

// src/video-analyzer.ts
var VideoAnalyzer = class {
  constructor(config = {}) {
    this.uploader = null;
    this.folderMatcher = null;
    this.config = config;
    this.scanner = new VideoScanner();
    this.analysisEngine = new AnalysisEngine();
    this.productAnalyzer = new ProductAnalyzer();
    this.reportGenerator = new ReportGenerator();
    this.frameAnalyzer = new FrameAnalyzer();
  }
  /**
   * Scan directory for video files
   */
  async scanDirectory(directoryPath, options) {
    try {
      return await this.scanner.scanDirectory(directoryPath);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Directory scan failed: ${errorMessage}`,
        "SCAN_FAILED",
        error
      );
    }
  }
  /**
   * Analyze a single video file
   */
  async analyzeVideo(videoFile, mode, options = {}, onProgress) {
    try {
      if (!this.uploader) {
        this.initializeUploader();
      }
      const progress = {
        step: "Starting analysis",
        progress: 0,
        currentFile: videoFile.name,
        stage: "upload"
      };
      onProgress?.(progress);
      progress.step = "Uploading video";
      progress.progress = 10;
      onProgress?.(progress);
      const uploadResult = await this.uploader.uploadVideo(videoFile);
      if (!uploadResult.success) {
        throw new Error(`Upload failed: ${uploadResult.error}`);
      }
      let analysisResult;
      if (mode.type === "gemini") {
        analysisResult = await this.analysisEngine.analyzeVideo(
          videoFile,
          uploadResult.gcsPath,
          mode,
          options,
          onProgress
        );
      } else if (mode.type === "gpt4") {
        progress.step = "Analyzing frames";
        progress.stage = "analysis";
        onProgress?.(progress);
        const frameAnalysis = await this.frameAnalyzer.analyzeFrames(videoFile, options);
        analysisResult = {
          metadata: {
            file: videoFile,
            technical: {
              codec: "unknown",
              container: videoFile.format || "unknown",
              hasAudio: true
            }
          },
          analysisMode: mode,
          scenes: [],
          objects: [],
          summary: {
            description: "GPT-4 frame-by-frame analysis completed",
            highlights: [],
            topics: [],
            keywords: []
          },
          frameAnalysis,
          analyzedAt: /* @__PURE__ */ new Date(),
          processingTime: Date.now()
        };
      } else {
        throw new Error(`Unsupported analysis mode: ${mode.type}`);
      }
      if (options.enableProductAnalysis && mode.type === "gemini") {
        progress.step = "Analyzing product features";
        progress.progress = 80;
        onProgress?.(progress);
        analysisResult.productFeatures = await this.productAnalyzer.analyzeProductFeatures(
          videoFile,
          uploadResult.gcsPath,
          options
        );
      }
      progress.step = "Analysis completed";
      progress.progress = 100;
      progress.stage = "complete";
      onProgress?.(progress);
      return analysisResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Video analysis failed: ${errorMessage}`,
        "ANALYSIS_FAILED",
        error
      );
    }
  }
  /**
   * Analyze multiple videos in a directory
   */
  async analyzeDirectory(directoryPath, mode, scanOptions, analysisOptions, onProgress) {
    try {
      const videoFiles = await this.scanDirectory(directoryPath, scanOptions);
      if (videoFiles.length === 0) {
        throw new Error("No video files found in directory");
      }
      const results = [];
      for (let i = 0; i < videoFiles.length; i++) {
        const videoFile = videoFiles[i];
        const overallProgress = {
          step: `Analyzing video ${i + 1}/${videoFiles.length}: ${videoFile.name}`,
          progress: i / videoFiles.length * 100,
          currentFile: videoFile.name,
          stage: "processing"
        };
        onProgress?.(overallProgress);
        try {
          const result = await this.analyzeVideo(videoFile, mode, analysisOptions);
          results.push(result);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.warn(`Failed to analyze ${videoFile.name}:`, errorMessage);
        }
      }
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Directory analysis failed: ${errorMessage}`,
        "DIRECTORY_ANALYSIS_FAILED",
        error
      );
    }
  }
  /**
   * Find matching folders for analysis results
   */
  async findMatchingFolders(analysisResults, folderConfig) {
    try {
      if (!this.folderMatcher) {
        this.folderMatcher = new FolderMatcher(folderConfig);
      }
      const matches = {};
      for (const result of analysisResults) {
        const videoPath = result.metadata.file.path;
        matches[videoPath] = await this.folderMatcher.findMatchingFolders(result);
      }
      return matches;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Folder matching failed: ${errorMessage}`,
        "FOLDER_MATCHING_FAILED",
        error
      );
    }
  }
  /**
   * Generate analysis report
   */
  async generateReport(analysisResults, folderMatches = {}, reportOptions) {
    try {
      return await this.reportGenerator.generateReport(
        analysisResults,
        folderMatches,
        reportOptions
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Report generation failed: ${errorMessage}`,
        "REPORT_GENERATION_FAILED",
        error
      );
    }
  }
  /**
   * Complete workflow: scan, analyze, match folders, and generate report
   */
  async analyzeDirectoryComplete(directoryPath, mode, options = {}) {
    try {
      const { scanOptions, analysisOptions, folderConfig, reportOptions, onProgress } = options;
      onProgress?.({
        step: "Analyzing videos",
        progress: 0,
        stage: "processing"
      });
      const analysisResults = await this.analyzeDirectory(
        directoryPath,
        mode,
        scanOptions,
        analysisOptions,
        onProgress
      );
      let folderMatches = {};
      if (folderConfig) {
        onProgress?.({
          step: "Finding matching folders",
          progress: 80,
          stage: "processing"
        });
        folderMatches = await this.findMatchingFolders(analysisResults, folderConfig);
      }
      let reportPath;
      if (reportOptions) {
        onProgress?.({
          step: "Generating report",
          progress: 90,
          stage: "processing"
        });
        reportPath = await this.generateReport(analysisResults, folderMatches, reportOptions);
      }
      onProgress?.({
        step: "Complete",
        progress: 100,
        stage: "complete"
      });
      return {
        analysisResults,
        folderMatches,
        reportPath
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new VideoAnalyzerError(
        `Complete analysis workflow failed: ${errorMessage}`,
        "WORKFLOW_FAILED",
        error
      );
    }
  }
  /**
   * Initialize uploader with configuration
   */
  initializeUploader() {
    const uploadConfig = {
      bucketName: this.config.upload?.bucketName || "default-bucket",
      filePrefix: this.config.upload?.filePrefix || "video-analysis/",
      chunkSize: this.config.upload?.chunkSize,
      maxRetries: this.config.upload?.maxRetries,
      onProgress: (_progress) => {
      }
    };
    this.uploader = new VideoUploader(uploadConfig);
  }
  /**
   * Update configuration
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    if (config.upload && this.uploader) {
      this.initializeUploader();
    }
  }
  /**
   * Get current configuration
   */
  getConfig() {
    return { ...this.config };
  }
  /**
   * Get analysis statistics
   */
  getAnalysisStatistics(results) {
    const totalVideos = results.length;
    const totalProcessingTime = results.reduce((sum, r) => sum + r.processingTime, 0);
    const averageProcessingTime = totalVideos > 0 ? totalProcessingTime / totalVideos : 0;
    const totalScenes = results.reduce((sum, r) => sum + r.scenes.length, 0);
    const totalObjects = results.reduce((sum, r) => sum + r.objects.length, 0);
    const qualityScores = results.map((r) => r.qualityMetrics?.overallScore || 0).filter((score) => score > 0);
    const averageQualityScore = qualityScores.length > 0 ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length : 0;
    return {
      totalVideos,
      totalProcessingTime,
      averageProcessingTime,
      totalScenes,
      totalObjects,
      averageQualityScore
    };
  }
};
function createVideoAnalyzer(config) {
  return new VideoAnalyzer(config);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ANALYSIS_PROMPTS,
  AnalysisEngine,
  DEFAULT_FOLDER_MATCH_CONFIG,
  DEFAULT_SCAN_OPTIONS,
  DEFAULT_UPLOAD_CONFIG,
  DEFAULT_VIDEO_EXTENSIONS,
  FolderMatcher,
  FrameAnalyzer,
  PRODUCT_ANALYSIS_PROMPTS,
  ProductAnalyzer,
  ReportGenerator,
  VideoAnalyzer,
  VideoAnalyzerError,
  VideoScanner,
  VideoUploader,
  analyzeProductInVideo,
  analyzeVideoFrames,
  analyzeVideoWithGemini,
  createVideoAnalyzer,
  findMatchingFoldersForVideo,
  generateAnalysisReport,
  getVideoScanStatistics,
  scanVideoDirectory,
  uploadVideoToGemini,
  uploadVideosToGemini
});
