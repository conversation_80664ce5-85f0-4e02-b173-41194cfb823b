/**
 * Report generation for video analysis results
 */
import { VideoAnalysisResult, FolderMatchResult } from './types';
/**
 * Report generation options
 */
export interface ReportOptions {
    /** Output format */
    format: 'xml' | 'json' | 'csv' | 'html';
    /** Output file path */
    outputPath: string;
    /** Include thumbnails in report */
    includeThumbnails?: boolean;
    /** Include detailed analysis data */
    includeDetailedAnalysis?: boolean;
    /** Include folder matching results */
    includeFolderMatching?: boolean;
    /** Custom report title */
    title?: string;
    /** Additional metadata */
    metadata?: Record<string, any>;
}
/**
 * Report generator class
 */
export declare class ReportGenerator {
    /**
     * Generate comprehensive analysis report
     */
    generateReport(videoResults: VideoAnalysisResult[], folderMatches: Record<string, FolderMatchResult[]> | undefined, options: ReportOptions): Promise<string>;
    private getErrorMessage;
    /**
     * Build report data structure
     */
    private buildReport;
    /**
     * Generate XML report
     */
    private generateXMLReport;
    /**
     * Generate JSON report
     */
    private generateJSONReport;
    /**
     * Generate CSV report
     */
    private generateCSVReport;
    /**
     * Generate HTML report
     */
    private generateHTMLReport;
    /**
     * Ensure output directory exists
     */
    private ensureOutputDirectory;
    /**
     * Count occurrences of items in array
     */
    private countOccurrences;
    /**
     * Escape XML special characters
     */
    private escapeXML;
    /**
     * Escape HTML special characters
     */
    private escapeHTML;
    /**
     * Escape CSV special characters
     */
    private escapeCSV;
}
/**
 * Convenience function to generate report
 */
export declare function generateAnalysisReport(videoResults: VideoAnalysisResult[], folderMatches: Record<string, FolderMatchResult[]>, options: ReportOptions): Promise<string>;
//# sourceMappingURL=report-generator.d.ts.map