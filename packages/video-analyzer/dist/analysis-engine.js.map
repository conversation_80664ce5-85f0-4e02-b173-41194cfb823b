{"version": 3, "file": "analysis-engine.js", "sourceRoot": "", "sources": ["../src/analysis-engine.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAUL,kBAAkB,EACnB,MAAM,SAAS,CAAC;AAEjB;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,aAAa,EAAE;;;;;;;0CAOyB;IAExC,eAAe,EAAE;;;;;;;;qBAQE;IAEnB,eAAe,EAAE;;;;;;;kBAOD;IAEhB,gBAAgB,EAAE;;;;;;;kBAOF;CACjB,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,cAAc;IAGzB;QAFQ,iBAAY,GAAQ,IAAI,CAAC;QAG/B,gDAAgD;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,MAAM,SAAS,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,SAAoB,EACpB,OAAe,EACf,IAAkB,EAClB,UAA2B,EAAE,EAC7B,UAAiD;QAEjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,QAAQ,GAAqB;YACjC,IAAI,EAAE,uBAAuB;YAC7B,QAAQ,EAAE,CAAC;YACX,WAAW,EAAE,SAAS,CAAC,IAAI;YAC3B,KAAK,EAAE,YAAY;SACpB,CAAC;QAEF,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEpC,QAAQ,CAAC,IAAI,GAAG,4BAA4B,CAAC;YAC7C,QAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvB,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;YAEvB,6CAA6C;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAEtD,QAAQ,CAAC,IAAI,GAAG,yBAAyB,CAAC;YAC1C,QAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvB,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC;YAC5B,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;YAEvB,iCAAiC;YACjC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAC7D,OAAO,EACP,OAAO,EACP,OAAO,EACP,UAAU,CACX,CAAC;YAEF,QAAQ,CAAC,IAAI,GAAG,6BAA6B,CAAC;YAC9C,QAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvB,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;YAEvB,qBAAqB;YACrB,MAAM,MAAM,GAAwB;gBAClC,QAAQ,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;gBAClD,YAAY,EAAE,IAAI;gBAClB,MAAM,EAAE,eAAe,CAAC,MAAM,IAAI,EAAE;gBACpC,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,EAAE;gBACtC,eAAe,EAAE,eAAe,CAAC,eAAe;gBAChD,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC/D,aAAa,EAAE,eAAe,CAAC,aAAa;gBAC5C,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC;aAC9D,CAAC;YAEF,QAAQ,CAAC,IAAI,GAAG,oBAAoB,CAAC;YACrC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;YACxB,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC;YAC5B,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;YAEvB,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,kBAAkB,CAC1B,uBAAuB,SAAS,CAAC,IAAI,KAAK,YAAY,EAAE,EACxD,iBAAiB,EACjB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAwB;QACtD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B,CACxC,OAAe,EACf,OAAiB,EACjB,OAAwB,EACxB,UAAiD;QAEjD,MAAM,OAAO,GAAQ;YACnB,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,OAAO,EAAE,IAAI;YACb,eAAe,EAAE,IAAI;SACtB,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,eAAe,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAE7D,UAAU,EAAE,CAAC;gBACX,IAAI,EAAE,oBAAoB,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;gBACnD,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,UAAU;aACE,CAAC,CAAC;YAEvB,IAAI,CAAC;gBACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC9E,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,OAAe,EACf,MAAc,EACd,OAAwB;QAExB,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,QAAQ,GAAG;gBACf;oBACE,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,MAAM;yBACb;wBACD;4BACE,QAAQ,EAAE;gCACR,QAAQ,EAAE,WAAW,EAAE,sCAAsC;gCAC7D,OAAO,EAAE,QAAQ,OAAO,EAAE;6BAC3B;yBACF;qBACF;iBACF;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CACtD,QAAQ,EACR,kBAAkB,EAClB;gBACE,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,IAAI;gBACrB,IAAI,EAAE,GAAG;aACV,CACF,CAAC;YAEF,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBACjG,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC3E,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAClD,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAEtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,kBAAkB,CAC1B,2BAA2B,YAAY,EAAE,EACzC,wBAAwB,EACxB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,YAAoB;QAChD,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACpD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;YAED,yDAAyD;YACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,oDAAoD,EAAE,YAAY,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY;QACpC,MAAM,MAAM,GAAQ;YAClB,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,OAAO,EAAE;gBACP,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBACnC,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;aACb;SACF,CAAC;QAEF,+CAA+C;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACpE,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAClD,SAAS,EAAE,KAAK,GAAG,EAAE,EAAE,kBAAkB;gBACzC,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE;gBACzB,QAAQ,EAAE,EAAE;gBACZ,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;gBACrD,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC,CAAC;QACN,CAAC;QAED,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC/D,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC3C,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;gBAClD,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAW,EAAE,MAAW;QACnD,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAClC,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YACtD,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,SAAoB;QACnD,OAAO;YACL,IAAI,EAAE,SAAS;YACf,SAAS,EAAE;gBACT,KAAK,EAAE,SAAS;gBAChB,SAAS,EAAE,SAAS,CAAC,MAAM,IAAI,SAAS;gBACxC,QAAQ,EAAE,IAAI,EAAE,mBAAmB;gBACnC,UAAU,EAAE,SAAS;aACtB;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,SAAS,CAAC,IAAI;gBACrB,WAAW,EAAE,sBAAsB,SAAS,CAAC,IAAI,EAAE;gBACnD,IAAI,EAAE,EAAE;aACT;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,OAAO;YACL,WAAW,EAAE,kCAAkC;YAC/C,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAY;QAK1C,IAAI,YAAY,GAAG,GAAG,CAAC,CAAC,aAAa;QACrC,IAAI,iBAAiB,GAAG,GAAG,CAAC;QAC5B,IAAI,aAAa,GAAG,GAAG,CAAC;QAExB,0CAA0C;QAC1C,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,YAAY,IAAI,GAAG,CAAC;YACpB,iBAAiB,IAAI,GAAG,CAAC;QAC3B,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,YAAY,IAAI,GAAG,CAAC;YACpB,iBAAiB,IAAI,GAAG,CAAC;QAC3B,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACnD,YAAY,IAAI,GAAG,CAAC;YACpB,aAAa,IAAI,GAAG,CAAC;QACvB,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,aAAa,IAAI,GAAG,CAAC;QACvB,CAAC;QAED,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC;YACzC,iBAAiB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC;YACnD,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC;SAC5C,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAC1C,SAAoB,EACpB,OAAe,EACf,IAAkB,EAClB,OAAyB,EACzB,UAAiD;IAEjD,MAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;IACpC,OAAO,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AAC5E,CAAC"}