import { GeminiUploadResult } from '@mixvideo/gemini';

/**
 * Core types and interfaces for video analysis
 */
interface VideoFile {
    path: string;
    name: string;
    size: number;
    duration?: number;
    format?: string;
    resolution?: {
        width: number;
        height: number;
    };
    frameRate?: number;
    bitrate?: number;
    createdAt?: Date;
    modifiedAt?: Date;
}
interface VideoScanOptions {
    /** Supported video extensions to scan for */
    extensions?: string[];
    /** Whether to scan subdirectories recursively */
    recursive?: boolean;
    /** Maximum file size in bytes (default: 500MB) */
    maxFileSize?: number;
    /** Minimum file size in bytes (default: 1KB) */
    minFileSize?: number;
    /** Progress callback for scanning */
    onProgress?: (progress: ScanProgress) => void;
}
interface ScanProgress {
    /** Current step being performed */
    step: string;
    /** Progress percentage (0-100) */
    progress: number;
    /** Current file being processed */
    currentFile?: string;
    /** Total files found so far */
    filesFound: number;
    /** Total directories scanned */
    directoriesScanned: number;
}
interface UploadProgress {
    /** Current step being performed */
    step: string;
    /** Progress percentage (0-100) */
    progress: number;
    /** Current file being uploaded */
    currentFile?: string;
    /** Bytes uploaded */
    bytesUploaded: number;
    /** Total bytes to upload */
    totalBytes: number;
    /** Upload speed in bytes/second */
    uploadSpeed?: number;
}
interface AnalysisMode {
    /** Analysis mode type */
    type: 'gemini' | 'gpt4';
    /** Model configuration */
    model?: string;
    /** Analysis options */
    options?: AnalysisOptions;
}
interface AnalysisOptions {
    /** Enable scene detection */
    enableSceneDetection?: boolean;
    /** Enable object detection */
    enableObjectDetection?: boolean;
    /** Enable product feature analysis */
    enableProductAnalysis?: boolean;
    /** Enable content summarization */
    enableSummarization?: boolean;
    /** Frame sampling interval in seconds (for GPT-4 mode) */
    frameSamplingInterval?: number;
    /** Maximum number of frames to analyze */
    maxFrames?: number;
    /** Analysis quality level */
    quality?: 'low' | 'medium' | 'high';
    /** Language for analysis results */
    language?: string;
    /** Custom analysis prompts */
    customPrompts?: string[];
}
interface AnalysisProgress {
    /** Current step being performed */
    step: string;
    /** Progress percentage (0-100) */
    progress: number;
    /** Current file being analyzed */
    currentFile?: string;
    /** Current frame being processed (for GPT-4 mode) */
    currentFrame?: number;
    /** Total frames to process */
    totalFrames?: number;
    /** Analysis stage */
    stage: 'upload' | 'processing' | 'analysis' | 'complete';
}
interface VideoMetadata {
    /** Video file information */
    file: VideoFile;
    /** Technical metadata */
    technical: {
        codec: string;
        container: string;
        hasAudio: boolean;
        audioCodec?: string;
        channels?: number;
        sampleRate?: number;
    };
    /** Content metadata */
    content?: {
        title?: string;
        description?: string;
        tags?: string[];
        thumbnail?: string;
    };
}
interface SceneDetection {
    /** Scene start time in seconds */
    startTime: number;
    /** Scene end time in seconds */
    endTime: number;
    /** Scene duration in seconds */
    duration: number;
    /** Scene description */
    description: string;
    /** Scene type/category */
    type?: string;
    /** Confidence score (0-1) */
    confidence: number;
    /** Key objects in scene */
    objects?: string[];
    /** Scene thumbnail timestamp */
    thumbnailTime?: number;
}
interface ObjectDetection {
    /** Object name/label */
    name: string;
    /** Object category */
    category: string;
    /** Confidence score (0-1) */
    confidence: number;
    /** Bounding box coordinates (if available) */
    boundingBox?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    /** Time range when object appears */
    timeRange?: {
        start: number;
        end: number;
    };
    /** Additional attributes */
    attributes?: Record<string, any>;
}
interface ProductFeatures {
    /** Product appearance details */
    appearance: {
        colors: string[];
        shape: string;
        size: string;
        style: string;
    };
    /** Material analysis */
    materials: string[];
    /** Functional features */
    functionality: string[];
    /** Usage scenarios */
    usageScenarios: string[];
    /** Target audience */
    targetAudience?: string;
    /** Brand elements */
    brandElements?: string[];
}
interface ContentSummary {
    /** Overall description */
    description: string;
    /** Key highlights */
    highlights: string[];
    /** Main topics/themes */
    topics: string[];
    /** Emotional tone */
    tone?: string;
    /** Content category */
    category?: string;
    /** Keywords */
    keywords: string[];
}
interface FrameAnalysis {
    /** Frame timestamp in seconds */
    timestamp: number;
    /** Frame description */
    description: string;
    /** Objects detected in frame */
    objects: ObjectDetection[];
    /** Frame quality score */
    quality: number;
    /** Frame type (keyframe, etc.) */
    type?: string;
}
interface VideoAnalysisResult {
    /** Video metadata */
    metadata: VideoMetadata;
    /** Analysis mode used */
    analysisMode: AnalysisMode;
    /** Scene detection results */
    scenes: SceneDetection[];
    /** Object detection results */
    objects: ObjectDetection[];
    /** Product feature analysis */
    productFeatures?: ProductFeatures;
    /** Content summary */
    summary: ContentSummary;
    /** Frame-by-frame analysis (GPT-4 mode) */
    frameAnalysis?: FrameAnalysis[];
    /** Analysis timestamp */
    analyzedAt: Date;
    /** Processing time in milliseconds */
    processingTime: number;
    /** Analysis quality metrics */
    qualityMetrics?: {
        overallScore: number;
        detectionAccuracy: number;
        analysisDepth: number;
    };
}
interface FolderMatchResult {
    /** Folder path */
    folderPath: string;
    /** Folder name */
    folderName: string;
    /** Match confidence score (0-1) */
    confidence: number;
    /** Matching reasons */
    reasons: string[];
    /** Semantic similarity score */
    semanticScore: number;
    /** Content relevance score */
    relevanceScore: number;
    /** Recommended action */
    action: 'move' | 'copy' | 'link' | 'ignore';
}
interface AnalysisReport {
    /** Report metadata */
    metadata: {
        generatedAt: Date;
        version: string;
        totalVideos: number;
        totalProcessingTime: number;
    };
    /** Individual video results */
    videoResults: VideoAnalysisResult[];
    /** Folder matching results */
    folderMatches?: Record<string, FolderMatchResult[]>;
    /** Summary statistics */
    summary: {
        totalScenes: number;
        totalObjects: number;
        commonThemes: string[];
        recommendedCategories: string[];
    };
    /** Export options */
    exportOptions?: {
        format: 'xml' | 'json' | 'csv' | 'html';
        includeImages: boolean;
        includeThumbnails: boolean;
    };
}
interface VideoAnalyzerConfig {
    /** Gemini API configuration */
    gemini?: {
        accessToken?: string;
        cloudflareProjectId?: string;
        cloudflareGatewayId?: string;
        googleProjectId?: string;
        regions?: string[];
    };
    /** Upload configuration */
    upload?: {
        bucketName?: string;
        filePrefix?: string;
        chunkSize?: number;
        maxRetries?: number;
    };
    /** Analysis configuration */
    analysis?: {
        defaultMode?: 'gemini' | 'gpt4';
        defaultOptions?: AnalysisOptions;
        maxConcurrentAnalysis?: number;
    };
    /** Logging configuration */
    logging?: {
        enabled: boolean;
        level: 'debug' | 'info' | 'warn' | 'error';
        outputFile?: string;
    };
}
declare class VideoAnalyzerError extends Error {
    code: string;
    details?: any;
    file?: string;
    stage?: string;
    constructor(message: string, code: string, details?: any, file?: string, stage?: string);
}

/**
 * Video file scanner and validation utilities
 */

/**
 * Default supported video file extensions
 */
declare const DEFAULT_VIDEO_EXTENSIONS: string[];
/**
 * Default scan options
 */
declare const DEFAULT_SCAN_OPTIONS: Required<VideoScanOptions>;
/**
 * Video file scanner class
 */
declare class VideoScanner {
    private options;
    constructor(options?: VideoScanOptions);
    /**
     * Scan a directory for video files
     */
    scanDirectory(directoryPath: string): Promise<VideoFile[]>;
    /**
     * Recursively scan directory for video files
     */
    private scanDirectoryRecursive;
    /**
     * Check if a file is a video file based on extension (synchronous)
     */
    isVideoFile(filePath: string): boolean;
    /**
     * Check if a file is a video file based on extension (async version for compatibility)
     */
    isVideoFileAsync(filePath: string): Promise<boolean>;
    /**
     * Create VideoFile object from file path
     */
    createVideoFile(filePath: string): Promise<VideoFile>;
    /**
     * Validate video file against size constraints
     */
    isValidVideoFile(videoFile: VideoFile): boolean;
    /**
     * Check if directory exists and is accessible
     */
    isValidDirectory(dirPath: string): Promise<boolean>;
    /**
     * Get video file metadata using basic file system info
     */
    getBasicMetadata(videoFile: VideoFile): Promise<VideoFile>;
    /**
     * Validate multiple video files
     */
    validateVideoFiles(videoFiles: VideoFile[]): Promise<{
        valid: VideoFile[];
        invalid: Array<{
            file: VideoFile;
            reason: string;
        }>;
    }>;
    /**
     * Check if file exists
     */
    private fileExists;
    /**
     * Get scan statistics
     */
    getScanStatistics(directoryPath: string): Promise<{
        totalFiles: number;
        totalVideoFiles: number;
        totalSize: number;
        averageSize: number;
        formatDistribution: Record<string, number>;
    }>;
    /**
     * Update scan options
     */
    updateOptions(options: Partial<VideoScanOptions>): void;
    /**
     * Get current scan options
     */
    getOptions(): VideoScanOptions;
}
/**
 * Convenience function to scan directory with default options
 */
declare function scanVideoDirectory(directoryPath: string, options?: VideoScanOptions): Promise<VideoFile[]>;
/**
 * Convenience function to get video scan statistics
 */
declare function getVideoScanStatistics(directoryPath: string, options?: VideoScanOptions): Promise<{
    totalFiles: number;
    totalVideoFiles: number;
    totalSize: number;
    averageSize: number;
    formatDistribution: Record<string, number>;
}>;

/**
 * Video file uploader for Gemini AI
 */

/**
 * Upload cache entry
 */
interface UploadCacheEntry {
    /** Original video file information */
    videoFile: VideoFile;
    /** Upload result from Gemini */
    result: GeminiUploadResult;
    /** Timestamp when cached */
    timestamp: number;
    /** File checksum for validation */
    checksum: string;
}
/**
 * Upload configuration options
 */
interface UploadConfig {
    /** GCS bucket name for uploads */
    bucketName: string;
    /** File prefix for organization */
    filePrefix: string;
    /** Chunk size for large file uploads (bytes) */
    chunkSize?: number;
    /** Maximum number of retry attempts */
    maxRetries?: number;
    /** Timeout for upload operations (ms) */
    timeout?: number;
    /** Progress callback */
    onProgress?: (progress: UploadProgress) => void;
    /** Enable local cache to avoid duplicate uploads */
    enableCache?: boolean;
    /** Cache directory path */
    cacheDir?: string;
    /** Cache expiry time in milliseconds (default: 24 hours) */
    cacheExpiry?: number;
}
/**
 * Default upload configuration
 */
declare const DEFAULT_UPLOAD_CONFIG: Required<Omit<UploadConfig, 'bucketName' | 'filePrefix'>>;
/**
 * Upload result information
 */
interface UploadResult {
    /** Original video file */
    videoFile: VideoFile;
    /** Upload success status */
    success: boolean;
    /** GCS file path */
    gcsPath?: string;
    /** Upload duration in milliseconds */
    uploadTime: number;
    /** File size uploaded */
    bytesUploaded: number;
    /** Error information if upload failed */
    error?: string;
    /** Upload metadata */
    metadata?: {
        uploadId: string;
        timestamp: Date;
        checksum?: string;
    };
}
/**
 * Video uploader class
 */
declare class VideoUploader {
    private config;
    constructor(config: UploadConfig);
    /**
     * Upload a single video file to Gemini
     */
    uploadVideo(videoFile: VideoFile): Promise<UploadResult>;
    private getErrorMessage;
    /**
     * Upload multiple video files
     */
    uploadVideos(videoFiles: VideoFile[]): Promise<UploadResult[]>;
    /**
     * Validate file before upload
     */
    private validateFileForUpload;
    /**
     * Read video file into buffer
     */
    private readVideoFile;
    private getMimeType;
    /**
     * 将上传结果保存到本地缓存
     */
    private saveToLocalDb;
    /**
     * 从本地缓存检查是否已上传
     */
    private checkLocalCache;
    /**
     * 确保缓存目录存在
     */
    private ensureCacheDir;
    /**
     * 生成缓存键
     */
    private generateCacheKey;
    /**
     * 清理过期的缓存文件
     */
    cleanExpiredCache(): Promise<void>;
    /**
     * 获取缓存统计信息
     */
    getCacheStats(): Promise<{
        totalFiles: number;
        totalSize: number;
        oldestEntry: Date | null;
        newestEntry: Date | null;
    }>;
    /**
     * Upload with retry logic
     */
    private uploadWithRetry;
    /**
     * Generate GCS path for video file
     */
    private generateGcsPath;
    /**
     * Generate unique upload ID
     */
    private generateUploadId;
    /**
     * Calculate file checksum for verification
     */
    private calculateChecksum;
    /**
     * Get upload statistics
     */
    getUploadStatistics(results: UploadResult[]): {
        totalFiles: number;
        successfulUploads: number;
        failedUploads: number;
        totalBytesUploaded: number;
        averageUploadTime: number;
        successRate: number;
    };
    /**
     * Update upload configuration
     */
    updateConfig(config: Partial<UploadConfig>): void;
    /**
     * Get current upload configuration
     */
    getConfig(): UploadConfig;
}
/**
 * Convenience function to upload a single video
 */
declare function uploadVideoToGemini(videoFile: VideoFile, config: UploadConfig): Promise<UploadResult>;
/**
 * Convenience function to upload multiple videos
 */
declare function uploadVideosToGemini(videoFiles: VideoFile[], config: UploadConfig): Promise<UploadResult[]>;

/**
 * Core video analysis engine using Gemini AI
 */

/**
 * Analysis cache entry
 */
interface AnalysisCacheEntry {
    /** Video file path used as key */
    videoPath: string;
    /** GCS path of the uploaded video */
    gcsPath: string;
    /** Analysis prompt used */
    prompt: string;
    /** Analysis options used */
    options: AnalysisOptions;
    /** Analysis result */
    result: any;
    /** Timestamp when cached */
    timestamp: number;
    /** File checksum for validation */
    checksum: string;
}
/**
 * Analysis cache configuration
 */
interface AnalysisCacheConfig {
    /** Enable analysis result caching */
    enableCache?: boolean;
    /** Cache directory path */
    cacheDir?: string;
    /** Cache expiry time in milliseconds (default: 7 days) */
    cacheExpiry?: number;
}
/**
 * Default analysis cache configuration
 */
declare const DEFAULT_ANALYSIS_CACHE_CONFIG: Required<AnalysisCacheConfig>;
/**
 * Analysis prompts for different types of content analysis
 */
declare const ANALYSIS_PROMPTS: {
    COMPREHENSIVE: string;
    PRODUCT_FOCUSED: string;
    SCENE_DETECTION: string;
    OBJECT_DETECTION: string;
};
/**
 * Video analysis engine class
 */
declare class AnalysisEngine {
    private geminiClient;
    private cacheConfig;
    constructor(cacheConfig?: AnalysisCacheConfig);
    /**
     * Initialize Gemini client
     */
    private initializeGeminiClient;
    /**
     * Analyze video using Gemini AI
     */
    analyzeVideo(videoFile: VideoFile, gcsPath: string, mode: AnalysisMode, options?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult>;
    /**
     * Generate analysis prompts based on options
     */
    private generateAnalysisPrompts;
    /**
     * Perform comprehensive analysis using multiple prompts
     */
    private performComprehensiveAnalysis;
    /**
     * Run a single analysis with a specific prompt
     */
    private runSingleAnalysis;
    /**
     * Parse analysis response from Gemini
     */
    private parseAnalysisResponse;
    /**
     * Parse text response when JSON parsing fails
     */
    private parseTextResponse;
    /**
     * Merge multiple analysis results
     */
    private mergeAnalysisResults;
    /**
     * Build video metadata
     */
    private buildVideoMetadata;
    /**
     * Create default summary when none is provided
     */
    private createDefaultSummary;
    /**
     * Calculate quality metrics for analysis results
     */
    private calculateQualityMetrics;
    /**
     * 确保缓存目录存在
     */
    private ensureCacheDir;
    /**
     * 生成缓存键
     */
    private generateAnalysisCacheKey;
    /**
     * 计算文件校验和
     */
    private calculateFileChecksum;
    /**
     * 检查分析缓存
     */
    private checkAnalysisCache;
    /**
     * 保存分析结果到缓存
     */
    private saveAnalysisCache;
    /**
     * 清理过期的分析缓存
     */
    cleanExpiredAnalysisCache(): Promise<void>;
    /**
     * 获取分析缓存统计信息
     */
    getAnalysisCacheStats(): Promise<{
        totalFiles: number;
        totalSize: number;
        oldestEntry: Date | null;
        newestEntry: Date | null;
    }>;
}
/**
 * Convenience function to analyze a video
 */
declare function analyzeVideoWithGemini(videoFile: VideoFile, gcsPath: string, mode: AnalysisMode, options?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void, cacheConfig?: AnalysisCacheConfig): Promise<VideoAnalysisResult>;

/**
 * Specialized product feature analysis for e-commerce and marketing videos
 */

/**
 * Product analysis prompts for different aspects
 */
declare const PRODUCT_ANALYSIS_PROMPTS: {
    APPEARANCE: string;
    MATERIALS: string;
    FUNCTIONALITY: string;
    USAGE_SCENARIOS: string;
    BRAND_ELEMENTS: string;
    ATMOSPHERE: string;
};
/**
 * Product feature analyzer class
 */
declare class ProductAnalyzer {
    private geminiClient;
    constructor();
    /**
     * Initialize Gemini client
     */
    private initializeGeminiClient;
    /**
     * Analyze product features in video
     */
    analyzeProductFeatures(videoFile: VideoFile, gcsPath: string, options?: AnalysisOptions): Promise<ProductFeatures>;
    private getErrorMessage;
    /**
     * Analyze product appearance
     */
    private analyzeAppearance;
    /**
     * Analyze product materials
     */
    private analyzeMaterials;
    /**
     * Analyze product functionality
     */
    private analyzeFunctionality;
    /**
     * Analyze usage scenarios
     */
    private analyzeUsageScenarios;
    /**
     * Analyze brand elements
     */
    private analyzeBrandElements;
    /**
     * Analyze video atmosphere
     */
    private analyzeAtmosphere;
    /**
     * Run a single product analysis
     */
    private runProductAnalysis;
    /**
     * Parse product analysis response
     */
    private parseProductAnalysisResponse;
    /**
     * Parse text response for product analysis
     */
    private parseProductTextResponse;
    /**
     * Combine all product analysis results
     */
    private combineProductAnalysis;
    /**
     * Analyze product for e-commerce categorization
     */
    analyzeForEcommerce(videoFile: VideoFile, gcsPath: string): Promise<{
        category: string;
        subcategory: string;
        tags: string[];
        priceRange: string;
        targetMarket: string;
    }>;
    /**
     * Generate product description from analysis
     */
    generateProductDescription(features: ProductFeatures): string;
}
/**
 * Convenience function to analyze product features
 */
declare function analyzeProductInVideo(videoFile: VideoFile, gcsPath: string, options?: AnalysisOptions): Promise<ProductFeatures>;

/**
 * Smart folder matching system for video categorization
 */

/**
 * Folder matching configuration
 */
interface FolderMatchConfig {
    /** Base directory to scan for folders */
    baseDirectory: string;
    /** Maximum depth to scan for folders */
    maxDepth?: number;
    /** Minimum confidence score to include in results */
    minConfidence?: number;
    /** Maximum number of matches to return */
    maxMatches?: number;
    /** Whether to include semantic analysis */
    enableSemanticAnalysis?: boolean;
}
/**
 * Default folder match configuration
 */
declare const DEFAULT_FOLDER_MATCH_CONFIG: Required<Omit<FolderMatchConfig, 'baseDirectory'>>;
/**
 * Smart folder matcher class
 */
declare class FolderMatcher {
    private config;
    private geminiClient;
    private folderCache;
    constructor(config: FolderMatchConfig);
    /**
     * Initialize Gemini client
     */
    private initializeGeminiClient;
    /**
     * Find matching folders for video analysis result
     */
    findMatchingFolders(analysisResult: VideoAnalysisResult): Promise<FolderMatchResult[]>;
    /**
     * Scan for available folders
     */
    private scanFolders;
    /**
     * Recursively scan folders
     */
    private scanFoldersRecursive;
    /**
     * Generate content description for matching
     */
    private generateContentDescription;
    /**
     * Perform folder matching using AI analysis
     */
    private performFolderMatching;
    /**
     * Perform rule-based folder matching
     */
    private performRuleBasedMatching;
    /**
     * Calculate rule-based confidence score
     */
    private calculateRuleBasedConfidence;
    /**
     * Generate reasons for rule-based matching
     */
    private generateRuleBasedReasons;
    /**
     * Perform semantic matching using Gemini AI
     */
    private performSemanticMatching;
    /**
     * Parse semantic matching response
     */
    private parseSemanticMatchingResponse;
    private getErrorMessage;
    /**
     * Merge and deduplicate matches
     */
    private mergeMatches;
    /**
     * Determine recommended action based on confidence
     */
    private determineAction;
    /**
     * Extract keywords from content
     */
    private extractKeywords;
    /**
     * Extract categories from content
     */
    private extractCategories;
    /**
     * Extract colors from content
     */
    private extractColors;
    /**
     * Clear folder cache
     */
    clearCache(): void;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<FolderMatchConfig>): void;
}
/**
 * Convenience function to find matching folders
 */
declare function findMatchingFoldersForVideo(analysisResult: VideoAnalysisResult, config: FolderMatchConfig): Promise<FolderMatchResult[]>;

/**
 * Report generation for video analysis results
 */

/**
 * Report generation options
 */
interface ReportOptions {
    /** Output format */
    format: 'xml' | 'json' | 'csv' | 'html';
    /** Output file path */
    outputPath: string;
    /** Include thumbnails in report */
    includeThumbnails?: boolean;
    /** Include detailed analysis data */
    includeDetailedAnalysis?: boolean;
    /** Include folder matching results */
    includeFolderMatching?: boolean;
    /** Custom report title */
    title?: string;
    /** Additional metadata */
    metadata?: Record<string, any>;
}
/**
 * Report generator class
 */
declare class ReportGenerator {
    /**
     * Generate comprehensive analysis report
     */
    generateReport(videoResults: VideoAnalysisResult[], folderMatches: Record<string, FolderMatchResult[]> | undefined, options: ReportOptions): Promise<string>;
    private getErrorMessage;
    /**
     * Build report data structure
     */
    private buildReport;
    /**
     * Generate XML report
     */
    private generateXMLReport;
    /**
     * Generate JSON report
     */
    private generateJSONReport;
    /**
     * Generate CSV report
     */
    private generateCSVReport;
    /**
     * Generate HTML report
     */
    private generateHTMLReport;
    /**
     * Ensure output directory exists
     */
    private ensureOutputDirectory;
    /**
     * Count occurrences of items in array
     */
    private countOccurrences;
    /**
     * Escape XML special characters
     */
    private escapeXML;
    /**
     * Escape HTML special characters
     */
    private escapeHTML;
    /**
     * Escape CSV special characters
     */
    private escapeCSV;
}
/**
 * Convenience function to generate report
 */
declare function generateAnalysisReport(videoResults: VideoAnalysisResult[], folderMatches: Record<string, FolderMatchResult[]>, options: ReportOptions): Promise<string>;

/**
 * GPT-4 frame analysis mode (placeholder implementation)
 * This module provides frame extraction and analysis functionality
 */

/**
 * Frame analyzer class for GPT-4 mode
 */
declare class FrameAnalyzer {
    /**
     * Extract and analyze frames from video
     * Note: This is a placeholder implementation
     * In a real-world scenario, you would use ffmpeg or similar tools
     */
    analyzeFrames(videoFile: VideoFile, options?: AnalysisOptions): Promise<FrameAnalysis[]>;
    private getErrorMessage;
    /**
     * Extract key frames from video
     * Note: This is a placeholder implementation
     */
    extractKeyFrames(_videoFile: VideoFile, interval?: number): Promise<{
        timestamp: number;
        quality: number;
    }[]>;
    /**
     * Analyze single frame
     * Note: This would integrate with GPT-4 Vision API in a real implementation
     */
    analyzeSingleFrame(_frameData: Buffer, timestamp: number): Promise<FrameAnalysis>;
}
/**
 * Convenience function to analyze video frames
 */
declare function analyzeVideoFrames(videoFile: VideoFile, options?: AnalysisOptions): Promise<FrameAnalysis[]>;

/**
 * Main VideoAnalyzer class - orchestrates all video analysis functionality
 */

/**
 * Main VideoAnalyzer class
 */
declare class VideoAnalyzer {
    private config;
    private scanner;
    private uploader;
    private analysisEngine;
    private productAnalyzer;
    private folderMatcher;
    private reportGenerator;
    private frameAnalyzer;
    constructor(config?: VideoAnalyzerConfig);
    /**
     * Scan directory for video files
     */
    scanDirectory(directoryPath: string, options?: VideoScanOptions): Promise<VideoFile[]>;
    /**
     * Analyze a single video file
     */
    analyzeVideo(videoFile: VideoFile, mode: AnalysisMode, options?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult>;
    /**
     * Analyze multiple videos in a directory
     */
    analyzeDirectory(directoryPath: string, mode: AnalysisMode, scanOptions?: VideoScanOptions, analysisOptions?: AnalysisOptions, onProgress?: (progress: AnalysisProgress) => void): Promise<VideoAnalysisResult[]>;
    /**
     * Find matching folders for analysis results
     */
    findMatchingFolders(analysisResults: VideoAnalysisResult[], folderConfig: FolderMatchConfig): Promise<Record<string, FolderMatchResult[]>>;
    /**
     * Generate analysis report
     */
    generateReport(analysisResults: VideoAnalysisResult[], folderMatches: Record<string, FolderMatchResult[]> | undefined, reportOptions: ReportOptions): Promise<string>;
    /**
     * Complete workflow: scan, analyze, match folders, and generate report
     */
    analyzeDirectoryComplete(directoryPath: string, mode: AnalysisMode, options?: {
        scanOptions?: VideoScanOptions;
        analysisOptions?: AnalysisOptions;
        folderConfig?: FolderMatchConfig;
        reportOptions?: ReportOptions;
        onProgress?: (progress: AnalysisProgress) => void;
    }): Promise<{
        analysisResults: VideoAnalysisResult[];
        folderMatches: Record<string, FolderMatchResult[]>;
        reportPath?: string;
    }>;
    /**
     * Initialize uploader with configuration
     */
    private initializeUploader;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<VideoAnalyzerConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): VideoAnalyzerConfig;
    /**
     * Get analysis statistics
     */
    getAnalysisStatistics(results: VideoAnalysisResult[]): {
        totalVideos: number;
        totalProcessingTime: number;
        averageProcessingTime: number;
        totalScenes: number;
        totalObjects: number;
        averageQualityScore: number;
    };
}
/**
 * Convenience function to create VideoAnalyzer instance
 */
declare function createVideoAnalyzer(config?: VideoAnalyzerConfig): VideoAnalyzer;

export { ANALYSIS_PROMPTS, type AnalysisCacheConfig, type AnalysisCacheEntry, AnalysisEngine, type AnalysisMode, type AnalysisOptions, type AnalysisProgress, type AnalysisReport, type ContentSummary, DEFAULT_ANALYSIS_CACHE_CONFIG, DEFAULT_FOLDER_MATCH_CONFIG, DEFAULT_SCAN_OPTIONS, DEFAULT_UPLOAD_CONFIG, DEFAULT_VIDEO_EXTENSIONS, type FolderMatchConfig, type FolderMatchResult, FolderMatcher, type FrameAnalysis, FrameAnalyzer, type ObjectDetection, PRODUCT_ANALYSIS_PROMPTS, ProductAnalyzer, type ProductFeatures, ReportGenerator, type ReportOptions, type ScanProgress, type SceneDetection, type UploadCacheEntry, type UploadConfig, type UploadProgress, type UploadResult, type VideoAnalysisResult, VideoAnalyzer, type VideoAnalyzerConfig, VideoAnalyzerError, type VideoFile, type VideoMetadata, type VideoScanOptions, VideoScanner, VideoUploader, analyzeProductInVideo, analyzeVideoFrames, analyzeVideoWithGemini, createVideoAnalyzer, findMatchingFoldersForVideo, generateAnalysisReport, getVideoScanStatistics, scanVideoDirectory, uploadVideoToGemini, uploadVideosToGemini };
