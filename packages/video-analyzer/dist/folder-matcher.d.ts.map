{"version": 3, "file": "folder-matcher.d.ts", "sourceRoot": "", "sources": ["../src/folder-matcher.ts"], "names": [], "mappings": "AAAA;;GAEG;AAMH,OAAO,EACL,mBAAmB,EACnB,iBAAiB,EAElB,MAAM,SAAS,CAAC;AAKjB;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC,yCAAyC;IACzC,aAAa,EAAE,MAAM,CAAC;IACtB,wCAAwC;IACxC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,qDAAqD;IACrD,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,0CAA0C;IAC1C,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,2CAA2C;IAC3C,sBAAsB,CAAC,EAAE,OAAO,CAAC;CAClC;AAED;;GAEG;AACH,eAAO,MAAM,2BAA2B,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAK1F,CAAC;AAEF;;GAEG;AACH,qBAAa,aAAa;IACxB,OAAO,CAAC,MAAM,CAA8B;IAC5C,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,WAAW,CAAoC;gBAE3C,MAAM,EAAE,iBAAiB;IAIrC;;OAEG;YACW,sBAAsB;IAMpC;;OAEG;IACG,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;IA0B5F;;OAEG;YACW,WAAW;IAczB;;OAEG;YACW,oBAAoB;IA0BlC;;OAEG;IACH,OAAO,CAAC,0BAA0B;IAsDlC;;OAEG;YACW,qBAAqB;IAoBnC;;OAEG;IACH,OAAO,CAAC,wBAAwB;IA2BhC;;OAEG;IACH,OAAO,CAAC,4BAA4B;IA8BpC;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAoBhC;;OAEG;YACW,uBAAuB;IAgDrC;;OAEG;IACH,OAAO,CAAC,6BAA6B;IAiCrC,OAAO,CAAC,eAAe;IAIvB;;OAEG;IACH,OAAO,CAAC,YAAY;IAqBpB;;OAEG;IACH,OAAO,CAAC,eAAe;IAOvB;;OAEG;IACH,OAAO,CAAC,eAAe;IAcvB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAwBzB;;OAEG;IACH,OAAO,CAAC,aAAa;IAKrB;;OAEG;IACH,UAAU,IAAI,IAAI;IAIlB;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI;CAIvD;AAED;;GAEG;AACH,wBAAsB,2BAA2B,CAC/C,cAAc,EAAE,mBAAmB,EACnC,MAAM,EAAE,iBAAiB,GACxB,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAG9B"}