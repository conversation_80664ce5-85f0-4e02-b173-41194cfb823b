/**
 * Specialized product feature analysis for e-commerce and marketing videos
 */
import { VideoFile, ProductFeatures, AnalysisOptions } from './types';
/**
 * Product analysis prompts for different aspects
 */
export declare const PRODUCT_ANALYSIS_PROMPTS: {
    APPEARANCE: string;
    MATERIALS: string;
    FUNCTIONALITY: string;
    USAGE_SCENARIOS: string;
    BRAND_ELEMENTS: string;
    ATMOSPHERE: string;
};
/**
 * Product feature analyzer class
 */
export declare class ProductAnalyzer {
    private geminiClient;
    constructor();
    /**
     * Initialize Gemini client
     */
    private initializeGeminiClient;
    /**
     * Analyze product features in video
     */
    analyzeProductFeatures(videoFile: VideoFile, gcsPath: string, options?: AnalysisOptions): Promise<ProductFeatures>;
    /**
     * Analyze product appearance
     */
    private analyzeAppearance;
    /**
     * Analyze product materials
     */
    private analyzeMaterials;
    /**
     * Analyze product functionality
     */
    private analyzeFunctionality;
    /**
     * Analyze usage scenarios
     */
    private analyzeUsageScenarios;
    /**
     * Analyze brand elements
     */
    private analyzeBrandElements;
    /**
     * Analyze video atmosphere
     */
    private analyzeAtmosphere;
    /**
     * Run a single product analysis
     */
    private runProductAnalysis;
    /**
     * Parse product analysis response
     */
    private parseProductAnalysisResponse;
    /**
     * Parse text response for product analysis
     */
    private parseProductTextResponse;
    /**
     * Combine all product analysis results
     */
    private combineProductAnalysis;
    /**
     * Analyze product for e-commerce categorization
     */
    analyzeForEcommerce(videoFile: VideoFile, gcsPath: string): Promise<{
        category: string;
        subcategory: string;
        tags: string[];
        priceRange: string;
        targetMarket: string;
    }>;
    /**
     * Generate product description from analysis
     */
    generateProductDescription(features: ProductFeatures): string;
}
/**
 * Convenience function to analyze product features
 */
export declare function analyzeProductInVideo(videoFile: VideoFile, gcsPath: string, options?: AnalysisOptions): Promise<ProductFeatures>;
//# sourceMappingURL=product-analyzer.d.ts.map