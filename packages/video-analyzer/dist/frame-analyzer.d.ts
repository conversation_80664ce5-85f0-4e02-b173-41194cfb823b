/**
 * GPT-4 frame analysis mode (placeholder implementation)
 * This module provides frame extraction and analysis functionality
 */
import { VideoFile, FrameAnalysis, AnalysisOptions } from './types';
/**
 * Frame analyzer class for GPT-4 mode
 */
export declare class FrameAnalyzer {
    /**
     * Extract and analyze frames from video
     * Note: This is a placeholder implementation
     * In a real-world scenario, you would use ffmpeg or similar tools
     */
    analyzeFrames(videoFile: VideoFile, options?: AnalysisOptions): Promise<FrameAnalysis[]>;
    private getErrorMessage;
    /**
     * Extract key frames from video
     * Note: This is a placeholder implementation
     */
    extractKeyFrames(_videoFile: VideoFile, interval?: number): Promise<{
        timestamp: number;
        quality: number;
    }[]>;
    /**
     * Analyze single frame
     * Note: This would integrate with GPT-4 Vision API in a real implementation
     */
    analyzeSingleFrame(_frameData: Buffer, timestamp: number): Promise<FrameAnalysis>;
}
/**
 * Convenience function to analyze video frames
 */
export declare function analyzeVideoFrames(videoFile: VideoFile, options?: AnalysisOptions): Promise<FrameAnalysis[]>;
//# sourceMappingURL=frame-analyzer.d.ts.map