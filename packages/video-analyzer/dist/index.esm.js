import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import axios from 'axios';

/**
 * Video file scanner and validation utilities
 */
const stat$1 = promisify(fs.stat);
const readdir$1 = promisify(fs.readdir);
/**
 * Default supported video file extensions
 */
const DEFAULT_VIDEO_EXTENSIONS = [
    '.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'
];
/**
 * Default scan options
 */
const DEFAULT_SCAN_OPTIONS = {
    extensions: DEFAULT_VIDEO_EXTENSIONS,
    recursive: true,
    maxFileSize: 500 * 1024 * 1024, // 500MB
    minFileSize: 1024, // 1KB
    onProgress: () => { }
};
/**
 * Video file scanner class
 */
class VideoScanner {
    constructor(options = {}) {
        this.options = { ...DEFAULT_SCAN_OPTIONS, ...options };
    }
    /**
     * Scan a directory for video files
     */
    async scanDirectory(directoryPath) {
        if (!await this.isValidDirectory(directoryPath)) {
            throw new VideoAnalyzerError(`Invalid directory path: ${directoryPath}`, 'INVALID_DIRECTORY');
        }
        const videoFiles = [];
        const progress = {
            step: 'Initializing scan',
            progress: 0,
            filesFound: 0,
            directoriesScanned: 0
        };
        this.options.onProgress(progress);
        try {
            await this.scanDirectoryRecursive(directoryPath, videoFiles, progress);
            progress.step = 'Scan completed';
            progress.progress = 100;
            this.options.onProgress(progress);
            return videoFiles;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Failed to scan directory: ${error.message}`, 'SCAN_FAILED', error);
        }
    }
    /**
     * Recursively scan directory for video files
     */
    async scanDirectoryRecursive(dirPath, videoFiles, progress) {
        progress.directoriesScanned++;
        progress.step = `Scanning: ${path.basename(dirPath)}`;
        this.options.onProgress(progress);
        const entries = await readdir$1(dirPath, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            if (entry.isDirectory() && this.options.recursive) {
                await this.scanDirectoryRecursive(fullPath, videoFiles, progress);
            }
            else if (entry.isFile()) {
                progress.currentFile = entry.name;
                this.options.onProgress(progress);
                if (await this.isVideoFile(fullPath)) {
                    try {
                        const videoFile = await this.createVideoFile(fullPath);
                        if (this.isValidVideoFile(videoFile)) {
                            videoFiles.push(videoFile);
                            progress.filesFound++;
                            progress.progress = Math.min(95, (progress.filesFound / 10) * 100);
                            this.options.onProgress(progress);
                        }
                    }
                    catch (error) {
                        console.warn(`Failed to process video file ${fullPath}:`, error.message);
                    }
                }
            }
        }
    }
    /**
     * Check if a file is a video file based on extension
     */
    async isVideoFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.options.extensions.includes(ext);
    }
    /**
     * Create VideoFile object from file path
     */
    async createVideoFile(filePath) {
        const stats = await stat$1(filePath);
        const parsedPath = path.parse(filePath);
        return {
            path: filePath,
            name: parsedPath.name,
            size: stats.size,
            format: parsedPath.ext.toLowerCase().substring(1),
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    /**
     * Validate video file against size constraints
     */
    isValidVideoFile(videoFile) {
        return (videoFile.size >= this.options.minFileSize &&
            videoFile.size <= this.options.maxFileSize);
    }
    /**
     * Check if directory exists and is accessible
     */
    async isValidDirectory(dirPath) {
        try {
            const stats = await stat$1(dirPath);
            return stats.isDirectory();
        }
        catch {
            return false;
        }
    }
    /**
     * Get video file metadata using basic file system info
     */
    async getBasicMetadata(videoFile) {
        // This is a basic implementation
        // In a real-world scenario, you might want to use ffprobe or similar tools
        // to extract detailed video metadata like duration, resolution, etc.
        try {
            const stats = await stat$1(videoFile.path);
            return {
                ...videoFile,
                size: stats.size,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
        }
        catch (error) {
            throw new VideoAnalyzerError(`Failed to get metadata for ${videoFile.path}: ${error.message}`, 'METADATA_FAILED', error);
        }
    }
    /**
     * Validate multiple video files
     */
    async validateVideoFiles(videoFiles) {
        const valid = [];
        const invalid = [];
        for (const videoFile of videoFiles) {
            try {
                // Check if file still exists
                if (!await this.fileExists(videoFile.path)) {
                    invalid.push({ file: videoFile, reason: 'File not found' });
                    continue;
                }
                // Check file size constraints
                if (!this.isValidVideoFile(videoFile)) {
                    invalid.push({
                        file: videoFile,
                        reason: `File size ${videoFile.size} bytes is outside allowed range`
                    });
                    continue;
                }
                // Check if it's still a video file
                if (!await this.isVideoFile(videoFile.path)) {
                    invalid.push({ file: videoFile, reason: 'Not a supported video format' });
                    continue;
                }
                valid.push(videoFile);
            }
            catch (error) {
                invalid.push({ file: videoFile, reason: error.message });
            }
        }
        return { valid, invalid };
    }
    /**
     * Check if file exists
     */
    async fileExists(filePath) {
        try {
            await stat$1(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get scan statistics
     */
    async getScanStatistics(directoryPath) {
        const videoFiles = await this.scanDirectory(directoryPath);
        const totalFiles = videoFiles.length;
        const totalSize = videoFiles.reduce((sum, file) => sum + file.size, 0);
        const averageSize = totalFiles > 0 ? totalSize / totalFiles : 0;
        const formatDistribution = {};
        videoFiles.forEach(file => {
            const format = file.format || 'unknown';
            formatDistribution[format] = (formatDistribution[format] || 0) + 1;
        });
        return {
            totalFiles,
            totalVideoFiles: totalFiles,
            totalSize,
            averageSize,
            formatDistribution
        };
    }
    /**
     * Update scan options
     */
    updateOptions(options) {
        this.options = { ...this.options, ...options };
    }
    /**
     * Get current scan options
     */
    getOptions() {
        return { ...this.options };
    }
}
/**
 * Convenience function to scan directory with default options
 */
async function scanVideoDirectory(directoryPath, options) {
    const scanner = new VideoScanner(options);
    return scanner.scanDirectory(directoryPath);
}
/**
 * Convenience function to get video scan statistics
 */
async function getVideoScanStatistics(directoryPath, options) {
    const scanner = new VideoScanner(options);
    return scanner.getScanStatistics(directoryPath);
}

/**
 * Google Generative AI 客户端
 * 通过 Cloudflare Gateway 调用 Google Vertex AI API
 */
class GoogleGenaiClient {
    config;
    constructor(config) {
        this.config = config;
    }
    /**
     * 计算属性：通过 Cloudflare Gateway 调用的 URL
     */
    get gatewayUrl() {
        // 随机选择一个区域
        const randomRegion = this.config.regions[Math.floor(Math.random() * this.config.regions.length)];
        return `https://gateway.ai.cloudflare.com/v1/${this.config.cloudflareProjectId}/${this.config.cloudflareGatewayId}/google-vertex-ai/v1/projects/${this.config.googleProjectId}/locations/${randomRegion}/publishers/google/models`;
    }
    /**
     * 生成内容
     * @param modelId 模型 ID，如 'gemini-2.5-flash'
     * @param contents 内容数组
     * @param config 生成配置
     * @param timeout 超时时间（秒）
     * @returns 生成结果和状态码
     */
    async generateContent(contents, modelId = 'gemini-2.5-flash', config, timeout = 30) {
        try {
            // 构建请求体
            const requestBody = {
                contents,
                ...(config && { generationConfig: config })
            };
            // 排除空值并转换为 JSON
            const jsonBody = JSON.stringify(requestBody, (key, value) => {
                if (value === null || value === undefined) {
                    return undefined;
                }
                return value;
            }, 2);
            const url = `${this.gatewayUrl}/${modelId}:generateContent`;
            const response = await axios.post(url, jsonBody, {
                timeout: timeout * 1000, // 转换为毫秒
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.accessToken}`
                }
            });
            if (response.status !== 200) {
                return {
                    response: null,
                    statusCode: response.status
                };
            }
            return {
                response: response.data,
                statusCode: response.status
            };
        }
        catch (error) {
            if (error.response) {
                return {
                    response: null,
                    statusCode: error.response.status
                };
            }
            else if (error.request) {
                return {
                    response: null,
                    statusCode: 0
                };
            }
            else {
                return {
                    response: null,
                    statusCode: -1
                };
            }
        }
    }
    /**
     * 简化的文本生成方法
     * @param modelId 模型 ID
     * @param prompt 文本提示
     * @param config 生成配置
     * @returns 生成的文本内容
     */
    async generateText(prompt, modelId = 'gemini-2.5-flash', config) {
        const contents = [
            {
                role: 'user',
                parts: [{ text: prompt }]
            }
        ];
        const result = await this.generateContent(contents, modelId, config);
        if (result.statusCode === 200 && result.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
            return result.response.candidates[0].content.parts[0].text;
        }
        return null;
    }
    /**
     * 获取配置信息
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * 更新访问令牌
     */
    updateAccessToken(accessToken) {
        this.config.accessToken = accessToken;
    }
}
/**
 * 使用默认配置创建客户端
 */
function createDefaultGoogleGenaiClient(accessToken) {
    return new GoogleGenaiClient({
        cloudflareProjectId: '67720b647ff2b55cf37ba3ef9e677083',
        cloudflareGatewayId: 'bowong-dev',
        googleProjectId: 'gen-lang-client-0413414134',
        regions: ['us-central1'], // 使用稳定的区域
        accessToken
    });
}

function useGeminiAxios() {
    return axios.create({
        baseURL: `https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run`,
        headers: {
            Authorization: `Bearer bowong7777`,
        }
    });
}
async function useGeminiAccessToken() {
    const geminiAxios = useGeminiAxios();
    const token = await geminiAxios.request({
        method: `get`,
        url: `/google/access-token`
    }).then(res => res.data);
    return token;
}
async function uploadFileToGemini(bucket, prefix, file) {
    const genminiAxios = useGeminiAxios();
    await genminiAxios.request({
        method: `post`,
        url: `/google/vertex-ai/upload`,
        params: {
            bucket: bucket,
            prefix: prefix
        },
        data: {
            file: file
        },
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}
async function useGemini() {
    const token = await useGeminiAccessToken();
    return createDefaultGoogleGenaiClient(token.access_token);
}

/**
 * Video file uploader for Gemini AI
 */
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
/**
 * Default upload configuration
 */
const DEFAULT_UPLOAD_CONFIG = {
    chunkSize: 10 * 1024 * 1024, // 10MB chunks
    maxRetries: 3,
    timeout: 300000, // 5 minutes
    onProgress: () => { }
};
/**
 * Video uploader class
 */
class VideoUploader {
    constructor(config) {
        this.config = {
            ...DEFAULT_UPLOAD_CONFIG,
            ...config
        };
    }
    /**
     * Upload a single video file to Gemini
     */
    async uploadVideo(videoFile) {
        const startTime = Date.now();
        const uploadId = this.generateUploadId(videoFile);
        const progress = {
            step: 'Preparing upload',
            progress: 0,
            currentFile: videoFile.name,
            bytesUploaded: 0,
            totalBytes: videoFile.size
        };
        this.config.onProgress(progress);
        try {
            // Validate file before upload
            await this.validateFileForUpload(videoFile);
            progress.step = 'Reading file';
            progress.progress = 10;
            this.config.onProgress(progress);
            // Read file content
            const fileBuffer = await this.readVideoFile(videoFile);
            progress.step = 'Uploading to Gemini';
            progress.progress = 20;
            this.config.onProgress(progress);
            // Generate GCS path
            const gcsPath = this.generateGcsPath(videoFile);
            // Upload with retry logic
            await this.uploadWithRetry(fileBuffer, gcsPath, progress);
            const uploadTime = Date.now() - startTime;
            progress.step = 'Upload completed';
            progress.progress = 100;
            progress.bytesUploaded = videoFile.size;
            this.config.onProgress(progress);
            return {
                videoFile,
                success: true,
                gcsPath,
                uploadTime,
                bytesUploaded: videoFile.size,
                metadata: {
                    uploadId,
                    timestamp: new Date(),
                    checksum: await this.calculateChecksum(fileBuffer)
                }
            };
        }
        catch (error) {
            const uploadTime = Date.now() - startTime;
            return {
                videoFile,
                success: false,
                uploadTime,
                bytesUploaded: progress.bytesUploaded,
                error: error.message,
                metadata: {
                    uploadId,
                    timestamp: new Date()
                }
            };
        }
    }
    /**
     * Upload multiple video files
     */
    async uploadVideos(videoFiles) {
        const results = [];
        for (let i = 0; i < videoFiles.length; i++) {
            const videoFile = videoFiles[i];
            try {
                const result = await this.uploadVideo(videoFile);
                results.push(result);
                // Update overall progress
                const overallProgress = ((i + 1) / videoFiles.length) * 100;
                this.config.onProgress({
                    step: `Uploaded ${i + 1}/${videoFiles.length} files`,
                    progress: overallProgress,
                    currentFile: videoFile.name,
                    bytesUploaded: results.reduce((sum, r) => sum + r.bytesUploaded, 0),
                    totalBytes: videoFiles.reduce((sum, f) => sum + f.size, 0)
                });
            }
            catch (error) {
                results.push({
                    videoFile,
                    success: false,
                    uploadTime: 0,
                    bytesUploaded: 0,
                    error: error.message
                });
            }
        }
        return results;
    }
    /**
     * Validate file before upload
     */
    async validateFileForUpload(videoFile) {
        // Check if file exists
        try {
            await stat(videoFile.path);
        }
        catch (error) {
            throw new VideoAnalyzerError(`File not found: ${videoFile.path}`, 'FILE_NOT_FOUND');
        }
        // Check file size
        if (videoFile.size === 0) {
            throw new VideoAnalyzerError(`File is empty: ${videoFile.path}`, 'EMPTY_FILE');
        }
        // Check if file is too large (Gemini has limits)
        const maxSize = 500 * 1024 * 1024; // 500MB
        if (videoFile.size > maxSize) {
            throw new VideoAnalyzerError(`File too large: ${videoFile.size} bytes (max: ${maxSize} bytes)`, 'FILE_TOO_LARGE');
        }
    }
    /**
     * Read video file into buffer
     */
    async readVideoFile(videoFile) {
        try {
            return await readFile(videoFile.path);
        }
        catch (error) {
            throw new VideoAnalyzerError(`Failed to read file: ${videoFile.path}`, 'READ_FAILED', error);
        }
    }
    /**
     * Upload with retry logic
     */
    async uploadWithRetry(fileBuffer, gcsPath, progress) {
        let lastError = null;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                progress.step = `Upload attempt ${attempt}/${this.config.maxRetries}`;
                this.config.onProgress(progress);
                await uploadFileToGemini(this.config.bucketName, gcsPath, fileBuffer);
                // Upload successful
                return;
            }
            catch (error) {
                lastError = error;
                if (attempt < this.config.maxRetries) {
                    // Wait before retry with exponential backoff
                    const delay = Math.pow(2, attempt - 1) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw new VideoAnalyzerError(`Upload failed after ${this.config.maxRetries} attempts: ${lastError?.message}`, 'UPLOAD_FAILED', lastError);
    }
    /**
     * Generate GCS path for video file
     */
    generateGcsPath(videoFile) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const sanitizedName = videoFile.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        return `${this.config.filePrefix}${timestamp}_${sanitizedName}.${videoFile.format}`;
    }
    /**
     * Generate unique upload ID
     */
    generateUploadId(videoFile) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        return `upload_${timestamp}_${random}`;
    }
    /**
     * Calculate file checksum for verification
     */
    async calculateChecksum(buffer) {
        const crypto = await import('crypto');
        return crypto.createHash('md5').update(buffer).digest('hex');
    }
    /**
     * Get upload statistics
     */
    getUploadStatistics(results) {
        const totalFiles = results.length;
        const successfulUploads = results.filter(r => r.success).length;
        const failedUploads = totalFiles - successfulUploads;
        const totalBytesUploaded = results.reduce((sum, r) => sum + r.bytesUploaded, 0);
        const totalUploadTime = results.reduce((sum, r) => sum + r.uploadTime, 0);
        const averageUploadTime = totalFiles > 0 ? totalUploadTime / totalFiles : 0;
        const successRate = totalFiles > 0 ? (successfulUploads / totalFiles) * 100 : 0;
        return {
            totalFiles,
            successfulUploads,
            failedUploads,
            totalBytesUploaded,
            averageUploadTime,
            successRate
        };
    }
    /**
     * Update upload configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Get current upload configuration
     */
    getConfig() {
        return { ...this.config };
    }
}
/**
 * Convenience function to upload a single video
 */
async function uploadVideoToGemini(videoFile, config) {
    const uploader = new VideoUploader(config);
    return uploader.uploadVideo(videoFile);
}
/**
 * Convenience function to upload multiple videos
 */
async function uploadVideosToGemini(videoFiles, config) {
    const uploader = new VideoUploader(config);
    return uploader.uploadVideos(videoFiles);
}

/**
 * Core video analysis engine using Gemini AI
 */
/**
 * Analysis prompts for different types of content analysis
 */
const ANALYSIS_PROMPTS = {
    COMPREHENSIVE: `请对这个视频进行全面分析，包括：
1. 场景检测：识别视频中的不同场景，包括开始时间、结束时间和场景描述
2. 物体识别：识别视频中出现的主要物体、人物和元素
3. 内容总结：提供视频的整体描述、关键亮点和主要主题
4. 情感基调：分析视频的情感氛围和风格
5. 关键词提取：提取最相关的关键词

请以JSON格式返回结果，包含scenes、objects、summary等字段。`,
    PRODUCT_FOCUSED: `请专门分析这个视频中的产品相关内容：
1. 产品外观：颜色、形状、尺寸、风格
2. 材质分析：识别产品使用的材料
3. 功能特征：产品展示的功能和特性
4. 使用场景：产品的使用环境和场景
5. 目标受众：分析产品的目标用户群体
6. 品牌元素：识别品牌标识、logo等元素

请以JSON格式返回详细的产品分析结果。`,
    SCENE_DETECTION: `请详细分析视频中的场景变化：
1. 识别每个独立的场景
2. 标记场景的开始和结束时间
3. 描述每个场景的内容和特征
4. 评估场景转换的流畅性
5. 识别关键帧时间点

请以JSON格式返回场景分析结果。`,
    OBJECT_DETECTION: `请识别和分析视频中的所有重要物体：
1. 物体名称和类别
2. 物体在视频中出现的时间范围
3. 物体的重要性和相关性评分
4. 物体之间的关系和交互
5. 物体的属性和特征

请以JSON格式返回物体检测结果。`
};
/**
 * Video analysis engine class
 */
class AnalysisEngine {
    constructor() {
        this.geminiClient = null;
        // Gemini client will be initialized when needed
    }
    /**
     * Initialize Gemini client
     */
    async initializeGeminiClient() {
        if (!this.geminiClient) {
            this.geminiClient = await useGemini();
        }
    }
    /**
     * Analyze video using Gemini AI
     */
    async analyzeVideo(videoFile, gcsPath, mode, options = {}, onProgress) {
        const startTime = Date.now();
        const progress = {
            step: 'Initializing analysis',
            progress: 0,
            currentFile: videoFile.name,
            stage: 'processing'
        };
        onProgress?.(progress);
        try {
            await this.initializeGeminiClient();
            progress.step = 'Preparing analysis prompts';
            progress.progress = 10;
            onProgress?.(progress);
            // Generate analysis prompts based on options
            const prompts = this.generateAnalysisPrompts(options);
            progress.step = 'Analyzing video content';
            progress.progress = 20;
            progress.stage = 'analysis';
            onProgress?.(progress);
            // Perform comprehensive analysis
            const analysisResults = await this.performComprehensiveAnalysis(gcsPath, prompts, options, onProgress);
            progress.step = 'Processing analysis results';
            progress.progress = 90;
            onProgress?.(progress);
            // Build final result
            const result = {
                metadata: await this.buildVideoMetadata(videoFile),
                analysisMode: mode,
                scenes: analysisResults.scenes || [],
                objects: analysisResults.objects || [],
                productFeatures: analysisResults.productFeatures,
                summary: analysisResults.summary || this.createDefaultSummary(),
                frameAnalysis: analysisResults.frameAnalysis,
                analyzedAt: new Date(),
                processingTime: Date.now() - startTime,
                qualityMetrics: this.calculateQualityMetrics(analysisResults)
            };
            progress.step = 'Analysis completed';
            progress.progress = 100;
            progress.stage = 'complete';
            onProgress?.(progress);
            return result;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Analysis failed for ${videoFile.name}: ${error.message}`, 'ANALYSIS_FAILED', error);
        }
    }
    /**
     * Generate analysis prompts based on options
     */
    generateAnalysisPrompts(options) {
        const prompts = [];
        if (options.enableSceneDetection) {
            prompts.push(ANALYSIS_PROMPTS.SCENE_DETECTION);
        }
        if (options.enableObjectDetection) {
            prompts.push(ANALYSIS_PROMPTS.OBJECT_DETECTION);
        }
        if (options.enableProductAnalysis) {
            prompts.push(ANALYSIS_PROMPTS.PRODUCT_FOCUSED);
        }
        if (options.enableSummarization || prompts.length === 0) {
            prompts.push(ANALYSIS_PROMPTS.COMPREHENSIVE);
        }
        // Add custom prompts if provided
        if (options.customPrompts) {
            prompts.push(...options.customPrompts);
        }
        return prompts;
    }
    /**
     * Perform comprehensive analysis using multiple prompts
     */
    async performComprehensiveAnalysis(gcsPath, prompts, options, onProgress) {
        const results = {
            scenes: [],
            objects: [],
            summary: null,
            productFeatures: null
        };
        for (let i = 0; i < prompts.length; i++) {
            const prompt = prompts[i];
            const progressPercent = 20 + ((i + 1) / prompts.length) * 60;
            onProgress?.({
                step: `Running analysis ${i + 1}/${prompts.length}`,
                progress: progressPercent,
                stage: 'analysis'
            });
            try {
                const analysisResult = await this.runSingleAnalysis(gcsPath, prompt, options);
                this.mergeAnalysisResults(results, analysisResult);
            }
            catch (error) {
                console.warn(`Analysis prompt ${i + 1} failed:`, error.message);
            }
        }
        return results;
    }
    /**
     * Run a single analysis with a specific prompt
     */
    async runSingleAnalysis(gcsPath, prompt, options) {
        try {
            // Create content array with video reference
            const contents = [
                {
                    role: 'user',
                    parts: [
                        {
                            text: prompt
                        },
                        {
                            fileData: {
                                mimeType: 'video/mp4', // Adjust based on actual video format
                                fileUri: `gs://${gcsPath}`
                            }
                        }
                    ]
                }
            ];
            const response = await this.geminiClient.generateContent(contents, 'gemini-2.5-flash', {
                temperature: 0.3,
                maxOutputTokens: 4096,
                topP: 0.8
            });
            if (response.statusCode === 200 && response.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
                const responseText = response.response.candidates[0].content.parts[0].text;
                return this.parseAnalysisResponse(responseText);
            }
            throw new Error('Invalid response from Gemini API');
        }
        catch (error) {
            throw new VideoAnalyzerError(`Gemini analysis failed: ${error.message}`, 'GEMINI_ANALYSIS_FAILED', error);
        }
    }
    /**
     * Parse analysis response from Gemini
     */
    parseAnalysisResponse(responseText) {
        try {
            // Try to extract JSON from the response
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            // If no JSON found, create structured response from text
            return this.parseTextResponse(responseText);
        }
        catch (error) {
            console.warn('Failed to parse JSON response, using text parsing:', error.message);
            return this.parseTextResponse(responseText);
        }
    }
    /**
     * Parse text response when JSON parsing fails
     */
    parseTextResponse(text) {
        const result = {
            scenes: [],
            objects: [],
            summary: {
                description: text.substring(0, 500),
                highlights: [],
                topics: [],
                keywords: []
            }
        };
        // Extract key information using regex patterns
        const sceneMatches = text.match(/场景\s*\d+[：:](.*?)(?=场景\s*\d+|$)/g);
        if (sceneMatches) {
            result.scenes = sceneMatches.map((match, index) => ({
                startTime: index * 10, // Estimate timing
                endTime: (index + 1) * 10,
                duration: 10,
                description: match.replace(/场景\s*\d+[：:]/, '').trim(),
                confidence: 0.7
            }));
        }
        // Extract objects/keywords
        const objectMatches = text.match(/(?:物体|对象|元素)[：:]([^。\n]+)/g);
        if (objectMatches) {
            result.objects = objectMatches.map(match => ({
                name: match.replace(/(?:物体|对象|元素)[：:]/, '').trim(),
                category: 'general',
                confidence: 0.6
            }));
        }
        return result;
    }
    /**
     * Merge multiple analysis results
     */
    mergeAnalysisResults(target, source) {
        if (source.scenes && Array.isArray(source.scenes)) {
            target.scenes.push(...source.scenes);
        }
        if (source.objects && Array.isArray(source.objects)) {
            target.objects.push(...source.objects);
        }
        if (source.summary && !target.summary) {
            target.summary = source.summary;
        }
        if (source.productFeatures && !target.productFeatures) {
            target.productFeatures = source.productFeatures;
        }
    }
    /**
     * Build video metadata
     */
    async buildVideoMetadata(videoFile) {
        return {
            file: videoFile,
            technical: {
                codec: 'unknown',
                container: videoFile.format || 'unknown',
                hasAudio: true, // Assume has audio
                audioCodec: 'unknown'
            },
            content: {
                title: videoFile.name,
                description: `Video analysis for ${videoFile.name}`,
                tags: []
            }
        };
    }
    /**
     * Create default summary when none is provided
     */
    createDefaultSummary() {
        return {
            description: 'Video content analysis completed',
            highlights: [],
            topics: [],
            keywords: []
        };
    }
    /**
     * Calculate quality metrics for analysis results
     */
    calculateQualityMetrics(results) {
        let overallScore = 0.5; // Base score
        let detectionAccuracy = 0.5;
        let analysisDepth = 0.5;
        // Increase scores based on available data
        if (results.scenes && results.scenes.length > 0) {
            overallScore += 0.2;
            detectionAccuracy += 0.2;
        }
        if (results.objects && results.objects.length > 0) {
            overallScore += 0.2;
            detectionAccuracy += 0.2;
        }
        if (results.summary && results.summary.description) {
            overallScore += 0.1;
            analysisDepth += 0.3;
        }
        if (results.productFeatures) {
            analysisDepth += 0.2;
        }
        return {
            overallScore: Math.min(1.0, overallScore),
            detectionAccuracy: Math.min(1.0, detectionAccuracy),
            analysisDepth: Math.min(1.0, analysisDepth)
        };
    }
}
/**
 * Convenience function to analyze a video
 */
async function analyzeVideoWithGemini(videoFile, gcsPath, mode, options, onProgress) {
    const engine = new AnalysisEngine();
    return engine.analyzeVideo(videoFile, gcsPath, mode, options, onProgress);
}

/**
 * Specialized product feature analysis for e-commerce and marketing videos
 */
/**
 * Product analysis prompts for different aspects
 */
const PRODUCT_ANALYSIS_PROMPTS = {
    APPEARANCE: `请详细分析视频中产品的外观特征：
1. 颜色：主要颜色、配色方案、颜色搭配
2. 形状：整体形状、设计风格、几何特征
3. 尺寸：相对大小、比例关系
4. 风格：设计风格（现代、经典、简约等）
5. 表面处理：光泽度、纹理、图案

请以JSON格式返回详细的外观分析结果。`,
    MATERIALS: `请分析视频中产品使用的材料：
1. 主要材料：金属、塑料、布料、皮革、玻璃等
2. 材料质感：光滑、粗糙、柔软、坚硬等
3. 材料质量：高端、中端、经济型
4. 特殊材料：环保材料、高科技材料等
5. 材料组合：多种材料的搭配使用

请以JSON格式返回材料分析结果。`,
    FUNCTIONALITY: `请分析产品展示的功能特征：
1. 主要功能：产品的核心功能和用途
2. 特色功能：独特的功能特点
3. 操作方式：如何使用和操作
4. 技术特征：涉及的技术和创新点
5. 性能表现：速度、效率、精度等

请以JSON格式返回功能分析结果。`,
    USAGE_SCENARIOS: `请分析产品的使用场景和环境：
1. 使用环境：室内、户外、办公室、家庭等
2. 使用时机：日常、特殊场合、季节性等
3. 用户行为：如何使用、使用频率
4. 搭配使用：与其他产品的配合
5. 适用人群：年龄、性别、职业等

请以JSON格式返回使用场景分析结果。`,
    BRAND_ELEMENTS: `请识别视频中的品牌元素：
1. Logo标识：品牌logo的位置和展示
2. 品牌色彩：品牌专用色彩
3. 包装设计：产品包装的品牌特征
4. 品牌文字：品牌名称、标语等
5. 品牌风格：整体品牌调性和风格

请以JSON格式返回品牌元素分析结果。`,
    ATMOSPHERE: `请分析视频的整体氛围和情感表达：
1. 视觉氛围：明亮、温馨、专业、时尚等
2. 情感基调：快乐、严肃、轻松、高端等
3. 音乐风格：背景音乐的风格和情感
4. 拍摄风格：镜头语言、构图风格
5. 目标情感：想要传达的情感和感受

请以JSON格式返回氛围分析结果。`
};
/**
 * Product feature analyzer class
 */
class ProductAnalyzer {
    constructor() {
        this.geminiClient = null;
        // Gemini client will be initialized when needed
    }
    /**
     * Initialize Gemini client
     */
    async initializeGeminiClient() {
        if (!this.geminiClient) {
            this.geminiClient = await useGemini();
        }
    }
    /**
     * Analyze product features in video
     */
    async analyzeProductFeatures(videoFile, gcsPath, options = {}) {
        try {
            await this.initializeGeminiClient();
            // Run comprehensive product analysis
            const analysisResults = await Promise.allSettled([
                this.analyzeAppearance(gcsPath),
                this.analyzeMaterials(gcsPath),
                this.analyzeFunctionality(gcsPath),
                this.analyzeUsageScenarios(gcsPath),
                this.analyzeBrandElements(gcsPath),
                this.analyzeAtmosphere(gcsPath)
            ]);
            // Combine results
            return this.combineProductAnalysis(analysisResults);
        }
        catch (error) {
            throw new VideoAnalyzerError(`Product analysis failed for ${videoFile.name}: ${error.message}`, 'PRODUCT_ANALYSIS_FAILED', error);
        }
    }
    /**
     * Analyze product appearance
     */
    async analyzeAppearance(gcsPath) {
        return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.APPEARANCE);
    }
    /**
     * Analyze product materials
     */
    async analyzeMaterials(gcsPath) {
        return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.MATERIALS);
    }
    /**
     * Analyze product functionality
     */
    async analyzeFunctionality(gcsPath) {
        return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.FUNCTIONALITY);
    }
    /**
     * Analyze usage scenarios
     */
    async analyzeUsageScenarios(gcsPath) {
        return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.USAGE_SCENARIOS);
    }
    /**
     * Analyze brand elements
     */
    async analyzeBrandElements(gcsPath) {
        return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.BRAND_ELEMENTS);
    }
    /**
     * Analyze video atmosphere
     */
    async analyzeAtmosphere(gcsPath) {
        return this.runProductAnalysis(gcsPath, PRODUCT_ANALYSIS_PROMPTS.ATMOSPHERE);
    }
    /**
     * Run a single product analysis
     */
    async runProductAnalysis(gcsPath, prompt) {
        try {
            const contents = [
                {
                    role: 'user',
                    parts: [
                        {
                            text: prompt
                        },
                        {
                            fileData: {
                                mimeType: 'video/mp4',
                                fileUri: `gs://${gcsPath}`
                            }
                        }
                    ]
                }
            ];
            const response = await this.geminiClient.generateContent(contents, 'gemini-2.5-flash', {
                temperature: 0.2, // Lower temperature for more consistent analysis
                maxOutputTokens: 2048,
                topP: 0.8
            });
            if (response.statusCode === 200 && response.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
                const responseText = response.response.candidates[0].content.parts[0].text;
                return this.parseProductAnalysisResponse(responseText);
            }
            return null;
        }
        catch (error) {
            console.warn('Product analysis step failed:', error.message);
            return null;
        }
    }
    /**
     * Parse product analysis response
     */
    parseProductAnalysisResponse(responseText) {
        try {
            // Try to extract JSON from the response
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            // If no JSON found, parse as text
            return this.parseProductTextResponse(responseText);
        }
        catch (error) {
            console.warn('Failed to parse product analysis JSON:', error.message);
            return this.parseProductTextResponse(responseText);
        }
    }
    /**
     * Parse text response for product analysis
     */
    parseProductTextResponse(text) {
        const result = {};
        // Extract colors
        const colorMatches = text.match(/颜色[：:]([^。\n]+)/g);
        if (colorMatches) {
            result.colors = colorMatches.map(match => match.replace(/颜色[：:]/, '').trim());
        }
        // Extract materials
        const materialMatches = text.match(/材料[：:]([^。\n]+)/g);
        if (materialMatches) {
            result.materials = materialMatches.map(match => match.replace(/材料[：:]/, '').trim());
        }
        // Extract functionality
        const functionMatches = text.match(/功能[：:]([^。\n]+)/g);
        if (functionMatches) {
            result.functionality = functionMatches.map(match => match.replace(/功能[：:]/, '').trim());
        }
        return result;
    }
    /**
     * Combine all product analysis results
     */
    combineProductAnalysis(results) {
        const productFeatures = {
            appearance: {
                colors: [],
                shape: '',
                size: '',
                style: ''
            },
            materials: [],
            functionality: [],
            usageScenarios: [],
            targetAudience: '',
            brandElements: []
        };
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && result.value) {
                const data = result.value;
                switch (index) {
                    case 0: // Appearance
                        if (data.colors)
                            productFeatures.appearance.colors = data.colors;
                        if (data.shape)
                            productFeatures.appearance.shape = data.shape;
                        if (data.size)
                            productFeatures.appearance.size = data.size;
                        if (data.style)
                            productFeatures.appearance.style = data.style;
                        break;
                    case 1: // Materials
                        if (data.materials)
                            productFeatures.materials = data.materials;
                        break;
                    case 2: // Functionality
                        if (data.functionality)
                            productFeatures.functionality = data.functionality;
                        break;
                    case 3: // Usage scenarios
                        if (data.usageScenarios)
                            productFeatures.usageScenarios = data.usageScenarios;
                        if (data.targetAudience)
                            productFeatures.targetAudience = data.targetAudience;
                        break;
                    case 4: // Brand elements
                        if (data.brandElements)
                            productFeatures.brandElements = data.brandElements;
                        break;
                }
            }
        });
        return productFeatures;
    }
    /**
     * Analyze product for e-commerce categorization
     */
    async analyzeForEcommerce(videoFile, gcsPath) {
        const prompt = `请分析这个产品视频，为电商平台分类：
1. 产品类别：确定主要产品类别
2. 子类别：更具体的产品分类
3. 标签：相关的产品标签和关键词
4. 价格范围：根据产品特征推测价格档次
5. 目标市场：目标消费群体和市场定位

请以JSON格式返回电商分类结果。`;
        try {
            const result = await this.runProductAnalysis(gcsPath, prompt);
            return {
                category: result?.category || 'unknown',
                subcategory: result?.subcategory || 'unknown',
                tags: result?.tags || [],
                priceRange: result?.priceRange || 'unknown',
                targetMarket: result?.targetMarket || 'unknown'
            };
        }
        catch (error) {
            throw new VideoAnalyzerError(`E-commerce analysis failed: ${error.message}`, 'ECOMMERCE_ANALYSIS_FAILED', error);
        }
    }
    /**
     * Generate product description from analysis
     */
    generateProductDescription(features) {
        const parts = [];
        // Add appearance description
        if (features.appearance.colors.length > 0) {
            parts.push(`颜色：${features.appearance.colors.join('、')}`);
        }
        if (features.appearance.style) {
            parts.push(`风格：${features.appearance.style}`);
        }
        // Add materials
        if (features.materials.length > 0) {
            parts.push(`材质：${features.materials.join('、')}`);
        }
        // Add functionality
        if (features.functionality.length > 0) {
            parts.push(`功能：${features.functionality.join('、')}`);
        }
        // Add usage scenarios
        if (features.usageScenarios.length > 0) {
            parts.push(`适用场景：${features.usageScenarios.join('、')}`);
        }
        return parts.join('；');
    }
}
/**
 * Convenience function to analyze product features
 */
async function analyzeProductInVideo(videoFile, gcsPath, options) {
    const analyzer = new ProductAnalyzer();
    return analyzer.analyzeProductFeatures(videoFile, gcsPath, options);
}

/**
 * Smart folder matching system for video categorization
 */
const readdir = promisify(fs.readdir);
promisify(fs.stat);
/**
 * Default folder match configuration
 */
const DEFAULT_FOLDER_MATCH_CONFIG = {
    maxDepth: 3,
    minConfidence: 0.3,
    maxMatches: 5,
    enableSemanticAnalysis: true
};
/**
 * Smart folder matcher class
 */
class FolderMatcher {
    constructor(config) {
        this.geminiClient = null;
        this.folderCache = new Map();
        this.config = { ...DEFAULT_FOLDER_MATCH_CONFIG, ...config };
    }
    /**
     * Initialize Gemini client
     */
    async initializeGeminiClient() {
        if (!this.geminiClient) {
            this.geminiClient = await useGemini();
        }
    }
    /**
     * Find matching folders for video analysis result
     */
    async findMatchingFolders(analysisResult) {
        try {
            // Get available folders
            const folders = await this.scanFolders();
            // Generate content description for matching
            const contentDescription = this.generateContentDescription(analysisResult);
            // Perform folder matching
            const matches = await this.performFolderMatching(contentDescription, folders);
            // Sort by confidence and return top matches
            return matches
                .filter(match => match.confidence >= this.config.minConfidence)
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, this.config.maxMatches);
        }
        catch (error) {
            throw new VideoAnalyzerError(`Folder matching failed: ${error.message}`, 'FOLDER_MATCHING_FAILED', error);
        }
    }
    /**
     * Scan for available folders
     */
    async scanFolders() {
        const cacheKey = this.config.baseDirectory;
        if (this.folderCache.has(cacheKey)) {
            return this.folderCache.get(cacheKey);
        }
        const folders = [];
        await this.scanFoldersRecursive(this.config.baseDirectory, folders, 0);
        this.folderCache.set(cacheKey, folders);
        return folders;
    }
    /**
     * Recursively scan folders
     */
    async scanFoldersRecursive(dirPath, folders, currentDepth) {
        if (currentDepth >= this.config.maxDepth) {
            return;
        }
        try {
            const entries = await readdir(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                if (entry.isDirectory()) {
                    const fullPath = path.join(dirPath, entry.name);
                    folders.push(fullPath);
                    // Continue scanning subdirectories
                    await this.scanFoldersRecursive(fullPath, folders, currentDepth + 1);
                }
            }
        }
        catch (error) {
            console.warn(`Failed to scan directory ${dirPath}:`, error.message);
        }
    }
    /**
     * Generate content description for matching
     */
    generateContentDescription(analysisResult) {
        const parts = [];
        // Add summary description
        if (analysisResult.summary.description) {
            parts.push(`内容描述：${analysisResult.summary.description}`);
        }
        // Add keywords
        if (analysisResult.summary.keywords.length > 0) {
            parts.push(`关键词：${analysisResult.summary.keywords.join('、')}`);
        }
        // Add topics
        if (analysisResult.summary.topics.length > 0) {
            parts.push(`主题：${analysisResult.summary.topics.join('、')}`);
        }
        // Add product features if available
        if (analysisResult.productFeatures) {
            const features = analysisResult.productFeatures;
            if (features.appearance.colors.length > 0) {
                parts.push(`颜色：${features.appearance.colors.join('、')}`);
            }
            if (features.materials.length > 0) {
                parts.push(`材质：${features.materials.join('、')}`);
            }
            if (features.functionality.length > 0) {
                parts.push(`功能：${features.functionality.join('、')}`);
            }
        }
        // Add scene information
        if (analysisResult.scenes.length > 0) {
            const sceneDescriptions = analysisResult.scenes
                .map(scene => scene.description)
                .slice(0, 3); // Take first 3 scenes
            parts.push(`场景：${sceneDescriptions.join('、')}`);
        }
        // Add object information
        if (analysisResult.objects.length > 0) {
            const objectNames = analysisResult.objects
                .map(obj => obj.name)
                .slice(0, 5); // Take first 5 objects
            parts.push(`物体：${objectNames.join('、')}`);
        }
        return parts.join('\n');
    }
    /**
     * Perform folder matching using AI analysis
     */
    async performFolderMatching(contentDescription, folders) {
        const matches = [];
        // First, perform rule-based matching
        const ruleBasedMatches = this.performRuleBasedMatching(contentDescription, folders);
        matches.push(...ruleBasedMatches);
        // Then, perform semantic matching if enabled
        if (this.config.enableSemanticAnalysis) {
            const semanticMatches = await this.performSemanticMatching(contentDescription, folders);
            matches.push(...semanticMatches);
        }
        // Merge and deduplicate matches
        return this.mergeMatches(matches);
    }
    /**
     * Perform rule-based folder matching
     */
    performRuleBasedMatching(contentDescription, folders) {
        const matches = [];
        const contentLower = contentDescription.toLowerCase();
        for (const folderPath of folders) {
            const folderName = path.basename(folderPath).toLowerCase();
            const confidence = this.calculateRuleBasedConfidence(contentLower, folderName);
            if (confidence > 0) {
                matches.push({
                    folderPath,
                    folderName: path.basename(folderPath),
                    confidence,
                    reasons: this.generateRuleBasedReasons(contentLower, folderName),
                    semanticScore: 0,
                    relevanceScore: confidence,
                    action: this.determineAction(confidence)
                });
            }
        }
        return matches;
    }
    /**
     * Calculate rule-based confidence score
     */
    calculateRuleBasedConfidence(content, folderName) {
        let confidence = 0;
        // Direct keyword matching
        const keywords = this.extractKeywords(content);
        for (const keyword of keywords) {
            if (folderName.includes(keyword)) {
                confidence += 0.3;
            }
        }
        // Category matching
        const categories = this.extractCategories(content);
        for (const category of categories) {
            if (folderName.includes(category)) {
                confidence += 0.4;
            }
        }
        // Color matching
        const colors = this.extractColors(content);
        for (const color of colors) {
            if (folderName.includes(color)) {
                confidence += 0.2;
            }
        }
        return Math.min(1.0, confidence);
    }
    /**
     * Generate reasons for rule-based matching
     */
    generateRuleBasedReasons(content, folderName) {
        const reasons = [];
        const keywords = this.extractKeywords(content);
        for (const keyword of keywords) {
            if (folderName.includes(keyword)) {
                reasons.push(`关键词匹配：${keyword}`);
            }
        }
        const categories = this.extractCategories(content);
        for (const category of categories) {
            if (folderName.includes(category)) {
                reasons.push(`类别匹配：${category}`);
            }
        }
        return reasons;
    }
    /**
     * Perform semantic matching using Gemini AI
     */
    async performSemanticMatching(contentDescription, folders) {
        try {
            await this.initializeGeminiClient();
            const folderNames = folders.map(f => path.basename(f));
            const prompt = `请分析以下视频内容描述，并为其推荐最合适的文件夹：

视频内容描述：
${contentDescription}

可选文件夹：
${folderNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}

请为每个文件夹评分（0-1），并说明匹配原因。返回JSON格式：
{
  "matches": [
    {
      "folderName": "文件夹名称",
      "score": 0.8,
      "reasons": ["匹配原因1", "匹配原因2"]
    }
  ]
}`;
            const response = await this.geminiClient.generateText(prompt, 'gemini-2.5-flash', {
                temperature: 0.3,
                maxOutputTokens: 2048
            });
            if (response) {
                return this.parseSemanticMatchingResponse(response, folders);
            }
            return [];
        }
        catch (error) {
            console.warn('Semantic matching failed:', error.message);
            return [];
        }
    }
    /**
     * Parse semantic matching response
     */
    parseSemanticMatchingResponse(response, folders) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                return [];
            const data = JSON.parse(jsonMatch[0]);
            const matches = [];
            if (data.matches && Array.isArray(data.matches)) {
                for (const match of data.matches) {
                    const folderPath = folders.find(f => path.basename(f) === match.folderName);
                    if (folderPath && match.score > 0) {
                        matches.push({
                            folderPath,
                            folderName: match.folderName,
                            confidence: match.score,
                            reasons: match.reasons || [],
                            semanticScore: match.score,
                            relevanceScore: match.score,
                            action: this.determineAction(match.score)
                        });
                    }
                }
            }
            return matches;
        }
        catch (error) {
            console.warn('Failed to parse semantic matching response:', error.message);
            return [];
        }
    }
    /**
     * Merge and deduplicate matches
     */
    mergeMatches(matches) {
        const mergedMap = new Map();
        for (const match of matches) {
            const existing = mergedMap.get(match.folderPath);
            if (existing) {
                // Merge with existing match
                existing.confidence = Math.max(existing.confidence, match.confidence);
                existing.semanticScore = Math.max(existing.semanticScore, match.semanticScore);
                existing.relevanceScore = Math.max(existing.relevanceScore, match.relevanceScore);
                existing.reasons = [...new Set([...existing.reasons, ...match.reasons])];
                existing.action = this.determineAction(existing.confidence);
            }
            else {
                mergedMap.set(match.folderPath, { ...match });
            }
        }
        return Array.from(mergedMap.values());
    }
    /**
     * Determine recommended action based on confidence
     */
    determineAction(confidence) {
        if (confidence >= 0.8)
            return 'move';
        if (confidence >= 0.6)
            return 'copy';
        if (confidence >= 0.4)
            return 'link';
        return 'ignore';
    }
    /**
     * Extract keywords from content
     */
    extractKeywords(content) {
        const keywords = [];
        // Common product keywords
        const productKeywords = ['产品', '商品', '物品', '设备', '工具', '装置'];
        for (const keyword of productKeywords) {
            if (content.includes(keyword)) {
                keywords.push(keyword);
            }
        }
        return keywords;
    }
    /**
     * Extract categories from content
     */
    extractCategories(content) {
        const categories = [];
        // Common categories
        const categoryMap = {
            '电子': ['电子', '数码', '手机', '电脑', '相机'],
            '服装': ['服装', '衣服', '鞋子', '包包', '配饰'],
            '家居': ['家居', '家具', '装饰', '厨具', '床品'],
            '美妆': ['美妆', '化妆品', '护肤', '香水', '彩妆'],
            '食品': ['食品', '零食', '饮料', '茶叶', '咖啡']
        };
        for (const [category, keywords] of Object.entries(categoryMap)) {
            for (const keyword of keywords) {
                if (content.includes(keyword)) {
                    categories.push(category);
                    break;
                }
            }
        }
        return categories;
    }
    /**
     * Extract colors from content
     */
    extractColors(content) {
        const colors = ['红', '蓝', '绿', '黄', '黑', '白', '灰', '紫', '粉', '橙'];
        return colors.filter(color => content.includes(color));
    }
    /**
     * Clear folder cache
     */
    clearCache() {
        this.folderCache.clear();
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
        this.clearCache(); // Clear cache when config changes
    }
}
/**
 * Convenience function to find matching folders
 */
async function findMatchingFoldersForVideo(analysisResult, config) {
    const matcher = new FolderMatcher(config);
    return matcher.findMatchingFolders(analysisResult);
}

/**
 * Report generation for video analysis results
 */
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
/**
 * Report generator class
 */
class ReportGenerator {
    /**
     * Generate comprehensive analysis report
     */
    async generateReport(videoResults, folderMatches = {}, options) {
        try {
            // Ensure output directory exists
            await this.ensureOutputDirectory(options.outputPath);
            // Build report data
            const report = this.buildReport(videoResults, folderMatches, options);
            // Generate report based on format
            let reportContent;
            switch (options.format) {
                case 'xml':
                    reportContent = this.generateXMLReport(report);
                    break;
                case 'json':
                    reportContent = this.generateJSONReport(report);
                    break;
                case 'csv':
                    reportContent = this.generateCSVReport(report);
                    break;
                case 'html':
                    reportContent = this.generateHTMLReport(report);
                    break;
                default:
                    throw new Error(`Unsupported format: ${options.format}`);
            }
            // Write report to file
            await writeFile(options.outputPath, reportContent, 'utf-8');
            return options.outputPath;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Report generation failed: ${error.message}`, 'REPORT_GENERATION_FAILED', error);
        }
    }
    /**
     * Build report data structure
     */
    buildReport(videoResults, folderMatches, options) {
        const totalProcessingTime = videoResults.reduce((sum, result) => sum + result.processingTime, 0);
        const totalScenes = videoResults.reduce((sum, result) => sum + result.scenes.length, 0);
        const totalObjects = videoResults.reduce((sum, result) => sum + result.objects.length, 0);
        // Extract common themes
        const allKeywords = videoResults.flatMap(result => result.summary.keywords);
        const keywordCounts = this.countOccurrences(allKeywords);
        const commonThemes = Object.entries(keywordCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([keyword]) => keyword);
        // Extract recommended categories
        const allTopics = videoResults.flatMap(result => result.summary.topics);
        const topicCounts = this.countOccurrences(allTopics);
        const recommendedCategories = Object.entries(topicCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([topic]) => topic);
        return {
            metadata: {
                generatedAt: new Date(),
                version: '1.0.0',
                totalVideos: videoResults.length,
                totalProcessingTime
            },
            videoResults,
            folderMatches: options.includeFolderMatching ? folderMatches : undefined,
            summary: {
                totalScenes,
                totalObjects,
                commonThemes,
                recommendedCategories
            },
            exportOptions: {
                format: options.format,
                includeImages: false,
                includeThumbnails: options.includeThumbnails || false
            }
        };
    }
    /**
     * Generate XML report
     */
    generateXMLReport(report) {
        const xml = ['<?xml version="1.0" encoding="UTF-8"?>'];
        xml.push('<VideoAnalysisReport>');
        // Metadata
        xml.push('  <Metadata>');
        xml.push(`    <GeneratedAt>${report.metadata.generatedAt.toISOString()}</GeneratedAt>`);
        xml.push(`    <Version>${report.metadata.version}</Version>`);
        xml.push(`    <TotalVideos>${report.metadata.totalVideos}</TotalVideos>`);
        xml.push(`    <TotalProcessingTime>${report.metadata.totalProcessingTime}</TotalProcessingTime>`);
        xml.push('  </Metadata>');
        // Summary
        xml.push('  <Summary>');
        xml.push(`    <TotalScenes>${report.summary.totalScenes}</TotalScenes>`);
        xml.push(`    <TotalObjects>${report.summary.totalObjects}</TotalObjects>`);
        xml.push('    <CommonThemes>');
        report.summary.commonThemes.forEach(theme => {
            xml.push(`      <Theme>${this.escapeXML(theme)}</Theme>`);
        });
        xml.push('    </CommonThemes>');
        xml.push('    <RecommendedCategories>');
        report.summary.recommendedCategories.forEach(category => {
            xml.push(`      <Category>${this.escapeXML(category)}</Category>`);
        });
        xml.push('    </RecommendedCategories>');
        xml.push('  </Summary>');
        // Video Results
        xml.push('  <VideoResults>');
        report.videoResults.forEach((result, index) => {
            xml.push(`    <Video id="${index + 1}">`);
            xml.push(`      <FileName>${this.escapeXML(result.metadata.file.name)}</FileName>`);
            xml.push(`      <FilePath>${this.escapeXML(result.metadata.file.path)}</FilePath>`);
            xml.push(`      <FileSize>${result.metadata.file.size}</FileSize>`);
            xml.push(`      <AnalyzedAt>${result.analyzedAt.toISOString()}</AnalyzedAt>`);
            xml.push(`      <ProcessingTime>${result.processingTime}</ProcessingTime>`);
            // Summary
            xml.push('      <Summary>');
            xml.push(`        <Description>${this.escapeXML(result.summary.description)}</Description>`);
            xml.push('        <Keywords>');
            result.summary.keywords.forEach(keyword => {
                xml.push(`          <Keyword>${this.escapeXML(keyword)}</Keyword>`);
            });
            xml.push('        </Keywords>');
            xml.push('        <Topics>');
            result.summary.topics.forEach(topic => {
                xml.push(`          <Topic>${this.escapeXML(topic)}</Topic>`);
            });
            xml.push('        </Topics>');
            xml.push('      </Summary>');
            // Scenes
            xml.push('      <Scenes>');
            result.scenes.forEach((scene, sceneIndex) => {
                xml.push(`        <Scene id="${sceneIndex + 1}">`);
                xml.push(`          <StartTime>${scene.startTime}</StartTime>`);
                xml.push(`          <EndTime>${scene.endTime}</EndTime>`);
                xml.push(`          <Duration>${scene.duration}</Duration>`);
                xml.push(`          <Description>${this.escapeXML(scene.description)}</Description>`);
                xml.push(`          <Confidence>${scene.confidence}</Confidence>`);
                xml.push('        </Scene>');
            });
            xml.push('      </Scenes>');
            // Objects
            xml.push('      <Objects>');
            result.objects.forEach((object, objectIndex) => {
                xml.push(`        <Object id="${objectIndex + 1}">`);
                xml.push(`          <Name>${this.escapeXML(object.name)}</Name>`);
                xml.push(`          <Category>${this.escapeXML(object.category)}</Category>`);
                xml.push(`          <Confidence>${object.confidence}</Confidence>`);
                xml.push('        </Object>');
            });
            xml.push('      </Objects>');
            // Product Features (if available)
            if (result.productFeatures) {
                xml.push('      <ProductFeatures>');
                xml.push('        <Appearance>');
                xml.push('          <Colors>');
                result.productFeatures.appearance.colors.forEach(color => {
                    xml.push(`            <Color>${this.escapeXML(color)}</Color>`);
                });
                xml.push('          </Colors>');
                xml.push(`          <Shape>${this.escapeXML(result.productFeatures.appearance.shape)}</Shape>`);
                xml.push(`          <Size>${this.escapeXML(result.productFeatures.appearance.size)}</Size>`);
                xml.push(`          <Style>${this.escapeXML(result.productFeatures.appearance.style)}</Style>`);
                xml.push('        </Appearance>');
                xml.push('        <Materials>');
                result.productFeatures.materials.forEach(material => {
                    xml.push(`          <Material>${this.escapeXML(material)}</Material>`);
                });
                xml.push('        </Materials>');
                xml.push('        <Functionality>');
                result.productFeatures.functionality.forEach(func => {
                    xml.push(`          <Function>${this.escapeXML(func)}</Function>`);
                });
                xml.push('        </Functionality>');
                xml.push('      </ProductFeatures>');
            }
            xml.push('    </Video>');
        });
        xml.push('  </VideoResults>');
        // Folder Matches (if included)
        if (report.folderMatches) {
            xml.push('  <FolderMatches>');
            Object.entries(report.folderMatches).forEach(([videoPath, matches]) => {
                xml.push(`    <VideoMatches videoPath="${this.escapeXML(videoPath)}">`);
                matches.forEach((match, matchIndex) => {
                    xml.push(`      <Match id="${matchIndex + 1}">`);
                    xml.push(`        <FolderPath>${this.escapeXML(match.folderPath)}</FolderPath>`);
                    xml.push(`        <FolderName>${this.escapeXML(match.folderName)}</FolderName>`);
                    xml.push(`        <Confidence>${match.confidence}</Confidence>`);
                    xml.push(`        <Action>${match.action}</Action>`);
                    xml.push('        <Reasons>');
                    match.reasons.forEach(reason => {
                        xml.push(`          <Reason>${this.escapeXML(reason)}</Reason>`);
                    });
                    xml.push('        </Reasons>');
                    xml.push('      </Match>');
                });
                xml.push('    </VideoMatches>');
            });
            xml.push('  </FolderMatches>');
        }
        xml.push('</VideoAnalysisReport>');
        return xml.join('\n');
    }
    /**
     * Generate JSON report
     */
    generateJSONReport(report) {
        return JSON.stringify(report, null, 2);
    }
    /**
     * Generate CSV report
     */
    generateCSVReport(report) {
        const headers = [
            'FileName',
            'FilePath',
            'FileSize',
            'ProcessingTime',
            'ScenesCount',
            'ObjectsCount',
            'Keywords',
            'Topics',
            'Description'
        ];
        const rows = [headers.join(',')];
        report.videoResults.forEach(result => {
            const row = [
                this.escapeCSV(result.metadata.file.name),
                this.escapeCSV(result.metadata.file.path),
                result.metadata.file.size.toString(),
                result.processingTime.toString(),
                result.scenes.length.toString(),
                result.objects.length.toString(),
                this.escapeCSV(result.summary.keywords.join('; ')),
                this.escapeCSV(result.summary.topics.join('; ')),
                this.escapeCSV(result.summary.description)
            ];
            rows.push(row.join(','));
        });
        return rows.join('\n');
    }
    /**
     * Generate HTML report
     */
    generateHTMLReport(report) {
        const html = ['<!DOCTYPE html>'];
        html.push('<html lang="zh-CN">');
        html.push('<head>');
        html.push('  <meta charset="UTF-8">');
        html.push('  <meta name="viewport" content="width=device-width, initial-scale=1.0">');
        html.push('  <title>视频分析报告</title>');
        html.push('  <style>');
        html.push('    body { font-family: Arial, sans-serif; margin: 20px; }');
        html.push('    .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }');
        html.push('    .summary { margin: 20px 0; }');
        html.push('    .video-result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }');
        html.push('    .scenes, .objects { margin: 10px 0; }');
        html.push('    .scene, .object { background: #f9f9f9; padding: 5px; margin: 5px 0; border-radius: 3px; }');
        html.push('  </style>');
        html.push('</head>');
        html.push('<body>');
        // Header
        html.push('  <div class="header">');
        html.push('    <h1>视频分析报告</h1>');
        html.push(`    <p>生成时间：${report.metadata.generatedAt.toLocaleString('zh-CN')}</p>`);
        html.push(`    <p>总视频数：${report.metadata.totalVideos}</p>`);
        html.push(`    <p>总处理时间：${report.metadata.totalProcessingTime}ms</p>`);
        html.push('  </div>');
        // Summary
        html.push('  <div class="summary">');
        html.push('    <h2>分析摘要</h2>');
        html.push(`    <p>总场景数：${report.summary.totalScenes}</p>`);
        html.push(`    <p>总物体数：${report.summary.totalObjects}</p>`);
        html.push(`    <p>常见主题：${report.summary.commonThemes.join('、')}</p>`);
        html.push('  </div>');
        // Video Results
        html.push('  <div class="video-results">');
        html.push('    <h2>视频分析结果</h2>');
        report.videoResults.forEach((result, index) => {
            html.push(`    <div class="video-result">`);
            html.push(`      <h3>${index + 1}. ${this.escapeHTML(result.metadata.file.name)}</h3>`);
            html.push(`      <p><strong>文件路径：</strong>${this.escapeHTML(result.metadata.file.path)}</p>`);
            html.push(`      <p><strong>文件大小：</strong>${result.metadata.file.size} bytes</p>`);
            html.push(`      <p><strong>处理时间：</strong>${result.processingTime}ms</p>`);
            html.push(`      <p><strong>描述：</strong>${this.escapeHTML(result.summary.description)}</p>`);
            if (result.scenes.length > 0) {
                html.push('      <div class="scenes">');
                html.push('        <h4>场景分析</h4>');
                result.scenes.forEach(scene => {
                    html.push(`        <div class="scene">`);
                    html.push(`          <strong>${scene.startTime}s - ${scene.endTime}s:</strong> ${this.escapeHTML(scene.description)}`);
                    html.push('        </div>');
                });
                html.push('      </div>');
            }
            if (result.objects.length > 0) {
                html.push('      <div class="objects">');
                html.push('        <h4>物体识别</h4>');
                result.objects.forEach(object => {
                    html.push(`        <div class="object">`);
                    html.push(`          <strong>${this.escapeHTML(object.name)}</strong> (${object.category}) - 置信度: ${object.confidence.toFixed(2)}`);
                    html.push('        </div>');
                });
                html.push('      </div>');
            }
            html.push('    </div>');
        });
        html.push('  </div>');
        html.push('</body>');
        html.push('</html>');
        return html.join('\n');
    }
    /**
     * Ensure output directory exists
     */
    async ensureOutputDirectory(filePath) {
        const dir = path.dirname(filePath);
        try {
            await mkdir(dir, { recursive: true });
        }
        catch (error) {
            // Directory might already exist
        }
    }
    /**
     * Count occurrences of items in array
     */
    countOccurrences(items) {
        const counts = {};
        items.forEach(item => {
            counts[item] = (counts[item] || 0) + 1;
        });
        return counts;
    }
    /**
     * Escape XML special characters
     */
    escapeXML(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }
    /**
     * Escape HTML special characters
     */
    escapeHTML(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;');
    }
    /**
     * Escape CSV special characters
     */
    escapeCSV(text) {
        if (text.includes(',') || text.includes('"') || text.includes('\n')) {
            return `"${text.replace(/"/g, '""')}"`;
        }
        return text;
    }
}
/**
 * Convenience function to generate report
 */
async function generateAnalysisReport(videoResults, folderMatches, options) {
    const generator = new ReportGenerator();
    return generator.generateReport(videoResults, folderMatches, options);
}

/**
 * GPT-4 frame analysis mode (placeholder implementation)
 * This module provides frame extraction and analysis functionality
 */
/**
 * Frame analyzer class for GPT-4 mode
 */
class FrameAnalyzer {
    /**
     * Extract and analyze frames from video
     * Note: This is a placeholder implementation
     * In a real-world scenario, you would use ffmpeg or similar tools
     */
    async analyzeFrames(videoFile, options = {}) {
        try {
            // Default options
            const frameSamplingInterval = options.frameSamplingInterval || 1; // 1 second
            const maxFrames = options.maxFrames || 30;
            // Simulate frame extraction and analysis
            const frameAnalysis = [];
            // Estimate video duration (placeholder - would need actual video metadata)
            const estimatedDuration = 60; // 60 seconds
            const totalFrames = Math.min(Math.floor(estimatedDuration / frameSamplingInterval), maxFrames);
            for (let i = 0; i < totalFrames; i++) {
                const timestamp = i * frameSamplingInterval;
                frameAnalysis.push({
                    timestamp,
                    description: `Frame at ${timestamp}s - placeholder description`,
                    objects: [
                        {
                            name: 'placeholder_object',
                            category: 'general',
                            confidence: 0.8
                        }
                    ],
                    quality: 0.8,
                    type: i === 0 ? 'keyframe' : 'regular'
                });
            }
            return frameAnalysis;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Frame analysis failed for ${videoFile.name}: ${error.message}`, 'FRAME_ANALYSIS_FAILED', error);
        }
    }
    /**
     * Extract key frames from video
     * Note: This is a placeholder implementation
     */
    async extractKeyFrames(videoFile, interval = 1) {
        // Placeholder implementation
        const keyFrames = [];
        // Simulate key frame extraction
        for (let i = 0; i < 30; i += interval) {
            keyFrames.push({
                timestamp: i,
                quality: 0.8 + Math.random() * 0.2 // Random quality between 0.8-1.0
            });
        }
        return keyFrames;
    }
    /**
     * Analyze single frame
     * Note: This would integrate with GPT-4 Vision API in a real implementation
     */
    async analyzeSingleFrame(frameData, timestamp) {
        // Placeholder implementation
        return {
            timestamp,
            description: `Placeholder frame analysis at ${timestamp}s`,
            objects: [
                {
                    name: 'detected_object',
                    category: 'general',
                    confidence: 0.7
                }
            ],
            quality: 0.8,
            type: 'regular'
        };
    }
}
/**
 * Convenience function to analyze video frames
 */
async function analyzeVideoFrames(videoFile, options) {
    const analyzer = new FrameAnalyzer();
    return analyzer.analyzeFrames(videoFile, options);
}

/**
 * Main VideoAnalyzer class - orchestrates all video analysis functionality
 */
/**
 * Main VideoAnalyzer class
 */
class VideoAnalyzer {
    constructor(config = {}) {
        this.uploader = null;
        this.folderMatcher = null;
        this.config = config;
        // Initialize components
        this.scanner = new VideoScanner();
        this.analysisEngine = new AnalysisEngine();
        this.productAnalyzer = new ProductAnalyzer();
        this.reportGenerator = new ReportGenerator();
        this.frameAnalyzer = new FrameAnalyzer();
    }
    /**
     * Scan directory for video files
     */
    async scanDirectory(directoryPath, options) {
        try {
            return await this.scanner.scanDirectory(directoryPath);
        }
        catch (error) {
            throw new VideoAnalyzerError(`Directory scan failed: ${error.message}`, 'SCAN_FAILED', error);
        }
    }
    /**
     * Analyze a single video file
     */
    async analyzeVideo(videoFile, mode, options = {}, onProgress) {
        try {
            // Initialize uploader if not already done
            if (!this.uploader) {
                this.initializeUploader();
            }
            const progress = {
                step: 'Starting analysis',
                progress: 0,
                currentFile: videoFile.name,
                stage: 'upload'
            };
            onProgress?.(progress);
            // Upload video to Gemini
            progress.step = 'Uploading video';
            progress.progress = 10;
            onProgress?.(progress);
            const uploadResult = await this.uploader.uploadVideo(videoFile);
            if (!uploadResult.success) {
                throw new Error(`Upload failed: ${uploadResult.error}`);
            }
            // Perform analysis based on mode
            let analysisResult;
            if (mode.type === 'gemini') {
                analysisResult = await this.analysisEngine.analyzeVideo(videoFile, uploadResult.gcsPath, mode, options, onProgress);
            }
            else if (mode.type === 'gpt4') {
                // GPT-4 mode with frame analysis
                progress.step = 'Analyzing frames';
                progress.stage = 'analysis';
                onProgress?.(progress);
                const frameAnalysis = await this.frameAnalyzer.analyzeFrames(videoFile, options);
                // Create basic analysis result for GPT-4 mode
                analysisResult = {
                    metadata: {
                        file: videoFile,
                        technical: {
                            codec: 'unknown',
                            container: videoFile.format || 'unknown',
                            hasAudio: true
                        }
                    },
                    analysisMode: mode,
                    scenes: [],
                    objects: [],
                    summary: {
                        description: 'GPT-4 frame-by-frame analysis completed',
                        highlights: [],
                        topics: [],
                        keywords: []
                    },
                    frameAnalysis,
                    analyzedAt: new Date(),
                    processingTime: Date.now()
                };
            }
            else {
                throw new Error(`Unsupported analysis mode: ${mode.type}`);
            }
            // Add product analysis if enabled
            if (options.enableProductAnalysis && mode.type === 'gemini') {
                progress.step = 'Analyzing product features';
                progress.progress = 80;
                onProgress?.(progress);
                analysisResult.productFeatures = await this.productAnalyzer.analyzeProductFeatures(videoFile, uploadResult.gcsPath, options);
            }
            progress.step = 'Analysis completed';
            progress.progress = 100;
            progress.stage = 'complete';
            onProgress?.(progress);
            return analysisResult;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Video analysis failed: ${error.message}`, 'ANALYSIS_FAILED', error);
        }
    }
    /**
     * Analyze multiple videos in a directory
     */
    async analyzeDirectory(directoryPath, mode, scanOptions, analysisOptions, onProgress) {
        try {
            // Scan directory for videos
            const videoFiles = await this.scanDirectory(directoryPath, scanOptions);
            if (videoFiles.length === 0) {
                throw new Error('No video files found in directory');
            }
            const results = [];
            // Analyze each video
            for (let i = 0; i < videoFiles.length; i++) {
                const videoFile = videoFiles[i];
                const overallProgress = {
                    step: `Analyzing video ${i + 1}/${videoFiles.length}: ${videoFile.name}`,
                    progress: (i / videoFiles.length) * 100,
                    currentFile: videoFile.name,
                    stage: 'processing'
                };
                onProgress?.(overallProgress);
                try {
                    const result = await this.analyzeVideo(videoFile, mode, analysisOptions);
                    results.push(result);
                }
                catch (error) {
                    console.warn(`Failed to analyze ${videoFile.name}:`, error.message);
                }
            }
            return results;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Directory analysis failed: ${error.message}`, 'DIRECTORY_ANALYSIS_FAILED', error);
        }
    }
    /**
     * Find matching folders for analysis results
     */
    async findMatchingFolders(analysisResults, folderConfig) {
        try {
            if (!this.folderMatcher) {
                this.folderMatcher = new FolderMatcher(folderConfig);
            }
            const matches = {};
            for (const result of analysisResults) {
                const videoPath = result.metadata.file.path;
                matches[videoPath] = await this.folderMatcher.findMatchingFolders(result);
            }
            return matches;
        }
        catch (error) {
            throw new VideoAnalyzerError(`Folder matching failed: ${error.message}`, 'FOLDER_MATCHING_FAILED', error);
        }
    }
    /**
     * Generate analysis report
     */
    async generateReport(analysisResults, folderMatches = {}, reportOptions) {
        try {
            return await this.reportGenerator.generateReport(analysisResults, folderMatches, reportOptions);
        }
        catch (error) {
            throw new VideoAnalyzerError(`Report generation failed: ${error.message}`, 'REPORT_GENERATION_FAILED', error);
        }
    }
    /**
     * Complete workflow: scan, analyze, match folders, and generate report
     */
    async analyzeDirectoryComplete(directoryPath, mode, options = {}) {
        try {
            const { scanOptions, analysisOptions, folderConfig, reportOptions, onProgress } = options;
            // Step 1: Analyze directory
            onProgress?.({
                step: 'Analyzing videos',
                progress: 0,
                stage: 'processing'
            });
            const analysisResults = await this.analyzeDirectory(directoryPath, mode, scanOptions, analysisOptions, onProgress);
            // Step 2: Find matching folders (if config provided)
            let folderMatches = {};
            if (folderConfig) {
                onProgress?.({
                    step: 'Finding matching folders',
                    progress: 80,
                    stage: 'processing'
                });
                folderMatches = await this.findMatchingFolders(analysisResults, folderConfig);
            }
            // Step 3: Generate report (if options provided)
            let reportPath;
            if (reportOptions) {
                onProgress?.({
                    step: 'Generating report',
                    progress: 90,
                    stage: 'processing'
                });
                reportPath = await this.generateReport(analysisResults, folderMatches, reportOptions);
            }
            onProgress?.({
                step: 'Complete',
                progress: 100,
                stage: 'complete'
            });
            return {
                analysisResults,
                folderMatches,
                reportPath
            };
        }
        catch (error) {
            throw new VideoAnalyzerError(`Complete analysis workflow failed: ${error.message}`, 'WORKFLOW_FAILED', error);
        }
    }
    /**
     * Initialize uploader with configuration
     */
    initializeUploader() {
        const uploadConfig = {
            bucketName: this.config.upload?.bucketName || 'default-bucket',
            filePrefix: this.config.upload?.filePrefix || 'video-analysis/',
            chunkSize: this.config.upload?.chunkSize,
            maxRetries: this.config.upload?.maxRetries,
            onProgress: (progress) => {
                // Handle upload progress if needed
            }
        };
        this.uploader = new VideoUploader(uploadConfig);
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
        // Reinitialize components if needed
        if (config.upload && this.uploader) {
            this.initializeUploader();
        }
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Get analysis statistics
     */
    getAnalysisStatistics(results) {
        const totalVideos = results.length;
        const totalProcessingTime = results.reduce((sum, r) => sum + r.processingTime, 0);
        const averageProcessingTime = totalVideos > 0 ? totalProcessingTime / totalVideos : 0;
        const totalScenes = results.reduce((sum, r) => sum + r.scenes.length, 0);
        const totalObjects = results.reduce((sum, r) => sum + r.objects.length, 0);
        const qualityScores = results
            .map(r => r.qualityMetrics?.overallScore || 0)
            .filter(score => score > 0);
        const averageQualityScore = qualityScores.length > 0
            ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
            : 0;
        return {
            totalVideos,
            totalProcessingTime,
            averageProcessingTime,
            totalScenes,
            totalObjects,
            averageQualityScore
        };
    }
}
/**
 * Convenience function to create VideoAnalyzer instance
 */
function createVideoAnalyzer(config) {
    return new VideoAnalyzer(config);
}

export { ANALYSIS_PROMPTS, AnalysisEngine, DEFAULT_FOLDER_MATCH_CONFIG, DEFAULT_SCAN_OPTIONS, DEFAULT_UPLOAD_CONFIG, DEFAULT_VIDEO_EXTENSIONS, FolderMatcher, FrameAnalyzer, PRODUCT_ANALYSIS_PROMPTS, ProductAnalyzer, ReportGenerator, VideoAnalyzer, VideoScanner, VideoUploader, analyzeProductInVideo, analyzeVideoFrames, analyzeVideoWithGemini, createVideoAnalyzer, findMatchingFoldersForVideo, generateAnalysisReport, getVideoScanStatistics, scanVideoDirectory, uploadVideoToGemini, uploadVideosToGemini };
//# sourceMappingURL=index.esm.js.map
