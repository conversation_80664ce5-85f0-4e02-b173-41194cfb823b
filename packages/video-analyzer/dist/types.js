/**
 * Core types and interfaces for video analysis
 */
export class VideoAnalyzerError extends <PERSON>rror {
    constructor(message, code, details, file, stage) {
        super(message);
        this.name = 'VideoAnalyzerError';
        this.code = code;
        this.details = details;
        this.file = file;
        this.stage = stage;
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, VideoAnalyzerError);
        }
    }
}
//# sourceMappingURL=types.js.map