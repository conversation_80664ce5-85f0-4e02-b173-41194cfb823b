{"version": 3, "file": "product-analyzer.js", "sourceRoot": "", "sources": ["../src/product-analyzer.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAIL,kBAAkB,EACnB,MAAM,SAAS,CAAC;AAEjB;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG;IACtC,UAAU,EAAE;;;;;;;qBAOO;IAEnB,SAAS,EAAE;;;;;;;kBAOK;IAEhB,aAAa,EAAE;;;;;;;kBAOC;IAEhB,eAAe,EAAE;;;;;;;oBAOC;IAElB,cAAc,EAAE;;;;;;;oBAOE;IAElB,UAAU,EAAE;;;;;;;kBAOI;CACjB,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,eAAe;IAG1B;QAFQ,iBAAY,GAAQ,IAAI,CAAC;QAG/B,gDAAgD;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,MAAM,SAAS,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,SAAoB,EACpB,OAAe,EACf,UAA2B,EAAE;QAE7B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEpC,qCAAqC;YACrC,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBAC/C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBAClC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;gBACnC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBAClC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;aAChC,CAAC,CAAC;YAEH,kBAAkB;YAClB,OAAO,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;QAEtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kBAAkB,CAC1B,+BAA+B,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,EAC/E,yBAAyB,EACzB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,KAAc;QACpC,OAAO,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC7C,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,CAAC,UAAU,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC5C,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,CAAC,SAAS,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAChD,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,CAAC,aAAa,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,OAAe;QACjD,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,CAAC,eAAe,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAChD,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,CAAC,cAAc,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC7C,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,CAAC,UAAU,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,MAAc;QAC9D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG;gBACf;oBACE,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,MAAM;yBACb;wBACD;4BACE,QAAQ,EAAE;gCACR,QAAQ,EAAE,WAAW;gCACrB,OAAO,EAAE,QAAQ,OAAO,EAAE;6BAC3B;yBACF;qBACF;iBACF;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CACtD,QAAQ,EACR,kBAAkB,EAClB;gBACE,WAAW,EAAE,GAAG,EAAE,iDAAiD;gBACnE,eAAe,EAAE,IAAI;gBACrB,IAAI,EAAE,GAAG;aACV,CACF,CAAC;YAEF,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBACjG,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC3E,OAAO,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,YAAoB;QACvD,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACpD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;YAED,kCAAkC;YAClC,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAErD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,IAAY;QAC3C,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,iBAAiB;QACjB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACpD,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CACvC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CACnC,CAAC;QACJ,CAAC;QAED,oBAAoB;QACpB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACvD,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CACnC,CAAC;QACJ,CAAC;QAED,wBAAwB;QACxB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACvD,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,CAAC,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CACjD,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CACnC,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAoC;QACjE,MAAM,eAAe,GAAoB;YACvC,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,EAAE;gBACT,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,EAAE;aACV;YACD,SAAS,EAAE,EAAE;YACb,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;YAClB,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClD,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;gBAE1B,QAAQ,KAAK,EAAE,CAAC;oBACd,KAAK,CAAC,EAAE,aAAa;wBACnB,IAAI,IAAI,CAAC,MAAM;4BAAE,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;wBACjE,IAAI,IAAI,CAAC,KAAK;4BAAE,eAAe,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;wBAC9D,IAAI,IAAI,CAAC,IAAI;4BAAE,eAAe,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;wBAC3D,IAAI,IAAI,CAAC,KAAK;4BAAE,eAAe,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;wBAC9D,MAAM;oBAER,KAAK,CAAC,EAAE,YAAY;wBAClB,IAAI,IAAI,CAAC,SAAS;4BAAE,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;wBAC/D,MAAM;oBAER,KAAK,CAAC,EAAE,gBAAgB;wBACtB,IAAI,IAAI,CAAC,aAAa;4BAAE,eAAe,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC3E,MAAM;oBAER,KAAK,CAAC,EAAE,kBAAkB;wBACxB,IAAI,IAAI,CAAC,cAAc;4BAAE,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;wBAC9E,IAAI,IAAI,CAAC,cAAc;4BAAE,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;wBAC9E,MAAM;oBAER,KAAK,CAAC,EAAE,iBAAiB;wBACvB,IAAI,IAAI,CAAC,aAAa;4BAAE,eAAe,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC3E,MAAM;oBAER,KAAK,CAAC,EAAE,aAAa;wBACnB,sDAAsD;wBACtD,MAAM;gBACV,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,SAAoB,EACpB,OAAe;QAQf,MAAM,MAAM,GAAG;;;;;;;kBAOD,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE9D,OAAO;gBACL,QAAQ,EAAE,MAAM,EAAE,QAAQ,IAAI,SAAS;gBACvC,WAAW,EAAE,MAAM,EAAE,WAAW,IAAI,SAAS;gBAC7C,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE;gBACxB,UAAU,EAAE,MAAM,EAAE,UAAU,IAAI,SAAS;gBAC3C,YAAY,EAAE,MAAM,EAAE,YAAY,IAAI,SAAS;aAChD,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,kBAAkB,CAC1B,+BAA+B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,EAC5D,2BAA2B,EAC3B,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,0BAA0B,CAAC,QAAyB;QAClD,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,KAAK,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,gBAAgB;QAChB,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,oBAAoB;QACpB,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,sBAAsB;QACtB,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,KAAK,CAAC,IAAI,CAAC,QAAQ,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,SAAoB,EACpB,OAAe,EACf,OAAyB;IAEzB,MAAM,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;IACvC,OAAO,QAAQ,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC"}