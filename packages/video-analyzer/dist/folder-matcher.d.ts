/**
 * Smart folder matching system for video categorization
 */
import { VideoAnalysisResult, FolderMatchResult } from './types';
/**
 * Folder matching configuration
 */
export interface FolderMatchConfig {
    /** Base directory to scan for folders */
    baseDirectory: string;
    /** Maximum depth to scan for folders */
    maxDepth?: number;
    /** Minimum confidence score to include in results */
    minConfidence?: number;
    /** Maximum number of matches to return */
    maxMatches?: number;
    /** Whether to include semantic analysis */
    enableSemanticAnalysis?: boolean;
}
/**
 * Default folder match configuration
 */
export declare const DEFAULT_FOLDER_MATCH_CONFIG: Required<Omit<FolderMatchConfig, 'baseDirectory'>>;
/**
 * Smart folder matcher class
 */
export declare class FolderMatcher {
    private config;
    private geminiClient;
    private folderCache;
    constructor(config: FolderMatchConfig);
    /**
     * Initialize Gemini client
     */
    private initializeGeminiClient;
    /**
     * Find matching folders for video analysis result
     */
    findMatchingFolders(analysisResult: VideoAnalysisResult): Promise<FolderMatchResult[]>;
    /**
     * Scan for available folders
     */
    private scanFolders;
    /**
     * Recursively scan folders
     */
    private scanFoldersRecursive;
    /**
     * Generate content description for matching
     */
    private generateContentDescription;
    /**
     * Perform folder matching using AI analysis
     */
    private performFolderMatching;
    /**
     * Perform rule-based folder matching
     */
    private performRuleBasedMatching;
    /**
     * Calculate rule-based confidence score
     */
    private calculateRuleBasedConfidence;
    /**
     * Generate reasons for rule-based matching
     */
    private generateRuleBasedReasons;
    /**
     * Perform semantic matching using Gemini AI
     */
    private performSemanticMatching;
    /**
     * Parse semantic matching response
     */
    private parseSemanticMatchingResponse;
    private getErrorMessage;
    /**
     * Merge and deduplicate matches
     */
    private mergeMatches;
    /**
     * Determine recommended action based on confidence
     */
    private determineAction;
    /**
     * Extract keywords from content
     */
    private extractKeywords;
    /**
     * Extract categories from content
     */
    private extractCategories;
    /**
     * Extract colors from content
     */
    private extractColors;
    /**
     * Clear folder cache
     */
    clearCache(): void;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<FolderMatchConfig>): void;
}
/**
 * Convenience function to find matching folders
 */
export declare function findMatchingFoldersForVideo(analysisResult: VideoAnalysisResult, config: FolderMatchConfig): Promise<FolderMatchResult[]>;
//# sourceMappingURL=folder-matcher.d.ts.map