/**
 * Video file uploader for Gemini AI
 */
import { VideoFile, UploadProgress } from './types';
/**
 * Upload configuration options
 */
export interface UploadConfig {
    /** GCS bucket name for uploads */
    bucketName: string;
    /** File prefix for organization */
    filePrefix: string;
    /** Chunk size for large file uploads (bytes) */
    chunkSize?: number;
    /** Maximum number of retry attempts */
    maxRetries?: number;
    /** Timeout for upload operations (ms) */
    timeout?: number;
    /** Progress callback */
    onProgress?: (progress: UploadProgress) => void;
}
/**
 * Default upload configuration
 */
export declare const DEFAULT_UPLOAD_CONFIG: Required<Omit<UploadConfig, 'bucketName' | 'filePrefix'>>;
/**
 * Upload result information
 */
export interface UploadResult {
    /** Original video file */
    videoFile: VideoFile;
    /** Upload success status */
    success: boolean;
    /** GCS file path */
    gcsPath?: string;
    /** Upload duration in milliseconds */
    uploadTime: number;
    /** File size uploaded */
    bytesUploaded: number;
    /** Error information if upload failed */
    error?: string;
    /** Upload metadata */
    metadata?: {
        uploadId: string;
        timestamp: Date;
        checksum?: string;
    };
}
/**
 * Video uploader class
 */
export declare class VideoUploader {
    private config;
    constructor(config: UploadConfig);
    /**
     * Upload a single video file to Gemini
     */
    uploadVideo(videoFile: VideoFile): Promise<UploadResult>;
    private getErrorMessage;
    /**
     * Upload multiple video files
     */
    uploadVideos(videoFiles: VideoFile[]): Promise<UploadResult[]>;
    /**
     * Validate file before upload
     */
    private validateFileForUpload;
    /**
     * Read video file into buffer
     */
    private readVideoFile;
    /**
     * Upload with retry logic
     */
    private uploadWithRetry;
    /**
     * Generate GCS path for video file
     */
    private generateGcsPath;
    /**
     * Generate unique upload ID
     */
    private generateUploadId;
    /**
     * Calculate file checksum for verification
     */
    private calculateChecksum;
    /**
     * Get upload statistics
     */
    getUploadStatistics(results: UploadResult[]): {
        totalFiles: number;
        successfulUploads: number;
        failedUploads: number;
        totalBytesUploaded: number;
        averageUploadTime: number;
        successRate: number;
    };
    /**
     * Update upload configuration
     */
    updateConfig(config: Partial<UploadConfig>): void;
    /**
     * Get current upload configuration
     */
    getConfig(): UploadConfig;
}
/**
 * Convenience function to upload a single video
 */
export declare function uploadVideoToGemini(videoFile: VideoFile, config: UploadConfig): Promise<UploadResult>;
/**
 * Convenience function to upload multiple videos
 */
export declare function uploadVideosToGemini(videoFiles: VideoFile[], config: UploadConfig): Promise<UploadResult[]>;
//# sourceMappingURL=video-uploader.d.ts.map