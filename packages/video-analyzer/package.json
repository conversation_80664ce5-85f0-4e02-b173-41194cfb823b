{"name": "@mixvideo/video-analyzer", "version": "1.0.0", "description": "Video analysis toolkit using Gemini 2.0 Flash for intelligent video feature extraction", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["video", "analysis", "gemini", "ai", "computer-vision", "typescript"], "author": "MixVideo Team", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.21.0", "axios": "^1.6.0", "form-data": "^4.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "rollup": "^4.0.0", "rollup-plugin-dts": "^6.0.0", "ts-jest": "^29.1.0", "tslib": "^2.8.1", "typescript": "^5.0.0"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "https://github.com/imeepos/verve.git", "directory": "packages/video-analyzer"}, "engines": {"node": ">=16.0.0"}}