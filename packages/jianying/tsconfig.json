{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmitOnError": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}