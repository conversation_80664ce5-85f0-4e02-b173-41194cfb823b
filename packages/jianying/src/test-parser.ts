#!/usr/bin/env ts-node

/**
 * 测试剪映草稿解析器
 */

import { parseJianyingDraft, formatVideoInfo, exportToJson } from './parse-draft';
import * as path from 'path';

async function testParser() {
  try {
    const draftPath = path.join(__dirname, 'draft_content.json');
    const outputPath = path.join(__dirname, 'parsed_video_info.json');
    
    console.log('🔍 开始解析剪映草稿文件...');
    console.log(`输入文件: ${draftPath}`);
    
    // 解析草稿文件
    const videoInfo = parseJianyingDraft(draftPath);
    
    // 输出格式化信息
    formatVideoInfo(videoInfo);
    
    // 导出详细信息到JSON文件
    exportToJson(videoInfo, outputPath);
    
    console.log('\n✅ 测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error instanceof Error ? error.message : String(error));
    console.error(error);
  }
}

// 运行测试
testParser();
