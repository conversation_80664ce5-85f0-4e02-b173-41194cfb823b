#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/mixvideo/node_modules/.pnpm/tsup@8.5.0_tsx@4.20.3_typescript@5.8.3/node_modules/tsup/dist/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/tsup@8.5.0_tsx@4.20.3_typescript@5.8.3/node_modules/tsup/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/tsup@8.5.0_tsx@4.20.3_typescript@5.8.3/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/mixvideo/node_modules/.pnpm/tsup@8.5.0_tsx@4.20.3_typescript@5.8.3/node_modules/tsup/dist/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/tsup@8.5.0_tsx@4.20.3_typescript@5.8.3/node_modules/tsup/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/tsup@8.5.0_tsx@4.20.3_typescript@5.8.3/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tsup/dist/cli-default.js" "$@"
else
  exec node  "$basedir/../tsup/dist/cli-default.js" "$@"
fi
