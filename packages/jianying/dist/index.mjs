import * as fs3 from 'fs';
import * as path2 from 'path';
import { execSync } from 'child_process';

var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
function microsecondsToSeconds(microseconds) {
  return Math.round(microseconds / 1e6 * 100) / 100;
}
function calculateAspectRatio(width, height) {
  const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
  const divisor = gcd(width, height);
  return `${width / divisor}:${height / divisor}`;
}
function parseJianyingDraft(filePath) {
  if (!fs3.existsSync(filePath)) {
    throw new Error(`\u6587\u4EF6\u4E0D\u5B58\u5728: ${filePath}`);
  }
  const content = fs3.readFileSync(filePath, "utf-8");
  const draft = JSON.parse(content);
  const videoClips = draft.materials.videos.map((video) => ({
    id: video.id,
    fileName: video.material_name,
    filePath: video.path,
    duration: video.duration,
    durationSeconds: microsecondsToSeconds(video.duration),
    resolution: {
      width: video.width,
      height: video.height,
      aspectRatio: calculateAspectRatio(video.width, video.height)
    },
    hasAudio: video.has_audio,
    cropInfo: {
      ratio: video.crop_ratio,
      scale: video.crop_scale,
      coordinates: video.crop
    },
    isAIGenerated: video.is_ai_generate_content,
    isCopyrighted: video.is_copyright
  }));
  const audioClips = draft.materials.audios.map((audio) => ({
    id: audio.id,
    name: audio.name,
    filePath: audio.path,
    duration: audio.duration,
    durationSeconds: microsecondsToSeconds(audio.duration),
    type: audio.type,
    isAIClone: audio.is_ai_clone_tone,
    isTextOverdub: audio.is_text_edit_overdub
  }));
  const tracks = draft.tracks.map((track) => {
    const segments = track.segments.map((segment) => ({
      materialId: segment.material_id,
      timeRange: {
        start: segment.target_timerange.start,
        duration: segment.target_timerange.duration,
        startSeconds: microsecondsToSeconds(segment.target_timerange.start),
        durationSeconds: microsecondsToSeconds(segment.target_timerange.duration)
      },
      sourceTimeRange: segment.source_timerange ? {
        start: segment.source_timerange.start,
        duration: segment.source_timerange.duration,
        startSeconds: microsecondsToSeconds(segment.source_timerange.start),
        durationSeconds: microsecondsToSeconds(segment.source_timerange.duration)
      } : {
        start: 0,
        duration: 0,
        startSeconds: 0,
        durationSeconds: 0
      },
      transform: segment.clip ? {
        alpha: segment.clip.alpha,
        rotation: segment.clip.rotation,
        scale: segment.clip.scale,
        position: segment.clip.transform,
        flip: segment.clip.flip
      } : {
        alpha: 1,
        rotation: 0,
        scale: { x: 1, y: 1 },
        position: { x: 0, y: 0 },
        flip: { horizontal: false, vertical: false }
      }
    }));
    return {
      id: track.id,
      name: track.name || `\u8F68\u9053 ${track.attribute}`,
      type: track.attribute === 0 ? "video" : "audio",
      segmentCount: segments.length,
      segments
    };
  });
  const uniqueVideoFiles = [...new Set(videoClips.map((v) => v.filePath))];
  const uniqueAudioFiles = [...new Set(audioClips.map((a) => a.filePath))];
  const totalSegments = tracks.reduce((sum, track) => sum + track.segmentCount, 0);
  return {
    projectId: draft.id,
    projectDuration: draft.duration,
    projectDurationSeconds: microsecondsToSeconds(draft.duration),
    fps: draft.fps,
    canvasSize: {
      width: draft.canvas_config.width,
      height: draft.canvas_config.height,
      ratio: draft.canvas_config.ratio
    },
    videoClips,
    audioClips,
    tracks,
    appInfo: {
      appId: draft.last_modified_platform.app_id,
      appSource: draft.last_modified_platform.app_source,
      appVersion: draft.last_modified_platform.app_version,
      platform: draft.last_modified_platform.platform,
      os: draft.last_modified_platform.os,
      deviceId: draft.last_modified_platform.device_id
    },
    statistics: {
      totalVideoClips: videoClips.length,
      totalAudioClips: audioClips.length,
      totalTracks: tracks.length,
      totalSegments,
      uniqueVideoFiles,
      uniqueAudioFiles
    }
  };
}
function formatVideoInfo(info) {
  console.log("\u{1F3AC} \u526A\u6620\u9879\u76EE\u8BE6\u7EC6\u4FE1\u606F");
  console.log("=".repeat(60));
  console.log("\n\u{1F4CB} \u9879\u76EE\u57FA\u672C\u4FE1\u606F:");
  console.log(`  \u9879\u76EEID: ${info.projectId}`);
  console.log(`  \u603B\u65F6\u957F: ${info.projectDurationSeconds}\u79D2 (${info.projectDuration}\u5FAE\u79D2)`);
  console.log(`  \u5E27\u7387: ${info.fps} FPS`);
  console.log(`  \u753B\u5E03\u5C3A\u5BF8: ${info.canvasSize.width}x${info.canvasSize.height} (${info.canvasSize.ratio})`);
  console.log("\n\u{1F4F1} \u5E94\u7528\u4FE1\u606F:");
  console.log(`  \u5E94\u7528: ${info.appInfo.appSource} v${info.appInfo.appVersion}`);
  console.log(`  \u5E73\u53F0: ${info.appInfo.platform} (${info.appInfo.os})`);
  console.log(`  \u8BBE\u5907ID: ${info.appInfo.deviceId}`);
  console.log("\n\u{1F4CA} \u7EDF\u8BA1\u4FE1\u606F:");
  console.log(`  \u89C6\u9891\u7D20\u6750: ${info.statistics.totalVideoClips}\u4E2A`);
  console.log(`  \u97F3\u9891\u7D20\u6750: ${info.statistics.totalAudioClips}\u4E2A`);
  console.log(`  \u8F68\u9053\u6570\u91CF: ${info.statistics.totalTracks}\u4E2A`);
  console.log(`  \u7247\u6BB5\u603B\u6570: ${info.statistics.totalSegments}\u4E2A`);
  console.log(`  \u72EC\u7ACB\u89C6\u9891\u6587\u4EF6: ${info.statistics.uniqueVideoFiles.length}\u4E2A`);
  console.log(`  \u72EC\u7ACB\u97F3\u9891\u6587\u4EF6: ${info.statistics.uniqueAudioFiles.length}\u4E2A`);
  if (info.videoClips.length > 0) {
    console.log("\n\u{1F3A5} \u89C6\u9891\u7D20\u6750\u8BE6\u60C5:");
    info.videoClips.forEach((clip, index) => {
      console.log(`  ${index + 1}. ${clip.fileName}`);
      console.log(`     \u6587\u4EF6\u8DEF\u5F84: ${clip.filePath}`);
      console.log(`     \u65F6\u957F: ${clip.durationSeconds}\u79D2`);
      console.log(`     \u5206\u8FA8\u7387: ${clip.resolution.width}x${clip.resolution.height} (${clip.resolution.aspectRatio})`);
      console.log(`     \u5305\u542B\u97F3\u9891: ${clip.hasAudio ? "\u662F" : "\u5426"}`);
      console.log(`     \u88C1\u526A\u6BD4\u4F8B: ${clip.cropInfo.ratio} (\u7F29\u653E: ${clip.cropInfo.scale})`);
      console.log(`     AI\u751F\u6210: ${clip.isAIGenerated ? "\u662F" : "\u5426"}`);
      console.log(`     \u7248\u6743\u5185\u5BB9: ${clip.isCopyrighted ? "\u662F" : "\u5426"}`);
      console.log("");
    });
  }
  if (info.audioClips.length > 0) {
    console.log("\n\u{1F3B5} \u97F3\u9891\u7D20\u6750\u8BE6\u60C5:");
    info.audioClips.forEach((clip, index) => {
      console.log(`  ${index + 1}. ${clip.name}`);
      console.log(`     \u6587\u4EF6\u8DEF\u5F84: ${clip.filePath}`);
      console.log(`     \u65F6\u957F: ${clip.durationSeconds}\u79D2`);
      console.log(`     \u7C7B\u578B: ${clip.type}`);
      console.log(`     AI\u514B\u9686\u97F3\u8272: ${clip.isAIClone ? "\u662F" : "\u5426"}`);
      console.log(`     \u6587\u672C\u914D\u97F3: ${clip.isTextOverdub ? "\u662F" : "\u5426"}`);
      console.log("");
    });
  }
  if (info.tracks.length > 0) {
    console.log("\n\u{1F6E4}\uFE0F \u8F68\u9053\u8BE6\u60C5:");
    info.tracks.forEach((track, index) => {
      console.log(`  ${index + 1}. ${track.name} (${track.type})`);
      console.log(`     \u8F68\u9053ID: ${track.id}`);
      console.log(`     \u7247\u6BB5\u6570\u91CF: ${track.segmentCount}\u4E2A`);
      if (track.segments.length > 0) {
        console.log("     \u7247\u6BB5\u8BE6\u60C5:");
        track.segments.forEach((segment, segIndex) => {
          console.log(`       ${segIndex + 1}. \u7D20\u6750ID: ${segment.materialId}`);
          console.log(`          \u65F6\u95F4\u8F74: ${segment.timeRange.startSeconds}s - ${segment.timeRange.startSeconds + segment.timeRange.durationSeconds}s (\u65F6\u957F: ${segment.timeRange.durationSeconds}s)`);
          console.log(`          \u6E90\u65F6\u95F4: ${segment.sourceTimeRange.startSeconds}s - ${segment.sourceTimeRange.startSeconds + segment.sourceTimeRange.durationSeconds}s`);
          console.log(`          \u53D8\u6362: \u900F\u660E\u5EA6=${segment.transform.alpha}, \u65CB\u8F6C=${segment.transform.rotation}\xB0`);
          console.log(`          \u7F29\u653E: x=${segment.transform.scale.x}, y=${segment.transform.scale.y}`);
          console.log(`          \u4F4D\u7F6E: x=${segment.transform.position.x}, y=${segment.transform.position.y}`);
          console.log(`          \u7FFB\u8F6C: \u6C34\u5E73=${segment.transform.flip.horizontal}, \u5782\u76F4=${segment.transform.flip.vertical}`);
        });
      }
      console.log("");
    });
  }
  console.log("\n\u{1F4C1} \u4F7F\u7528\u7684\u6587\u4EF6\u5217\u8868:");
  console.log("  \u89C6\u9891\u6587\u4EF6:");
  info.statistics.uniqueVideoFiles.forEach((file, index) => {
    console.log(`    ${index + 1}. ${file}`);
  });
  if (info.statistics.uniqueAudioFiles.length > 0) {
    console.log("  \u97F3\u9891\u6587\u4EF6:");
    info.statistics.uniqueAudioFiles.forEach((file, index) => {
      console.log(`    ${index + 1}. ${file}`);
    });
  }
}
function exportToJson(info, outputPath) {
  const jsonContent = JSON.stringify(info, null, 2);
  fs3.writeFileSync(outputPath, jsonContent, "utf-8");
  console.log(`
\u{1F4BE} \u8BE6\u7EC6\u4FE1\u606F\u5DF2\u5BFC\u51FA\u5230: ${outputPath}`);
}
function main() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.log("\u4F7F\u7528\u65B9\u6CD5:");
    console.log("  ts-node parse-draft.ts <draft_content.json\u8DEF\u5F84> [\u8F93\u51FAJSON\u8DEF\u5F84]");
    console.log("");
    console.log("\u793A\u4F8B:");
    console.log("  ts-node parse-draft.ts ./draft_content.json");
    console.log("  ts-node parse-draft.ts ./draft_content.json ./output.json");
    process.exit(1);
  }
  const inputPath = args[0];
  const outputPath = args[1];
  try {
    console.log(`\u{1F50D} \u6B63\u5728\u89E3\u6790\u526A\u6620\u8349\u7A3F\u6587\u4EF6: ${inputPath}`);
    const videoInfo = parseJianyingDraft(inputPath);
    formatVideoInfo(videoInfo);
    if (outputPath) {
      exportToJson(videoInfo, outputPath);
    }
    console.log("\n\u2705 \u89E3\u6790\u5B8C\u6210!");
  } catch (error) {
    console.error("\u274C \u89E3\u6790\u5931\u8D25:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}
if (__require.main === module) {
  main();
}
function secondsToMicroseconds(seconds) {
  return seconds * 1e6;
}
function calculateComplexity(info) {
  let score = 0;
  const factors = [];
  if (info.tracks.length > 3) {
    score += 20;
    factors.push(`\u591A\u8F68\u9053\u7F16\u8F91 (${info.tracks.length}\u4E2A\u8F68\u9053)`);
  }
  const totalSegments = info.statistics.totalSegments;
  if (totalSegments > 20) {
    score += 30;
    factors.push(`\u5927\u91CF\u7247\u6BB5 (${totalSegments}\u4E2A\u7247\u6BB5)`);
  } else if (totalSegments > 10) {
    score += 15;
    factors.push(`\u4E2D\u7B49\u7247\u6BB5\u6570\u91CF (${totalSegments}\u4E2A\u7247\u6BB5)`);
  }
  if (info.statistics.totalVideoClips > 10) {
    score += 20;
    factors.push(`\u591A\u4E2A\u89C6\u9891\u7D20\u6750 (${info.statistics.totalVideoClips}\u4E2A)`);
  }
  let transformationCount = 0;
  info.tracks.forEach((track) => {
    track.segments.forEach((segment) => {
      if (segment.transform.rotation !== 0) transformationCount++;
      if (segment.transform.scale.x !== 1 || segment.transform.scale.y !== 1) transformationCount++;
      if (segment.transform.position.x !== 0 || segment.transform.position.y !== 0) transformationCount++;
      if (segment.transform.flip.horizontal || segment.transform.flip.vertical) transformationCount++;
    });
  });
  if (transformationCount > 10) {
    score += 25;
    factors.push(`\u590D\u6742\u53D8\u6362 (${transformationCount}\u4E2A\u53D8\u6362)`);
  } else if (transformationCount > 5) {
    score += 10;
    factors.push(`\u4E2D\u7B49\u53D8\u6362 (${transformationCount}\u4E2A\u53D8\u6362)`);
  }
  if (info.projectDurationSeconds > 300) {
    score += 15;
    factors.push(`\u957F\u89C6\u9891 (${info.projectDurationSeconds}\u79D2)`);
  }
  let level;
  if (score >= 70) level = "Very Complex";
  else if (score >= 50) level = "Complex";
  else if (score >= 30) level = "Medium";
  else level = "Simple";
  return { score, level, factors };
}
function analyzeTimeline(info) {
  const allSegments = info.tracks.flatMap((track) => track.segments);
  const durations = allSegments.map((seg) => seg.timeRange.durationSeconds);
  return {
    totalDuration: info.projectDurationSeconds,
    segmentCount: allSegments.length,
    averageSegmentLength: durations.reduce((a, b) => a + b, 0) / durations.length,
    shortestSegment: Math.min(...durations),
    longestSegment: Math.max(...durations)
  };
}
function analyzeMaterials(info) {
  const videoFileUsage = /* @__PURE__ */ new Map();
  info.tracks.forEach((track) => {
    track.segments.forEach((segment) => {
      const videoClip = info.videoClips.find((clip) => clip.id === segment.materialId);
      if (videoClip) {
        const filePath = videoClip.filePath;
        if (!videoFileUsage.has(filePath)) {
          videoFileUsage.set(filePath, { usageCount: 0, totalDuration: 0, segments: [] });
        }
        const usage = videoFileUsage.get(filePath);
        usage.usageCount++;
        usage.totalDuration += segment.timeRange.durationSeconds;
        usage.segments.push({
          trackId: track.id,
          startTime: segment.timeRange.startSeconds,
          duration: segment.timeRange.durationSeconds
        });
      }
    });
  });
  const audioFileUsage = info.audioClips.map((clip) => ({
    filePath: clip.filePath,
    usageCount: 1,
    // 简化处理，假设每个音频素材只使用一次
    totalDuration: clip.durationSeconds
  }));
  return {
    videoFileUsage: Array.from(videoFileUsage.entries()).map(([filePath, usage]) => ({
      filePath,
      ...usage
    })),
    audioFileUsage
  };
}
function analyzeEditing(info) {
  let hasTransformations = false;
  let hasScaling = false;
  let hasRotation = false;
  let hasPositionChanges = false;
  let hasFlipping = false;
  let transformationCount = 0;
  info.tracks.forEach((track) => {
    track.segments.forEach((segment) => {
      if (segment.transform.rotation !== 0) {
        hasRotation = true;
        hasTransformations = true;
        transformationCount++;
      }
      if (segment.transform.scale.x !== 1 || segment.transform.scale.y !== 1) {
        hasScaling = true;
        hasTransformations = true;
        transformationCount++;
      }
      if (segment.transform.position.x !== 0 || segment.transform.position.y !== 0) {
        hasPositionChanges = true;
        hasTransformations = true;
        transformationCount++;
      }
      if (segment.transform.flip.horizontal || segment.transform.flip.vertical) {
        hasFlipping = true;
        hasTransformations = true;
        transformationCount++;
      }
    });
  });
  return {
    hasTransformations,
    hasScaling,
    hasRotation,
    hasPositionChanges,
    hasFlipping,
    transformationCount
  };
}
function generateRecommendations(analysis) {
  const recommendations = [];
  if (analysis.complexity.level === "Very Complex") {
    recommendations.push({
      type: "performance",
      description: "\u9879\u76EE\u590D\u6742\u5EA6\u5F88\u9AD8\uFF0C\u5EFA\u8BAE\u5206\u6BB5\u5904\u7406\u6216\u7B80\u5316\u7F16\u8F91"
    });
  }
  if (analysis.timeline.segmentCount > 50) {
    recommendations.push({
      type: "optimization",
      description: "\u7247\u6BB5\u6570\u91CF\u8F83\u591A\uFF0C\u8003\u8651\u5408\u5E76\u76F8\u4F3C\u7247\u6BB5\u4EE5\u63D0\u9AD8\u6027\u80FD"
    });
  }
  if (analysis.timeline.averageSegmentLength < 1) {
    recommendations.push({
      type: "content",
      description: "\u5E73\u5747\u7247\u6BB5\u65F6\u957F\u8F83\u77ED\uFF0C\u53EF\u80FD\u5F71\u54CD\u89C2\u770B\u4F53\u9A8C"
    });
  }
  if (analysis.editing.transformationCount > 20) {
    recommendations.push({
      type: "performance",
      description: "\u53D8\u6362\u64CD\u4F5C\u8F83\u591A\uFF0C\u6CE8\u610F\u68C0\u67E5\u6E32\u67D3\u6027\u80FD"
    });
  }
  const videoFileCount = analysis.materials.videoFileUsage.length;
  if (videoFileCount === 1) {
    recommendations.push({
      type: "content",
      description: "\u53EA\u4F7F\u7528\u4E86\u4E00\u4E2A\u89C6\u9891\u6587\u4EF6\uFF0C\u8003\u8651\u6DFB\u52A0\u66F4\u591A\u7D20\u6750\u4E30\u5BCC\u5185\u5BB9"
    });
  } else if (videoFileCount > 10) {
    recommendations.push({
      type: "management",
      description: "\u4F7F\u7528\u4E86\u5927\u91CF\u89C6\u9891\u6587\u4EF6\uFF0C\u6CE8\u610F\u7D20\u6750\u7BA1\u7406\u548C\u5B58\u50A8\u7A7A\u95F4"
    });
  }
  if (recommendations.length === 0) {
    recommendations.push({
      type: "general",
      description: "\u9879\u76EE\u7ED3\u6784\u826F\u597D\uFF0C\u65E0\u7279\u6B8A\u5EFA\u8BAE"
    });
  }
  return recommendations;
}
function performEnhancedAnalysis(filePath) {
  const basicInfo = parseJianyingDraft(filePath);
  const complexity = calculateComplexity(basicInfo);
  const timeline = analyzeTimeline(basicInfo);
  const materials = analyzeMaterials(basicInfo);
  const editing = analyzeEditing(basicInfo);
  const analysisData = { complexity, timeline, materials, editing };
  const recommendations = generateRecommendations(analysisData);
  return {
    basicInfo,
    analysis: {
      complexity,
      timeline,
      materials,
      editing
    },
    recommendations
  };
}
function formatEnhancedAnalysis(analysis) {
  console.log("\u{1F680} \u526A\u6620\u9879\u76EE\u589E\u5F3A\u5206\u6790\u62A5\u544A");
  console.log("=".repeat(80));
  console.log("\n\u{1F4CA} \u9879\u76EE\u590D\u6742\u5EA6\u5206\u6790:");
  console.log(`  \u590D\u6742\u5EA6\u7B49\u7EA7: ${analysis.analysis.complexity.level}`);
  console.log(`  \u590D\u6742\u5EA6\u8BC4\u5206: ${analysis.analysis.complexity.score}/100`);
  console.log("  \u5F71\u54CD\u56E0\u7D20:");
  analysis.analysis.complexity.factors.forEach((factor) => {
    console.log(`    \u2022 ${factor}`);
  });
  console.log("\n\u23F1\uFE0F \u65F6\u95F4\u8F74\u5206\u6790:");
  console.log(`  \u603B\u65F6\u957F: ${analysis.analysis.timeline.totalDuration.toFixed(2)}\u79D2`);
  console.log(`  \u7247\u6BB5\u603B\u6570: ${analysis.analysis.timeline.segmentCount}\u4E2A`);
  console.log(`  \u5E73\u5747\u7247\u6BB5\u65F6\u957F: ${analysis.analysis.timeline.averageSegmentLength.toFixed(2)}\u79D2`);
  console.log(`  \u6700\u77ED\u7247\u6BB5: ${analysis.analysis.timeline.shortestSegment.toFixed(2)}\u79D2`);
  console.log(`  \u6700\u957F\u7247\u6BB5: ${analysis.analysis.timeline.longestSegment.toFixed(2)}\u79D2`);
  console.log("\n\u{1F4C1} \u7D20\u6750\u4F7F\u7528\u5206\u6790:");
  console.log("  \u89C6\u9891\u6587\u4EF6\u4F7F\u7528\u60C5\u51B5:");
  analysis.analysis.materials.videoFileUsage.forEach((usage, index) => {
    console.log(`    ${index + 1}. ${path2.basename(usage.filePath)}`);
    console.log(`       \u4F7F\u7528\u6B21\u6570: ${usage.usageCount}\u6B21`);
    console.log(`       \u603B\u4F7F\u7528\u65F6\u957F: ${usage.totalDuration.toFixed(2)}\u79D2`);
    console.log(`       \u7247\u6BB5\u5206\u5E03: ${usage.segments.length}\u4E2A\u7247\u6BB5`);
  });
  console.log("\n\u{1F3A8} \u7F16\u8F91\u7279\u6027\u5206\u6790:");
  console.log(`  \u4F7F\u7528\u53D8\u6362\u6548\u679C: ${analysis.analysis.editing.hasTransformations ? "\u662F" : "\u5426"}`);
  console.log(`  \u4F7F\u7528\u7F29\u653E: ${analysis.analysis.editing.hasScaling ? "\u662F" : "\u5426"}`);
  console.log(`  \u4F7F\u7528\u65CB\u8F6C: ${analysis.analysis.editing.hasRotation ? "\u662F" : "\u5426"}`);
  console.log(`  \u4F7F\u7528\u4F4D\u7F6E\u8C03\u6574: ${analysis.analysis.editing.hasPositionChanges ? "\u662F" : "\u5426"}`);
  console.log(`  \u4F7F\u7528\u7FFB\u8F6C: ${analysis.analysis.editing.hasFlipping ? "\u662F" : "\u5426"}`);
  console.log(`  \u53D8\u6362\u64CD\u4F5C\u603B\u6570: ${analysis.analysis.editing.transformationCount}\u4E2A`);
  console.log("\n\u{1F4A1} \u4F18\u5316\u5EFA\u8BAE:");
  analysis.recommendations.forEach((rec) => {
    console.log(`  \u2022 [${rec.type}] ${rec.description}`);
  });
}
function main2() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.log("\u4F7F\u7528\u65B9\u6CD5:");
    console.log("  ts-node enhanced-parser.ts <draft_content.json\u8DEF\u5F84> [\u8F93\u51FAJSON\u8DEF\u5F84]");
    console.log("");
    console.log("\u793A\u4F8B:");
    console.log("  ts-node enhanced-parser.ts ./draft_content.json");
    console.log("  ts-node enhanced-parser.ts ./draft_content.json ./enhanced_analysis.json");
    process.exit(1);
  }
  const inputPath = args[0];
  const outputPath = args[1];
  try {
    console.log(`\u{1F50D} \u6B63\u5728\u6267\u884C\u589E\u5F3A\u5206\u6790: ${inputPath}`);
    const analysis = performEnhancedAnalysis(inputPath);
    formatEnhancedAnalysis(analysis);
    if (outputPath) {
      const jsonContent = JSON.stringify(analysis, null, 2);
      fs3.writeFileSync(outputPath, jsonContent, "utf-8");
      console.log(`
\u{1F4BE} \u589E\u5F3A\u5206\u6790\u7ED3\u679C\u5DF2\u5BFC\u51FA\u5230: ${outputPath}`);
    }
    console.log("\n\u2705 \u589E\u5F3A\u5206\u6790\u5B8C\u6210!");
  } catch (error) {
    console.error("\u274C \u5206\u6790\u5931\u8D25:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}
if (__require.main === module) {
  main2();
}
function analyzeJianyingProject(filePath) {
  return performEnhancedAnalysis(filePath);
}
function analyzeJianyingProjectFromData(videoInfo) {
  const complexity = calculateComplexity(videoInfo);
  const timeline = analyzeTimeline(videoInfo);
  const materials = analyzeMaterials(videoInfo);
  const editing = analyzeEditing(videoInfo);
  const analysisData = { complexity, timeline, materials, editing };
  const recommendations = generateRecommendationsFromAnalysis(analysisData);
  return {
    basicInfo: videoInfo,
    analysis: analysisData,
    recommendations
  };
}
function generateRecommendationsFromAnalysis(analysis) {
  const recommendations = [];
  if (analysis.complexity.level === "Very Complex") {
    recommendations.push({
      type: "performance",
      description: "\u9879\u76EE\u590D\u6742\u5EA6\u5F88\u9AD8\uFF0C\u5EFA\u8BAE\u4F18\u5316\u6027\u80FD"
    });
  }
  return recommendations;
}
function generateUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === "x" ? r : r & 3 | 8;
    return v.toString(16);
  });
}
var VIDEO_EXTENSIONS = [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v"];
var AUDIO_EXTENSIONS = [".mp3", ".wav", ".aac", ".m4a", ".flac", ".ogg"];
function scanDirectory(dirPath) {
  const files = [];
  function scanRecursive(currentPath) {
    try {
      const items = fs3.readdirSync(currentPath);
      for (const item of items) {
        const fullPath = path2.join(currentPath, item);
        const stat = fs3.statSync(fullPath);
        if (stat.isDirectory()) {
          scanRecursive(fullPath);
        } else if (stat.isFile()) {
          const ext = path2.extname(item).toLowerCase();
          const isVideo = VIDEO_EXTENSIONS.includes(ext);
          const isAudio = AUDIO_EXTENSIONS.includes(ext);
          if (isVideo || isAudio) {
            files.push({
              filePath: fullPath,
              fileName: path2.basename(item, ext),
              extension: ext,
              size: stat.size,
              isVideo,
              isAudio
            });
          }
        }
      }
    } catch (error) {
      console.warn(`\u8B66\u544A: \u65E0\u6CD5\u8BBF\u95EE\u76EE\u5F55 ${currentPath}:`, error instanceof Error ? error.message : String(error));
    }
  }
  scanRecursive(dirPath);
  return files;
}
function checkFFProbe() {
  try {
    execSync("ffprobe -version", { stdio: "ignore" });
    return true;
  } catch {
    return false;
  }
}
function getVideoInfoWithFFProbe(filePath) {
  try {
    const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${filePath}"`;
    const output = execSync(command, { encoding: "utf-8" });
    const data = JSON.parse(output);
    let duration = 0;
    let width = 1920;
    let height = 1080;
    let hasAudio = false;
    if (data.format && data.format.duration) {
      duration = Math.floor(parseFloat(data.format.duration) * 1e6);
    }
    if (data.streams) {
      for (const stream of data.streams) {
        if (stream.codec_type === "video") {
          width = stream.width || width;
          height = stream.height || height;
        } else if (stream.codec_type === "audio") {
          hasAudio = true;
        }
      }
    }
    return { duration, width, height, hasAudio };
  } catch (error) {
    console.warn(`\u8B66\u544A: \u65E0\u6CD5\u83B7\u53D6\u6587\u4EF6\u4FE1\u606F ${filePath}:`, error instanceof Error ? error.message : String(error));
    return null;
  }
}
function getVideoInfo(file, useFFProbe = true) {
  if (useFFProbe && checkFFProbe()) {
    const realInfo = getVideoInfoWithFFProbe(file.filePath);
    if (realInfo) {
      return realInfo;
    }
  }
  const defaultWidth = 1920;
  const defaultHeight = 1080;
  const estimatedDuration = Math.max(1e6, Math.min(6e7, file.size / 1e3));
  return {
    duration: Math.floor(estimatedDuration),
    width: defaultWidth,
    height: defaultHeight,
    hasAudio: file.isVideo
    // 假设视频文件都有音频
  };
}
function generateVideoClips(videoFiles, useFFProbe = true) {
  return videoFiles.map((file) => {
    const info = getVideoInfo(file, useFFProbe);
    return {
      aigc_type: "none",
      audio_fade: null,
      cartoon_path: "",
      category_id: "",
      category_name: "",
      check_flag: 63487,
      crop: {
        lower_left_x: 0,
        lower_left_y: 1,
        lower_right_x: 1,
        lower_right_y: 1,
        upper_left_x: 0,
        upper_left_y: 0,
        upper_right_x: 1,
        upper_right_y: 0
      },
      crop_ratio: "free",
      crop_scale: 1,
      duration: info.duration,
      extra_type_option: 1,
      formula_id: "",
      freeze: null,
      has_audio: info.hasAudio,
      height: info.height,
      id: generateUUID().toUpperCase(),
      intensifies_audio_path: "",
      intensifies_path: "",
      is_ai_generate_content: false,
      is_copyright: false,
      is_text_edit_overdub: false,
      is_unified_beauty_mode: false,
      local_id: "",
      local_material_id: "",
      material_id: "",
      material_name: file.fileName + file.extension,
      material_url: "",
      matting: {
        flag: 0,
        has_use_quick_brush: false,
        has_use_quick_eraser: false,
        interactiveTime: [],
        path: "",
        strokes: []
      },
      media_path: "",
      object_locked: null,
      origin_material_id: "",
      path: file.filePath,
      picture_from: "none",
      picture_set_category_id: "",
      picture_set_category_name: "",
      request_id: "",
      reverse_intensifies_path: "",
      reverse_path: "",
      smart_motion: null,
      source: 0,
      source_platform: 0,
      stable: {
        matrix_path: "",
        stable_level: 0,
        time_range: {
          duration: info.duration,
          start: 0
        }
      },
      team_id: "",
      type: "video",
      video_algorithm: {
        algorithms: [],
        deflicker: null,
        motion_blur_config: null,
        noise_reduction: null,
        path: "",
        time_range: null
      },
      width: info.width
    };
  });
}
function generateAudioClips(audioFiles, useFFProbe = true) {
  return audioFiles.map((file) => {
    const info = getVideoInfo(file, useFFProbe);
    return {
      app_id: 0,
      category_id: "",
      category_name: "",
      check_flag: 1,
      copyright_limit_type: "none",
      duration: info.duration,
      effect_id: "",
      formula_id: "",
      id: generateUUID().toUpperCase(),
      intensifies_path: "",
      is_ai_clone_tone: false,
      is_text_edit_overdub: false,
      is_ugc: false,
      local_material_id: "",
      music_id: "",
      name: file.fileName,
      path: file.filePath,
      query: "",
      request_id: "",
      resource_id: "",
      search_id: "",
      source_from: "",
      source_platform: 0,
      team_id: "",
      text_id: "",
      tone_category_id: "",
      tone_category_name: "",
      tone_effect_id: "",
      tone_effect_name: "",
      tone_platform: "",
      tone_second_category_id: "",
      tone_second_category_name: "",
      tone_speaker: "",
      tone_type: "",
      type: file.isVideo ? "video_original_sound" : "audio",
      video_id: "",
      wave_points: []
    };
  });
}
function generateTracks(videoClips, audioClips) {
  const tracks = [];
  if (videoClips.length > 0) {
    let currentTime = 0;
    const segments = [];
    for (const clip of videoClips) {
      segments.push({
        clip: {
          alpha: 1,
          flip: {
            horizontal: false,
            vertical: false
          },
          rotation: 0,
          scale: {
            x: 1,
            y: 1
          },
          transform: {
            x: 0,
            y: 0
          }
        },
        material_id: clip.id,
        target_timerange: {
          duration: clip.duration,
          start: currentTime
        },
        source_timerange: {
          duration: clip.duration,
          start: 0
        }
      });
      currentTime += clip.duration;
    }
    tracks.push({
      attribute: 0,
      flag: 0,
      id: generateUUID().toUpperCase(),
      segments,
      type: "video"
    });
  }
  if (audioClips.length > 0) {
    let currentTime = 0;
    const segments = [];
    for (const clip of audioClips) {
      segments.push({
        material_id: clip.id,
        target_timerange: {
          duration: clip.duration,
          start: currentTime
        },
        source_timerange: {
          duration: clip.duration,
          start: 0
        }
      });
      currentTime += clip.duration;
    }
    tracks.push({
      attribute: 0,
      flag: 0,
      id: generateUUID().toUpperCase(),
      segments,
      type: "audio"
    });
  }
  return tracks;
}
function generateDraft(files, options) {
  const videoFiles = files.filter((f) => f.isVideo);
  const audioFiles = files.filter((f) => f.isAudio);
  const useFFProbe = options.useFFProbe !== false;
  const videoClips = generateVideoClips(videoFiles, useFFProbe);
  const audioClips = generateAudioClips(audioFiles, useFFProbe);
  const tracks = generateTracks(videoClips, audioClips);
  const totalDuration = Math.max(
    ...tracks.map(
      (track) => track.segments.reduce((sum, seg) => Math.max(sum, seg.target_timerange.start + seg.target_timerange.duration), 0)
    ),
    0
  );
  const canvasWidth = options.canvasWidth || 1080;
  const canvasHeight = options.canvasHeight || 1920;
  const ratio = `${canvasWidth}:${canvasHeight}`;
  const deviceId = "auto-generated-" + Date.now();
  const currentTime = Date.now() * 1e3;
  return {
    canvas_config: {
      height: canvasHeight,
      ratio,
      width: canvasWidth
    },
    color_space: 0,
    config: {
      adjust_max_index: 2,
      attachment_info: [],
      combination_max_index: 1,
      export_range: null,
      extract_audio_last_index: 1,
      lyrics_recognition_id: "",
      lyrics_sync: true,
      lyrics_taskinfo: [],
      maintrack_adsorb: true,
      material_save_mode: 0,
      multi_language_current: "none",
      multi_language_list: [],
      multi_language_main: "none",
      multi_language_mode: "none",
      original_sound_last_index: 2,
      record_audio_last_index: 1,
      sticker_max_index: 1,
      subtitle_keywords_config: null,
      subtitle_recognition_id: "",
      subtitle_sync: true,
      subtitle_taskinfo: [],
      system_font_list: [],
      text_recognition_id: "",
      text_sync: true,
      text_taskinfo: [],
      video_recognition_id: "",
      video_sync: true,
      video_taskinfo: []
    },
    create_time: currentTime,
    draft_fold_path: "",
    draft_id: generateUUID().toUpperCase(),
    draft_name: options.projectName || "Auto Generated Project",
    draft_removable_storage_device: "",
    duration: totalDuration,
    fps: options.fps || 30,
    id: generateUUID().toUpperCase(),
    last_modified_platform: {
      app_id: 3704,
      app_source: "auto-generator",
      app_version: "1.0.0",
      device_id: deviceId,
      hard_disk_id: "",
      mac_address: "",
      os_version: "auto",
      platform: "auto",
      screen_height: 1080,
      screen_width: 1920
    },
    materials: {
      audio_effects: [],
      audio_fades: [],
      audio_track_indexes: [],
      audios: audioClips,
      beats: [],
      canvases: [],
      chromas: [],
      color_curves: [],
      color_wheels: [],
      effects: [],
      flowers: [],
      handwrites: [],
      hsl: [],
      images: [],
      keyframes: [],
      masks: [],
      material_animations: [],
      placeholders: [],
      plugin_effects: [],
      shapes: [],
      sounds: [],
      stickers: [],
      texts: [],
      videos: videoClips
    },
    mutable_config: null,
    name: options.projectName || "",
    new_version: "110.0.0",
    platform: {
      app_id: 3704,
      app_source: "auto-generator",
      app_version: "1.0.0",
      device_id: deviceId,
      hard_disk_id: "",
      mac_address: "",
      os_version: "auto",
      platform: "auto",
      screen_height: 1080,
      screen_width: 1920
    },
    relationships: [],
    revert_generate_segment_config: null,
    source: "auto-generator",
    tracks,
    update_time: currentTime,
    version: "13.2.0"
  };
}
function main3() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.log("\u4F7F\u7528\u65B9\u6CD5:");
    console.log("  ts-node generate-draft.ts <\u626B\u63CF\u76EE\u5F55> [\u8F93\u51FA\u6587\u4EF6\u8DEF\u5F84] [\u9009\u9879]");
    console.log("");
    console.log("\u793A\u4F8B:");
    console.log("  ts-node generate-draft.ts ./videos");
    console.log("  ts-node generate-draft.ts ./videos ./generated_draft.json");
    console.log("  ts-node generate-draft.ts ./videos ./draft.json --width=1920 --height=1080 --fps=60");
    console.log("");
    console.log("\u9009\u9879:");
    console.log("  --width=<\u6570\u5B57>     \u753B\u5E03\u5BBD\u5EA6 (\u9ED8\u8BA4: 1080)");
    console.log("  --height=<\u6570\u5B57>    \u753B\u5E03\u9AD8\u5EA6 (\u9ED8\u8BA4: 1920)");
    console.log("  --fps=<\u6570\u5B57>       \u5E27\u7387 (\u9ED8\u8BA4: 30)");
    console.log("  --name=<\u5B57\u7B26\u4E32>    \u9879\u76EE\u540D\u79F0");
    process.exit(1);
  }
  const scanDir = args[0];
  const outputPath = args[1] || "./generated_draft_content.json";
  const options = {};
  for (let i = 2; i < args.length; i++) {
    const arg = args[i];
    if (arg.startsWith("--width=")) {
      options.canvasWidth = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--height=")) {
      options.canvasHeight = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--fps=")) {
      options.fps = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--name=")) {
      options.projectName = arg.split("=")[1];
    }
  }
  try {
    console.log(`\u{1F50D} \u6B63\u5728\u626B\u63CF\u76EE\u5F55: ${scanDir}`);
    if (!fs3.existsSync(scanDir)) {
      throw new Error(`\u76EE\u5F55\u4E0D\u5B58\u5728: ${scanDir}`);
    }
    const files = scanDirectory(scanDir);
    if (files.length === 0) {
      console.log("\u274C \u672A\u627E\u5230\u4EFB\u4F55\u5A92\u4F53\u6587\u4EF6");
      process.exit(1);
    }
    console.log(`\u{1F4C1} \u627E\u5230 ${files.length} \u4E2A\u5A92\u4F53\u6587\u4EF6:`);
    files.forEach((file, index) => {
      const type = file.isVideo ? "\u{1F3A5}" : "\u{1F3B5}";
      console.log(`  ${index + 1}. ${type} ${file.fileName}${file.extension} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
    });
    console.log("\n\u{1F680} \u6B63\u5728\u751F\u6210\u8349\u7A3F\u6587\u4EF6...");
    const draft = generateDraft(files, options);
    const jsonContent = JSON.stringify(draft, null, 2);
    fs3.writeFileSync(outputPath, jsonContent, "utf-8");
    console.log(`
\u2705 \u8349\u7A3F\u6587\u4EF6\u751F\u6210\u6210\u529F!`);
    console.log(`\u{1F4C4} \u8F93\u51FA\u6587\u4EF6: ${outputPath}`);
    console.log(`\u{1F4CA} \u9879\u76EE\u4FE1\u606F:`);
    console.log(`   \u9879\u76EEID: ${draft.id}`);
    console.log(`   \u603B\u65F6\u957F: ${(draft.duration / 1e6).toFixed(2)}\u79D2`);
    console.log(`   \u753B\u5E03\u5C3A\u5BF8: ${draft.canvas_config.width}x${draft.canvas_config.height}`);
    console.log(`   \u5E27\u7387: ${draft.fps} FPS`);
    console.log(`   \u89C6\u9891\u7D20\u6750: ${draft.materials.videos.length}\u4E2A`);
    console.log(`   \u97F3\u9891\u7D20\u6750: ${draft.materials.audios.length}\u4E2A`);
    console.log(`   \u8F68\u9053\u6570\u91CF: ${draft.tracks.length}\u4E2A`);
    console.log("\n\u{1F4A1} \u63D0\u793A: \u751F\u6210\u7684\u6587\u4EF6\u4F7F\u7528\u4E86\u9ED8\u8BA4\u7684\u89C6\u9891\u4FE1\u606F\u3002");
    console.log("   \u5982\u9700\u51C6\u786E\u7684\u89C6\u9891\u4FE1\u606F\uFF0C\u5EFA\u8BAE\u96C6\u6210 ffprobe \u5DE5\u5177\u3002");
  } catch (error) {
    console.error("\u274C \u751F\u6210\u5931\u8D25:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}
if (__require.main === module) {
  main3();
}

// src/index.ts
var JianyingUtils = {
  // 时间转换
  microsecondsToSeconds: (microseconds) => microseconds / 1e6,
  secondsToMicroseconds: (seconds) => seconds * 1e6,
  // 宽高比计算
  calculateAspectRatio: (width, height) => {
    const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
    const divisor = gcd(width, height);
    return `${width / divisor}:${height / divisor}`;
  },
  // 支持的文件格式
  VIDEO_EXTENSIONS: [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v"],
  AUDIO_EXTENSIONS: [".mp3", ".wav", ".aac", ".m4a", ".flac", ".ogg"],
  // 检查文件类型
  isVideoFile: (filename) => {
    const ext = filename.toLowerCase().substring(filename.lastIndexOf("."));
    return JianyingUtils.VIDEO_EXTENSIONS.includes(ext);
  },
  isAudioFile: (filename) => {
    const ext = filename.toLowerCase().substring(filename.lastIndexOf("."));
    return JianyingUtils.AUDIO_EXTENSIONS.includes(ext);
  },
  isMediaFile: (filename) => {
    return JianyingUtils.isVideoFile(filename) || JianyingUtils.isAudioFile(filename);
  }
};
var JIANYING_CONSTANTS = {
  // 默认配置
  DEFAULT_FPS: 30,
  DEFAULT_CANVAS_WIDTH: 1080,
  DEFAULT_CANVAS_HEIGHT: 1920,
  DEFAULT_ASPECT_RATIO: "9:16",
  // 时间单位
  MICROSECONDS_PER_SECOND: 1e6,
  // 剪映版本信息
  SUPPORTED_VERSIONS: ["13.2.0", "110.0.0"],
  DEFAULT_VERSION: "13.2.0",
  // 平台信息
  PLATFORM_INFO: {
    app_id: 3704,
    app_source: "auto-generator",
    platform: "auto"
  }
};

export { JIANYING_CONSTANTS, JianyingUtils, analyzeJianyingProject, analyzeJianyingProjectFromData, calculateAspectRatio, checkFFProbe, exportToJson, formatEnhancedAnalysis, formatVideoInfo, generateAudioClips, generateDraft, generateRecommendationsFromAnalysis, generateTracks, generateVideoClips, getVideoInfoWithFFProbe, microsecondsToSeconds, parseJianyingDraft, performEnhancedAnalysis, scanDirectory, secondsToMicroseconds };
//# sourceMappingURL=index.mjs.map
//# sourceMappingURL=index.mjs.map