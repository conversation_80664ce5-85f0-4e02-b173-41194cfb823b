{"version": 3, "sources": ["../src/parse-draft.ts", "../src/enhanced-parser.ts", "../src/generate-draft.ts", "../src/index.ts"], "names": ["fs", "path", "main", "fs2"], "mappings": ";;;;;;;;;;AAiPA,SAAS,sBAAsB,YAAA,EAA8B;AAC3D,EAAA,OAAO,IAAA,CAAK,KAAA,CAAO,YAAA,GAAe,GAAA,GAAW,GAAG,CAAA,GAAI,GAAA;AACtD;AAKA,SAAS,oBAAA,CAAqB,OAAe,MAAA,EAAwB;AACnE,EAAA,MAAM,GAAA,GAAM,CAAC,CAAA,EAAW,CAAA,KAAsB,CAAA,KAAM,IAAI,CAAA,GAAI,GAAA,CAAI,CAAA,EAAG,CAAA,GAAI,CAAC,CAAA;AACxE,EAAA,MAAM,OAAA,GAAU,GAAA,CAAI,KAAA,EAAO,MAAM,CAAA;AACjC,EAAA,OAAO,CAAA,EAAG,KAAA,GAAQ,OAAO,CAAA,CAAA,EAAI,SAAS,OAAO,CAAA,CAAA;AAC/C;AAKA,SAAS,mBAAmB,QAAA,EAA6B;AACvD,EAAA,IAAI,CAAIA,GAAA,CAAA,UAAA,CAAW,QAAQ,CAAA,EAAG;AAC5B,IAAA,MAAM,IAAI,KAAA,CAAM,CAAA,gCAAA,EAAU,QAAQ,CAAA,CAAE,CAAA;AAAA;AAGtC,EAAA,MAAM,OAAA,GAAaA,GAAA,CAAA,YAAA,CAAa,QAAA,EAAU,OAAO,CAAA;AACjD,EAAA,MAAM,KAAA,GAAuB,IAAA,CAAK,KAAA,CAAM,OAAO,CAAA;AAG/C,EAAA,MAAM,UAAA,GAAa,KAAA,CAAM,SAAA,CAAU,MAAA,CAAO,IAAI,CAAA,KAAA,MAAU;AAAA,IACtD,IAAI,KAAA,CAAM,EAAA;AAAA,IACV,UAAU,KAAA,CAAM,aAAA;AAAA,IAChB,UAAU,KAAA,CAAM,IAAA;AAAA,IAChB,UAAU,KAAA,CAAM,QAAA;AAAA,IAChB,eAAA,EAAiB,qBAAA,CAAsB,KAAA,CAAM,QAAQ,CAAA;AAAA,IACrD,UAAA,EAAY;AAAA,MACV,OAAO,KAAA,CAAM,KAAA;AAAA,MACb,QAAQ,KAAA,CAAM,MAAA;AAAA,MACd,WAAA,EAAa,oBAAA,CAAqB,KAAA,CAAM,KAAA,EAAO,MAAM,MAAM;AAAA,KAC7D;AAAA,IACA,UAAU,KAAA,CAAM,SAAA;AAAA,IAChB,QAAA,EAAU;AAAA,MACR,OAAO,KAAA,CAAM,UAAA;AAAA,MACb,OAAO,KAAA,CAAM,UAAA;AAAA,MACb,aAAa,KAAA,CAAM;AAAA,KACrB;AAAA,IACA,eAAe,KAAA,CAAM,sBAAA;AAAA,IACrB,eAAe,KAAA,CAAM;AAAA,GACvB,CAAE,CAAA;AAGF,EAAA,MAAM,UAAA,GAAa,KAAA,CAAM,SAAA,CAAU,MAAA,CAAO,IAAI,CAAA,KAAA,MAAU;AAAA,IACtD,IAAI,KAAA,CAAM,EAAA;AAAA,IACV,MAAM,KAAA,CAAM,IAAA;AAAA,IACZ,UAAU,KAAA,CAAM,IAAA;AAAA,IAChB,UAAU,KAAA,CAAM,QAAA;AAAA,IAChB,eAAA,EAAiB,qBAAA,CAAsB,KAAA,CAAM,QAAQ,CAAA;AAAA,IACrD,MAAM,KAAA,CAAM,IAAA;AAAA,IACZ,WAAW,KAAA,CAAM,gBAAA;AAAA,IACjB,eAAe,KAAA,CAAM;AAAA,GACvB,CAAE,CAAA;AAGF,EAAA,MAAM,MAAA,GAAS,KAAA,CAAM,MAAA,CAAO,GAAA,CAAI,CAAA,KAAA,KAAS;AACvC,IAAA,MAAM,QAAA,GAAW,KAAA,CAAM,QAAA,CAAS,GAAA,CAAI,CAAA,OAAA,MAAY;AAAA,MAC9C,YAAY,OAAA,CAAQ,WAAA;AAAA,MACpB,SAAA,EAAW;AAAA,QACT,KAAA,EAAO,QAAQ,gBAAA,CAAiB,KAAA;AAAA,QAChC,QAAA,EAAU,QAAQ,gBAAA,CAAiB,QAAA;AAAA,QACnC,YAAA,EAAc,qBAAA,CAAsB,OAAA,CAAQ,gBAAA,CAAiB,KAAK,CAAA;AAAA,QAClE,eAAA,EAAiB,qBAAA,CAAsB,OAAA,CAAQ,gBAAA,CAAiB,QAAQ;AAAA,OAC1E;AAAA,MACA,eAAA,EAAiB,QAAQ,gBAAA,GAAmB;AAAA,QAC1C,KAAA,EAAO,QAAQ,gBAAA,CAAiB,KAAA;AAAA,QAChC,QAAA,EAAU,QAAQ,gBAAA,CAAiB,QAAA;AAAA,QACnC,YAAA,EAAc,qBAAA,CAAsB,OAAA,CAAQ,gBAAA,CAAiB,KAAK,CAAA;AAAA,QAClE,eAAA,EAAiB,qBAAA,CAAsB,OAAA,CAAQ,gBAAA,CAAiB,QAAQ;AAAA,OAC1E,GAAI;AAAA,QACF,KAAA,EAAO,CAAA;AAAA,QACP,QAAA,EAAU,CAAA;AAAA,QACV,YAAA,EAAc,CAAA;AAAA,QACd,eAAA,EAAiB;AAAA,OACnB;AAAA,MACA,SAAA,EAAW,QAAQ,IAAA,GAAO;AAAA,QACxB,KAAA,EAAO,QAAQ,IAAA,CAAK,KAAA;AAAA,QACpB,QAAA,EAAU,QAAQ,IAAA,CAAK,QAAA;AAAA,QACvB,KAAA,EAAO,QAAQ,IAAA,CAAK,KAAA;AAAA,QACpB,QAAA,EAAU,QAAQ,IAAA,CAAK,SAAA;AAAA,QACvB,IAAA,EAAM,QAAQ,IAAA,CAAK;AAAA,OACrB,GAAI;AAAA,QACF,KAAA,EAAO,CAAA;AAAA,QACP,QAAA,EAAU,CAAA;AAAA,QACV,KAAA,EAAO,EAAE,CAAA,EAAG,CAAA,EAAK,GAAG,CAAA,EAAI;AAAA,QACxB,QAAA,EAAU,EAAE,CAAA,EAAG,CAAA,EAAK,GAAG,CAAA,EAAI;AAAA,QAC3B,IAAA,EAAM,EAAE,UAAA,EAAY,KAAA,EAAO,UAAU,KAAA;AAAM;AAC7C,KACF,CAAE,CAAA;AAEF,IAAA,OAAO;AAAA,MACL,IAAI,KAAA,CAAM,EAAA;AAAA,MACV,IAAA,EAAM,KAAA,CAAM,IAAA,IAAQ,CAAA,aAAA,EAAM,MAAM,SAAS,CAAA,CAAA;AAAA,MACzC,IAAA,EAAM,KAAA,CAAM,SAAA,KAAc,CAAA,GAAI,OAAA,GAAU,OAAA;AAAA,MACxC,cAAc,QAAA,CAAS,MAAA;AAAA,MACvB;AAAA,KACF;AAAA,GACD,CAAA;AAGD,EAAA,MAAM,gBAAA,GAAmB,CAAC,GAAG,IAAI,GAAA,CAAI,UAAA,CAAW,GAAA,CAAI,CAAA,CAAA,KAAK,CAAA,CAAE,QAAQ,CAAC,CAAC,CAAA;AACrE,EAAA,MAAM,gBAAA,GAAmB,CAAC,GAAG,IAAI,GAAA,CAAI,UAAA,CAAW,GAAA,CAAI,CAAA,CAAA,KAAK,CAAA,CAAE,QAAQ,CAAC,CAAC,CAAA;AACrE,EAAA,MAAM,aAAA,GAAgB,OAAO,MAAA,CAAO,CAAC,KAAK,KAAA,KAAU,GAAA,GAAM,KAAA,CAAM,YAAA,EAAc,CAAC,CAAA;AAE/E,EAAA,OAAO;AAAA,IACL,WAAW,KAAA,CAAM,EAAA;AAAA,IACjB,iBAAiB,KAAA,CAAM,QAAA;AAAA,IACvB,sBAAA,EAAwB,qBAAA,CAAsB,KAAA,CAAM,QAAQ,CAAA;AAAA,IAC5D,KAAK,KAAA,CAAM,GAAA;AAAA,IACX,UAAA,EAAY;AAAA,MACV,KAAA,EAAO,MAAM,aAAA,CAAc,KAAA;AAAA,MAC3B,MAAA,EAAQ,MAAM,aAAA,CAAc,MAAA;AAAA,MAC5B,KAAA,EAAO,MAAM,aAAA,CAAc;AAAA,KAC7B;AAAA,IACA,UAAA;AAAA,IACA,UAAA;AAAA,IACA,MAAA;AAAA,IACA,OAAA,EAAS;AAAA,MACP,KAAA,EAAO,MAAM,sBAAA,CAAuB,MAAA;AAAA,MACpC,SAAA,EAAW,MAAM,sBAAA,CAAuB,UAAA;AAAA,MACxC,UAAA,EAAY,MAAM,sBAAA,CAAuB,WAAA;AAAA,MACzC,QAAA,EAAU,MAAM,sBAAA,CAAuB,QAAA;AAAA,MACvC,EAAA,EAAI,MAAM,sBAAA,CAAuB,EAAA;AAAA,MACjC,QAAA,EAAU,MAAM,sBAAA,CAAuB;AAAA,KACzC;AAAA,IACA,UAAA,EAAY;AAAA,MACV,iBAAiB,UAAA,CAAW,MAAA;AAAA,MAC5B,iBAAiB,UAAA,CAAW,MAAA;AAAA,MAC5B,aAAa,MAAA,CAAO,MAAA;AAAA,MACpB,aAAA;AAAA,MACA,gBAAA;AAAA,MACA;AAAA;AACF,GACF;AACF;AAKA,SAAS,gBAAgB,IAAA,EAAuB;AAC9C,EAAA,OAAA,CAAQ,IAAI,4DAAa,CAAA;AACzB,EAAA,OAAA,CAAQ,GAAA,CAAI,GAAA,CAAK,MAAA,CAAO,EAAE,CAAC,CAAA;AAG3B,EAAA,OAAA,CAAQ,IAAI,mDAAc,CAAA;AAC1B,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,kBAAA,EAAW,IAAA,CAAK,SAAS,CAAA,CAAE,CAAA;AACvC,EAAA,OAAA,CAAQ,IAAI,CAAA,sBAAA,EAAU,IAAA,CAAK,sBAAsB,CAAA,QAAA,EAAM,IAAA,CAAK,eAAe,CAAA,aAAA,CAAK,CAAA;AAChF,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,gBAAA,EAAS,IAAA,CAAK,GAAG,CAAA,IAAA,CAAM,CAAA;AACnC,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,4BAAA,EAAW,IAAA,CAAK,UAAA,CAAW,KAAK,CAAA,CAAA,EAAI,IAAA,CAAK,UAAA,CAAW,MAAM,CAAA,EAAA,EAAK,IAAA,CAAK,UAAA,CAAW,KAAK,CAAA,CAAA,CAAG,CAAA;AAGnG,EAAA,OAAA,CAAQ,IAAI,uCAAY,CAAA;AACxB,EAAA,OAAA,CAAQ,GAAA,CAAI,mBAAS,IAAA,CAAK,OAAA,CAAQ,SAAS,CAAA,EAAA,EAAK,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA,CAAE,CAAA;AACzE,EAAA,OAAA,CAAQ,GAAA,CAAI,mBAAS,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAA,EAAA,EAAK,IAAA,CAAK,OAAA,CAAQ,EAAE,CAAA,CAAA,CAAG,CAAA;AACjE,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,kBAAA,EAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAA,CAAE,CAAA;AAG9C,EAAA,OAAA,CAAQ,IAAI,uCAAY,CAAA;AACxB,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,4BAAA,EAAW,IAAA,CAAK,UAAA,CAAW,eAAe,CAAA,MAAA,CAAG,CAAA;AACzD,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,4BAAA,EAAW,IAAA,CAAK,UAAA,CAAW,eAAe,CAAA,MAAA,CAAG,CAAA;AACzD,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,4BAAA,EAAW,IAAA,CAAK,UAAA,CAAW,WAAW,CAAA,MAAA,CAAG,CAAA;AACrD,EAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,4BAAA,EAAW,IAAA,CAAK,UAAA,CAAW,aAAa,CAAA,MAAA,CAAG,CAAA;AACvD,EAAA,OAAA,CAAQ,IAAI,CAAA,wCAAA,EAAa,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,MAAM,CAAA,MAAA,CAAG,CAAA;AACnE,EAAA,OAAA,CAAQ,IAAI,CAAA,wCAAA,EAAa,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,MAAM,CAAA,MAAA,CAAG,CAAA;AAGnE,EAAA,IAAI,IAAA,CAAK,UAAA,CAAW,MAAA,GAAS,CAAA,EAAG;AAC9B,IAAA,OAAA,CAAQ,IAAI,mDAAc,CAAA;AAC1B,IAAA,IAAA,CAAK,UAAA,CAAW,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAA,KAAU;AACvC,MAAA,OAAA,CAAQ,IAAI,CAAA,EAAA,EAAK,KAAA,GAAQ,CAAC,CAAA,EAAA,EAAK,IAAA,CAAK,QAAQ,CAAA,CAAE,CAAA;AAC9C,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,+BAAA,EAAc,IAAA,CAAK,QAAQ,CAAA,CAAE,CAAA;AACzC,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,mBAAA,EAAY,IAAA,CAAK,eAAe,CAAA,MAAA,CAAG,CAAA;AAC/C,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,yBAAA,EAAa,IAAA,CAAK,UAAA,CAAW,KAAK,CAAA,CAAA,EAAI,IAAA,CAAK,UAAA,CAAW,MAAM,CAAA,EAAA,EAAK,IAAA,CAAK,UAAA,CAAW,WAAW,CAAA,CAAA,CAAG,CAAA;AAC3G,MAAA,OAAA,CAAQ,IAAI,CAAA,+BAAA,EAAc,IAAA,CAAK,QAAA,GAAW,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AACrD,MAAA,OAAA,CAAQ,GAAA,CAAI,kCAAc,IAAA,CAAK,QAAA,CAAS,KAAK,CAAA,gBAAA,EAAS,IAAA,CAAK,QAAA,CAAS,KAAK,CAAA,CAAA,CAAG,CAAA;AAC5E,MAAA,OAAA,CAAQ,IAAI,CAAA,qBAAA,EAAc,IAAA,CAAK,aAAA,GAAgB,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AAC1D,MAAA,OAAA,CAAQ,IAAI,CAAA,+BAAA,EAAc,IAAA,CAAK,aAAA,GAAgB,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AAC1D,MAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AAAA,KACf,CAAA;AAAA;AAIH,EAAA,IAAI,IAAA,CAAK,UAAA,CAAW,MAAA,GAAS,CAAA,EAAG;AAC9B,IAAA,OAAA,CAAQ,IAAI,mDAAc,CAAA;AAC1B,IAAA,IAAA,CAAK,UAAA,CAAW,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAA,KAAU;AACvC,MAAA,OAAA,CAAQ,IAAI,CAAA,EAAA,EAAK,KAAA,GAAQ,CAAC,CAAA,EAAA,EAAK,IAAA,CAAK,IAAI,CAAA,CAAE,CAAA;AAC1C,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,+BAAA,EAAc,IAAA,CAAK,QAAQ,CAAA,CAAE,CAAA;AACzC,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,mBAAA,EAAY,IAAA,CAAK,eAAe,CAAA,MAAA,CAAG,CAAA;AAC/C,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,mBAAA,EAAY,IAAA,CAAK,IAAI,CAAA,CAAE,CAAA;AACnC,MAAA,OAAA,CAAQ,IAAI,CAAA,iCAAA,EAAgB,IAAA,CAAK,SAAA,GAAY,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AACxD,MAAA,OAAA,CAAQ,IAAI,CAAA,+BAAA,EAAc,IAAA,CAAK,aAAA,GAAgB,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AAC1D,MAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AAAA,KACf,CAAA;AAAA;AAIH,EAAA,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,CAAA,EAAG;AAC1B,IAAA,OAAA,CAAQ,IAAI,6CAAa,CAAA;AACzB,IAAA,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,CAAC,KAAA,EAAO,KAAA,KAAU;AACpC,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,EAAA,EAAK,KAAA,GAAQ,CAAC,CAAA,EAAA,EAAK,MAAM,IAAI,CAAA,EAAA,EAAK,KAAA,CAAM,IAAI,CAAA,CAAA,CAAG,CAAA;AAC3D,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,qBAAA,EAAc,KAAA,CAAM,EAAE,CAAA,CAAE,CAAA;AACpC,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,+BAAA,EAAc,KAAA,CAAM,YAAY,CAAA,MAAA,CAAG,CAAA;AAE/C,MAAA,IAAI,KAAA,CAAM,QAAA,CAAS,MAAA,GAAS,CAAA,EAAG;AAC7B,QAAA,OAAA,CAAQ,IAAI,gCAAY,CAAA;AACxB,QAAA,KAAA,CAAM,QAAA,CAAS,OAAA,CAAQ,CAAC,OAAA,EAAS,QAAA,KAAa;AAC5C,UAAA,OAAA,CAAQ,IAAI,CAAA,OAAA,EAAU,QAAA,GAAW,CAAC,CAAA,kBAAA,EAAW,OAAA,CAAQ,UAAU,CAAA,CAAE,CAAA;AACjE,UAAA,OAAA,CAAQ,IAAI,CAAA,8BAAA,EAAkB,OAAA,CAAQ,SAAA,CAAU,YAAY,OAAO,OAAA,CAAQ,SAAA,CAAU,YAAA,GAAe,OAAA,CAAQ,UAAU,eAAe,CAAA,iBAAA,EAAU,OAAA,CAAQ,SAAA,CAAU,eAAe,CAAA,EAAA,CAAI,CAAA;AACpL,UAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,8BAAA,EAAkB,OAAA,CAAQ,eAAA,CAAgB,YAAY,CAAA,IAAA,EAAO,OAAA,CAAQ,eAAA,CAAgB,YAAA,GAAe,OAAA,CAAQ,eAAA,CAAgB,eAAe,CAAA,CAAA,CAAG,CAAA;AAC1J,UAAA,OAAA,CAAQ,GAAA,CAAI,8CAAqB,OAAA,CAAQ,SAAA,CAAU,KAAK,CAAA,eAAA,EAAQ,OAAA,CAAQ,SAAA,CAAU,QAAQ,CAAA,IAAA,CAAG,CAAA;AAC7F,UAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,0BAAA,EAAmB,OAAA,CAAQ,SAAA,CAAU,KAAA,CAAM,CAAC,CAAA,IAAA,EAAO,OAAA,CAAQ,SAAA,CAAU,KAAA,CAAM,CAAC,CAAA,CAAE,CAAA;AAC1F,UAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,0BAAA,EAAmB,OAAA,CAAQ,SAAA,CAAU,QAAA,CAAS,CAAC,CAAA,IAAA,EAAO,OAAA,CAAQ,SAAA,CAAU,QAAA,CAAS,CAAC,CAAA,CAAE,CAAA;AAChG,UAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,qCAAA,EAAoB,OAAA,CAAQ,SAAA,CAAU,IAAA,CAAK,UAAU,CAAA,eAAA,EAAQ,OAAA,CAAQ,SAAA,CAAU,IAAA,CAAK,QAAQ,CAAA,CAAE,CAAA;AAAA,SAC3G,CAAA;AAAA;AAEH,MAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AAAA,KACf,CAAA;AAAA;AAIH,EAAA,OAAA,CAAQ,IAAI,yDAAe,CAAA;AAC3B,EAAA,OAAA,CAAQ,IAAI,6BAAS,CAAA;AACrB,EAAA,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,OAAA,CAAQ,CAAC,MAAM,KAAA,KAAU;AACxD,IAAA,OAAA,CAAQ,IAAI,CAAA,IAAA,EAAO,KAAA,GAAQ,CAAC,CAAA,EAAA,EAAK,IAAI,CAAA,CAAE,CAAA;AAAA,GACxC,CAAA;AAED,EAAA,IAAI,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,MAAA,GAAS,CAAA,EAAG;AAC/C,IAAA,OAAA,CAAQ,IAAI,6BAAS,CAAA;AACrB,IAAA,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,OAAA,CAAQ,CAAC,MAAM,KAAA,KAAU;AACxD,MAAA,OAAA,CAAQ,IAAI,CAAA,IAAA,EAAO,KAAA,GAAQ,CAAC,CAAA,EAAA,EAAK,IAAI,CAAA,CAAE,CAAA;AAAA,KACxC,CAAA;AAAA;AAEL;AAKA,SAAS,YAAA,CAAa,MAAiB,UAAA,EAA0B;AAC/D,EAAA,MAAM,WAAA,GAAc,IAAA,CAAK,SAAA,CAAU,IAAA,EAAM,MAAM,CAAC,CAAA;AAChD,EAAGA,GAAA,CAAA,aAAA,CAAc,UAAA,EAAY,WAAA,EAAa,OAAO,CAAA;AACjD,EAAA,OAAA,CAAQ,GAAA,CAAI;AAAA,4DAAA,EAAkB,UAAU,CAAA,CAAE,CAAA;AAC5C;AAKA,SAAS,IAAA,GAAa;AACpB,EAAA,MAAM,IAAA,GAAO,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA;AAEjC,EAAA,IAAI,IAAA,CAAK,WAAW,CAAA,EAAG;AACrB,IAAA,OAAA,CAAQ,IAAI,2BAAO,CAAA;AACnB,IAAA,OAAA,CAAQ,IAAI,0FAA4D,CAAA;AACxE,IAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AACd,IAAA,OAAA,CAAQ,IAAI,eAAK,CAAA;AACjB,IAAA,OAAA,CAAQ,IAAI,+CAA+C,CAAA;AAC3D,IAAA,OAAA,CAAQ,IAAI,6DAA6D,CAAA;AACzE,IAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAGhB,EAAA,MAAM,SAAA,GAAY,KAAK,CAAC,CAAA;AACxB,EAAA,MAAM,UAAA,GAAa,KAAK,CAAC,CAAA;AAEzB,EAAA,IAAI;AACF,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,wEAAA,EAAkB,SAAS,CAAA,CAAE,CAAA;AAEzC,IAAA,MAAM,SAAA,GAAY,mBAAmB,SAAS,CAAA;AAG9C,IAAA,eAAA,CAAgB,SAAS,CAAA;AAGzB,IAAA,IAAI,UAAA,EAAY;AACd,MAAA,YAAA,CAAa,WAAW,UAAU,CAAA;AAAA;AAGpC,IAAA,OAAA,CAAQ,IAAI,oCAAW,CAAA;AAAA,WAEhB,KAAA,EAAO;AACd,IAAA,OAAA,CAAQ,KAAA,CAAM,oCAAW,KAAA,YAAiB,KAAA,GAAQ,MAAM,OAAA,GAAU,MAAA,CAAO,KAAK,CAAC,CAAA;AAC/E,IAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAElB;AAGA,IAAI,SAAA,CAAQ,SAAS,MAAA,EAAQ;AAC3B,EAAA,IAAA,EAAK;AACP;AASO,SAAS,sBAAsB,OAAA,EAAyB;AAC7D,EAAA,OAAO,OAAA,GAAU,GAAA;AACnB;ACjeA,SAAS,oBAAoB,IAAA,EAA6D;AACxF,EAAA,IAAI,KAAA,GAAQ,CAAA;AACZ,EAAA,MAAM,UAAoB,EAAC;AAG3B,EAAA,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,CAAA,EAAG;AAC1B,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,gCAAA,EAAU,IAAA,CAAK,MAAA,CAAO,MAAM,CAAA,mBAAA,CAAM,CAAA;AAAA;AAIjD,EAAA,MAAM,aAAA,GAAgB,KAAK,UAAA,CAAW,aAAA;AACtC,EAAA,IAAI,gBAAgB,EAAA,EAAI;AACtB,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,0BAAA,EAAS,aAAa,CAAA,mBAAA,CAAM,CAAA;AAAA,GAC3C,MAAA,IAAW,gBAAgB,EAAA,EAAI;AAC7B,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,sCAAA,EAAW,aAAa,CAAA,mBAAA,CAAM,CAAA;AAAA;AAI7C,EAAA,IAAI,IAAA,CAAK,UAAA,CAAW,eAAA,GAAkB,EAAA,EAAI;AACxC,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,sCAAA,EAAW,IAAA,CAAK,UAAA,CAAW,eAAe,CAAA,OAAA,CAAI,CAAA;AAAA;AAI7D,EAAA,IAAI,mBAAA,GAAsB,CAAA;AAC1B,EAAA,IAAA,CAAK,MAAA,CAAO,QAAQ,CAAA,KAAA,KAAS;AAC3B,IAAA,KAAA,CAAM,QAAA,CAAS,QAAQ,CAAA,OAAA,KAAW;AAChC,MAAA,IAAI,OAAA,CAAQ,SAAA,CAAU,QAAA,KAAa,CAAA,EAAG,mBAAA,EAAA;AACtC,MAAA,IAAI,OAAA,CAAQ,UAAU,KAAA,CAAM,CAAA,KAAM,KAAK,OAAA,CAAQ,SAAA,CAAU,KAAA,CAAM,CAAA,KAAM,CAAA,EAAG,mBAAA,EAAA;AACxE,MAAA,IAAI,OAAA,CAAQ,UAAU,QAAA,CAAS,CAAA,KAAM,KAAK,OAAA,CAAQ,SAAA,CAAU,QAAA,CAAS,CAAA,KAAM,CAAA,EAAG,mBAAA,EAAA;AAC9E,MAAA,IAAI,QAAQ,SAAA,CAAU,IAAA,CAAK,cAAc,OAAA,CAAQ,SAAA,CAAU,KAAK,QAAA,EAAU,mBAAA,EAAA;AAAA,KAC3E,CAAA;AAAA,GACF,CAAA;AAED,EAAA,IAAI,sBAAsB,EAAA,EAAI;AAC5B,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,0BAAA,EAAS,mBAAmB,CAAA,mBAAA,CAAM,CAAA;AAAA,GACjD,MAAA,IAAW,sBAAsB,CAAA,EAAG;AAClC,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,0BAAA,EAAS,mBAAmB,CAAA,mBAAA,CAAM,CAAA;AAAA;AAIjD,EAAA,IAAI,IAAA,CAAK,yBAAyB,GAAA,EAAK;AACrC,IAAA,KAAA,IAAS,EAAA;AACT,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,oBAAA,EAAQ,IAAA,CAAK,sBAAsB,CAAA,OAAA,CAAI,CAAA;AAAA;AAGtD,EAAA,IAAI,KAAA;AACJ,EAAA,IAAI,KAAA,IAAS,IAAI,KAAA,GAAQ,cAAA;AAAA,OAAA,IAChB,KAAA,IAAS,IAAI,KAAA,GAAQ,SAAA;AAAA,OAAA,IACrB,KAAA,IAAS,IAAI,KAAA,GAAQ,QAAA;AAAA,OACzB,KAAA,GAAQ,QAAA;AAEb,EAAA,OAAO,EAAE,KAAA,EAAO,KAAA,EAAO,OAAA,EAAQ;AACjC;AAKA,SAAS,gBAAgB,IAAA,EAA2D;AAClF,EAAA,MAAM,cAAc,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,CAAA,KAAA,KAAS,MAAM,QAAQ,CAAA;AAC/D,EAAA,MAAM,YAAY,WAAA,CAAY,GAAA,CAAI,CAAA,GAAA,KAAO,GAAA,CAAI,UAAU,eAAe,CAAA;AAEtE,EAAA,OAAO;AAAA,IACL,eAAe,IAAA,CAAK,sBAAA;AAAA,IACpB,cAAc,WAAA,CAAY,MAAA;AAAA,IAC1B,oBAAA,EAAsB,SAAA,CAAU,MAAA,CAAO,CAAC,CAAA,EAAG,MAAM,CAAA,GAAI,CAAA,EAAG,CAAC,CAAA,GAAI,SAAA,CAAU,MAAA;AAAA,IACvE,eAAA,EAAiB,IAAA,CAAK,GAAA,CAAI,GAAG,SAAS,CAAA;AAAA,IACtC,cAAA,EAAgB,IAAA,CAAK,GAAA,CAAI,GAAG,SAAS;AAAA,GACvC;AACF;AAKA,SAAS,iBAAiB,IAAA,EAA4D;AAEpF,EAAA,MAAM,cAAA,uBAAqB,GAAA,EAIxB;AAEH,EAAA,IAAA,CAAK,MAAA,CAAO,QAAQ,CAAA,KAAA,KAAS;AAC3B,IAAA,KAAA,CAAM,QAAA,CAAS,QAAQ,CAAA,OAAA,KAAW;AAEhC,MAAA,MAAM,SAAA,GAAY,KAAK,UAAA,CAAW,IAAA,CAAK,UAAQ,IAAA,CAAK,EAAA,KAAO,QAAQ,UAAU,CAAA;AAC7E,MAAA,IAAI,SAAA,EAAW;AACb,QAAA,MAAM,WAAW,SAAA,CAAU,QAAA;AAC3B,QAAA,IAAI,CAAC,cAAA,CAAe,GAAA,CAAI,QAAQ,CAAA,EAAG;AACjC,UAAA,cAAA,CAAe,GAAA,CAAI,QAAA,EAAU,EAAE,UAAA,EAAY,CAAA,EAAG,eAAe,CAAA,EAAG,QAAA,EAAU,EAAC,EAAG,CAAA;AAAA;AAEhF,QAAA,MAAM,KAAA,GAAQ,cAAA,CAAe,GAAA,CAAI,QAAQ,CAAA;AACzC,QAAA,KAAA,CAAM,UAAA,EAAA;AACN,QAAA,KAAA,CAAM,aAAA,IAAiB,QAAQ,SAAA,CAAU,eAAA;AACzC,QAAA,KAAA,CAAM,SAAS,IAAA,CAAK;AAAA,UAClB,SAAS,KAAA,CAAM,EAAA;AAAA,UACf,SAAA,EAAW,QAAQ,SAAA,CAAU,YAAA;AAAA,UAC7B,QAAA,EAAU,QAAQ,SAAA,CAAU;AAAA,SAC7B,CAAA;AAAA;AACH,KACD,CAAA;AAAA,GACF,CAAA;AAGD,EAAA,MAAM,cAAA,GAAiB,IAAA,CAAK,UAAA,CAAW,GAAA,CAAI,CAAA,IAAA,MAAS;AAAA,IAClD,UAAU,IAAA,CAAK,QAAA;AAAA,IACf,UAAA,EAAY,CAAA;AAAA;AAAA,IACZ,eAAe,IAAA,CAAK;AAAA,GACtB,CAAE,CAAA;AAEF,EAAA,OAAO;AAAA,IACL,cAAA,EAAgB,KAAA,CAAM,IAAA,CAAK,cAAA,CAAe,OAAA,EAAS,CAAA,CAAE,GAAA,CAAI,CAAC,CAAC,QAAA,EAAU,KAAK,CAAA,MAAO;AAAA,MAC/E,QAAA;AAAA,MACA,GAAG;AAAA,KACL,CAAE,CAAA;AAAA,IACF;AAAA,GACF;AACF;AAKA,SAAS,eAAe,IAAA,EAA0D;AAChF,EAAA,IAAI,kBAAA,GAAqB,KAAA;AACzB,EAAA,IAAI,UAAA,GAAa,KAAA;AACjB,EAAA,IAAI,WAAA,GAAc,KAAA;AAClB,EAAA,IAAI,kBAAA,GAAqB,KAAA;AACzB,EAAA,IAAI,WAAA,GAAc,KAAA;AAClB,EAAA,IAAI,mBAAA,GAAsB,CAAA;AAE1B,EAAA,IAAA,CAAK,MAAA,CAAO,QAAQ,CAAA,KAAA,KAAS;AAC3B,IAAA,KAAA,CAAM,QAAA,CAAS,QAAQ,CAAA,OAAA,KAAW;AAChC,MAAA,IAAI,OAAA,CAAQ,SAAA,CAAU,QAAA,KAAa,CAAA,EAAG;AACpC,QAAA,WAAA,GAAc,IAAA;AACd,QAAA,kBAAA,GAAqB,IAAA;AACrB,QAAA,mBAAA,EAAA;AAAA;AAEF,MAAA,IAAI,OAAA,CAAQ,UAAU,KAAA,CAAM,CAAA,KAAM,KAAK,OAAA,CAAQ,SAAA,CAAU,KAAA,CAAM,CAAA,KAAM,CAAA,EAAG;AACtE,QAAA,UAAA,GAAa,IAAA;AACb,QAAA,kBAAA,GAAqB,IAAA;AACrB,QAAA,mBAAA,EAAA;AAAA;AAEF,MAAA,IAAI,OAAA,CAAQ,UAAU,QAAA,CAAS,CAAA,KAAM,KAAK,OAAA,CAAQ,SAAA,CAAU,QAAA,CAAS,CAAA,KAAM,CAAA,EAAG;AAC5E,QAAA,kBAAA,GAAqB,IAAA;AACrB,QAAA,kBAAA,GAAqB,IAAA;AACrB,QAAA,mBAAA,EAAA;AAAA;AAEF,MAAA,IAAI,QAAQ,SAAA,CAAU,IAAA,CAAK,cAAc,OAAA,CAAQ,SAAA,CAAU,KAAK,QAAA,EAAU;AACxE,QAAA,WAAA,GAAc,IAAA;AACd,QAAA,kBAAA,GAAqB,IAAA;AACrB,QAAA,mBAAA,EAAA;AAAA;AACF,KACD,CAAA;AAAA,GACF,CAAA;AAED,EAAA,OAAO;AAAA,IACL,kBAAA;AAAA,IACA,UAAA;AAAA,IACA,WAAA;AAAA,IACA,kBAAA;AAAA,IACA,WAAA;AAAA,IACA;AAAA,GACF;AACF;AAKA,SAAS,wBAAwB,QAAA,EAA6G;AAC5I,EAAA,MAAM,kBAA8D,EAAC;AAErE,EAAA,IAAI,QAAA,CAAS,UAAA,CAAW,KAAA,KAAU,cAAA,EAAgB;AAChD,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,aAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,IAAI,QAAA,CAAS,QAAA,CAAS,YAAA,GAAe,EAAA,EAAI;AACvC,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,cAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,IAAI,QAAA,CAAS,QAAA,CAAS,oBAAA,GAAuB,CAAA,EAAG;AAC9C,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,SAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,IAAI,QAAA,CAAS,OAAA,CAAQ,mBAAA,GAAsB,EAAA,EAAI;AAC7C,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,aAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,MAAM,cAAA,GAAiB,QAAA,CAAS,SAAA,CAAU,cAAA,CAAe,MAAA;AACzD,EAAA,IAAI,mBAAmB,CAAA,EAAG;AACxB,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,SAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA,GACH,MAAA,IAAW,iBAAiB,EAAA,EAAI;AAC9B,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,YAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,IAAI,eAAA,CAAgB,WAAW,CAAA,EAAG;AAChC,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,SAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,OAAO,eAAA;AACT;AAKA,SAAS,wBAAwB,QAAA,EAAoC;AACnE,EAAA,MAAM,SAAA,GAAY,mBAAmB,QAAQ,CAAA;AAE7C,EAAA,MAAM,UAAA,GAAa,oBAAoB,SAAS,CAAA;AAChD,EAAA,MAAM,QAAA,GAAW,gBAAgB,SAAS,CAAA;AAC1C,EAAA,MAAM,SAAA,GAAY,iBAAiB,SAAS,CAAA;AAC5C,EAAA,MAAM,OAAA,GAAU,eAAe,SAAS,CAAA;AACxC,EAAA,MAAM,YAAA,GAAe,EAAE,UAAA,EAAY,QAAA,EAAU,WAAW,OAAA,EAAQ;AAChE,EAAA,MAAM,eAAA,GAAkB,wBAAwB,YAAY,CAAA;AAE5D,EAAA,OAAO;AAAA,IACL,SAAA;AAAA,IACA,QAAA,EAAU;AAAA,MACR,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA;AAAA,KACF;AAAA,IACA;AAAA,GACF;AACF;AAKA,SAAS,uBAAuB,QAAA,EAAkC;AAChE,EAAA,OAAA,CAAQ,IAAI,wEAAe,CAAA;AAC3B,EAAA,OAAA,CAAQ,GAAA,CAAI,GAAA,CAAK,MAAA,CAAO,EAAE,CAAC,CAAA;AAG3B,EAAA,OAAA,CAAQ,IAAI,yDAAe,CAAA;AAC3B,EAAA,OAAA,CAAQ,IAAI,CAAA,kCAAA,EAAY,QAAA,CAAS,QAAA,CAAS,UAAA,CAAW,KAAK,CAAA,CAAE,CAAA;AAC5D,EAAA,OAAA,CAAQ,IAAI,CAAA,kCAAA,EAAY,QAAA,CAAS,QAAA,CAAS,UAAA,CAAW,KAAK,CAAA,IAAA,CAAM,CAAA;AAChE,EAAA,OAAA,CAAQ,IAAI,6BAAS,CAAA;AACrB,EAAA,QAAA,CAAS,QAAA,CAAS,UAAA,CAAW,OAAA,CAAQ,OAAA,CAAQ,CAAA,MAAA,KAAU;AACrD,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,WAAA,EAAS,MAAM,CAAA,CAAE,CAAA;AAAA,GAC9B,CAAA;AAGD,EAAA,OAAA,CAAQ,IAAI,gDAAa,CAAA;AACzB,EAAA,OAAA,CAAQ,GAAA,CAAI,yBAAU,QAAA,CAAS,QAAA,CAAS,SAAS,aAAA,CAAc,OAAA,CAAQ,CAAC,CAAC,CAAA,MAAA,CAAG,CAAA;AAC5E,EAAA,OAAA,CAAQ,IAAI,CAAA,4BAAA,EAAW,QAAA,CAAS,QAAA,CAAS,QAAA,CAAS,YAAY,CAAA,MAAA,CAAG,CAAA;AACjE,EAAA,OAAA,CAAQ,GAAA,CAAI,2CAAa,QAAA,CAAS,QAAA,CAAS,SAAS,oBAAA,CAAqB,OAAA,CAAQ,CAAC,CAAC,CAAA,MAAA,CAAG,CAAA;AACtF,EAAA,OAAA,CAAQ,GAAA,CAAI,+BAAW,QAAA,CAAS,QAAA,CAAS,SAAS,eAAA,CAAgB,OAAA,CAAQ,CAAC,CAAC,CAAA,MAAA,CAAG,CAAA;AAC/E,EAAA,OAAA,CAAQ,GAAA,CAAI,+BAAW,QAAA,CAAS,QAAA,CAAS,SAAS,cAAA,CAAe,OAAA,CAAQ,CAAC,CAAC,CAAA,MAAA,CAAG,CAAA;AAG9E,EAAA,OAAA,CAAQ,IAAI,mDAAc,CAAA;AAC1B,EAAA,OAAA,CAAQ,IAAI,qDAAa,CAAA;AACzB,EAAA,QAAA,CAAS,SAAS,SAAA,CAAU,cAAA,CAAe,OAAA,CAAQ,CAAC,OAAO,KAAA,KAAU;AACnE,IAAA,OAAA,CAAQ,GAAA,CAAI,OAAO,KAAA,GAAQ,CAAC,KAAUC,KAAA,CAAA,QAAA,CAAS,KAAA,CAAM,QAAQ,CAAC,CAAA,CAAE,CAAA;AAChE,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,iCAAA,EAAgB,KAAA,CAAM,UAAU,CAAA,MAAA,CAAG,CAAA;AAC/C,IAAA,OAAA,CAAQ,IAAI,CAAA,uCAAA,EAAiB,KAAA,CAAM,cAAc,OAAA,CAAQ,CAAC,CAAC,CAAA,MAAA,CAAG,CAAA;AAC9D,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,iCAAA,EAAgB,KAAA,CAAM,QAAA,CAAS,MAAM,CAAA,kBAAA,CAAK,CAAA;AAAA,GACvD,CAAA;AAGD,EAAA,OAAA,CAAQ,IAAI,mDAAc,CAAA;AAC1B,EAAA,OAAA,CAAQ,GAAA,CAAI,2CAAa,QAAA,CAAS,QAAA,CAAS,QAAQ,kBAAA,GAAqB,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AACnF,EAAA,OAAA,CAAQ,GAAA,CAAI,+BAAW,QAAA,CAAS,QAAA,CAAS,QAAQ,UAAA,GAAa,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AACzE,EAAA,OAAA,CAAQ,GAAA,CAAI,+BAAW,QAAA,CAAS,QAAA,CAAS,QAAQ,WAAA,GAAc,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AAC1E,EAAA,OAAA,CAAQ,GAAA,CAAI,2CAAa,QAAA,CAAS,QAAA,CAAS,QAAQ,kBAAA,GAAqB,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AACnF,EAAA,OAAA,CAAQ,GAAA,CAAI,+BAAW,QAAA,CAAS,QAAA,CAAS,QAAQ,WAAA,GAAc,QAAA,GAAM,QAAG,CAAA,CAAE,CAAA;AAC1E,EAAA,OAAA,CAAQ,IAAI,CAAA,wCAAA,EAAa,QAAA,CAAS,QAAA,CAAS,OAAA,CAAQ,mBAAmB,CAAA,MAAA,CAAG,CAAA;AAGzE,EAAA,OAAA,CAAQ,IAAI,uCAAY,CAAA;AACxB,EAAA,QAAA,CAAS,eAAA,CAAgB,QAAQ,CAAA,GAAA,KAAO;AACtC,IAAA,OAAA,CAAQ,IAAI,CAAA,UAAA,EAAQ,GAAA,CAAI,IAAI,CAAA,EAAA,EAAK,GAAA,CAAI,WAAW,CAAA,CAAE,CAAA;AAAA,GACnD,CAAA;AACH;AAKA,SAASC,KAAAA,GAAa;AACpB,EAAA,MAAM,IAAA,GAAO,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA;AAEjC,EAAA,IAAI,IAAA,CAAK,WAAW,CAAA,EAAG;AACrB,IAAA,OAAA,CAAQ,IAAI,2BAAO,CAAA;AACnB,IAAA,OAAA,CAAQ,IAAI,8FAAgE,CAAA;AAC5E,IAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AACd,IAAA,OAAA,CAAQ,IAAI,eAAK,CAAA;AACjB,IAAA,OAAA,CAAQ,IAAI,mDAAmD,CAAA;AAC/D,IAAA,OAAA,CAAQ,IAAI,4EAA4E,CAAA;AACxF,IAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAGhB,EAAA,MAAM,SAAA,GAAY,KAAK,CAAC,CAAA;AACxB,EAAA,MAAM,UAAA,GAAa,KAAK,CAAC,CAAA;AAEzB,EAAA,IAAI;AACF,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,4DAAA,EAAgB,SAAS,CAAA,CAAE,CAAA;AAEvC,IAAA,MAAM,QAAA,GAAW,wBAAwB,SAAS,CAAA;AAGlD,IAAA,sBAAA,CAAuB,QAAQ,CAAA;AAG/B,IAAA,IAAI,UAAA,EAAY;AACd,MAAA,MAAM,WAAA,GAAc,IAAA,CAAK,SAAA,CAAU,QAAA,EAAU,MAAM,CAAC,CAAA;AACpD,MAAGC,GAAA,CAAA,aAAA,CAAc,UAAA,EAAY,WAAA,EAAa,OAAO,CAAA;AACjD,MAAA,OAAA,CAAQ,GAAA,CAAI;AAAA,wEAAA,EAAoB,UAAU,CAAA,CAAE,CAAA;AAAA;AAG9C,IAAA,OAAA,CAAQ,IAAI,gDAAa,CAAA;AAAA,WAElB,KAAA,EAAO;AACd,IAAA,OAAA,CAAQ,KAAA,CAAM,oCAAW,KAAA,YAAiB,KAAA,GAAQ,MAAM,OAAA,GAAU,MAAA,CAAO,KAAK,CAAC,CAAA;AAC/E,IAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAElB;AAGA,IAAI,SAAA,CAAQ,SAAS,MAAA,EAAQ;AAC3B,EAAAD,KAAAA,EAAK;AACP;AAMO,SAAS,uBAAuB,QAAA,EAAoC;AACzE,EAAA,OAAO,wBAAwB,QAAQ,CAAA;AACzC;AAEO,SAAS,+BAA+B,SAAA,EAAwC;AACrF,EAAA,MAAM,UAAA,GAAa,oBAAoB,SAAS,CAAA;AAChD,EAAA,MAAM,QAAA,GAAW,gBAAgB,SAAS,CAAA;AAC1C,EAAA,MAAM,SAAA,GAAY,iBAAiB,SAAS,CAAA;AAC5C,EAAA,MAAM,OAAA,GAAU,eAAe,SAAS,CAAA;AACxC,EAAA,MAAM,YAAA,GAAe,EAAE,UAAA,EAAY,QAAA,EAAU,WAAW,OAAA,EAAQ;AAChE,EAAA,MAAM,eAAA,GAAkB,oCAAoC,YAAY,CAAA;AAExE,EAAA,OAAO;AAAA,IACL,SAAA,EAAW,SAAA;AAAA,IACX,QAAA,EAAU,YAAA;AAAA,IACV;AAAA,GACF;AACF;AAEO,SAAS,oCAAoC,QAAA,EAAsB;AACxE,EAAA,MAAM,kBAAyB,EAAC;AAEhC,EAAA,IAAI,QAAA,CAAS,UAAA,CAAW,KAAA,KAAU,cAAA,EAAgB;AAChD,IAAA,eAAA,CAAgB,IAAA,CAAK;AAAA,MACnB,IAAA,EAAM,aAAA;AAAA,MACN,WAAA,EAAa;AAAA,KACd,CAAA;AAAA;AAGH,EAAA,OAAO,eAAA;AACT;AChbA,SAAS,YAAA,GAAuB;AAC9B,EAAA,OAAO,sCAAA,CAAuC,OAAA,CAAQ,OAAA,EAAS,SAAS,CAAA,EAAG;AACzE,IAAA,MAAM,CAAA,GAAI,IAAA,CAAK,MAAA,EAAO,GAAI,EAAA,GAAK,CAAA;AAC/B,IAAA,MAAM,CAAA,GAAI,CAAA,KAAM,GAAA,GAAM,CAAA,GAAK,IAAI,CAAA,GAAM,CAAA;AACrC,IAAA,OAAO,CAAA,CAAE,SAAS,EAAE,CAAA;AAAA,GACrB,CAAA;AACH;AAGA,IAAM,gBAAA,GAAmB,CAAC,MAAA,EAAQ,MAAA,EAAQ,QAAQ,MAAA,EAAQ,MAAA,EAAQ,MAAA,EAAQ,OAAA,EAAS,MAAM,CAAA;AACzF,IAAM,mBAAmB,CAAC,MAAA,EAAQ,QAAQ,MAAA,EAAQ,MAAA,EAAQ,SAAS,MAAM,CAAA;AAmRzE,SAAS,cAAc,OAAA,EAAkC;AACvD,EAAA,MAAM,QAAyB,EAAC;AAEhC,EAAA,SAAS,cAAc,WAAA,EAAqB;AAC1C,IAAA,IAAI;AACF,MAAA,MAAM,KAAA,GAAW,gBAAY,WAAW,CAAA;AAExC,MAAA,KAAA,MAAW,QAAQ,KAAA,EAAO;AACxB,QAAA,MAAM,QAAA,GAAgB,KAAA,CAAA,IAAA,CAAK,WAAA,EAAa,IAAI,CAAA;AAC5C,QAAA,MAAM,IAAA,GAAU,aAAS,QAAQ,CAAA;AAEjC,QAAA,IAAI,IAAA,CAAK,aAAY,EAAG;AAEtB,UAAA,aAAA,CAAc,QAAQ,CAAA;AAAA,SACxB,MAAA,IAAW,IAAA,CAAK,MAAA,EAAO,EAAG;AACxB,UAAA,MAAM,GAAA,GAAW,KAAA,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAE,WAAA,EAAY;AAC3C,UAAA,MAAM,OAAA,GAAU,gBAAA,CAAiB,QAAA,CAAS,GAAG,CAAA;AAC7C,UAAA,MAAM,OAAA,GAAU,gBAAA,CAAiB,QAAA,CAAS,GAAG,CAAA;AAE7C,UAAA,IAAI,WAAW,OAAA,EAAS;AACtB,YAAA,KAAA,CAAM,IAAA,CAAK;AAAA,cACT,QAAA,EAAU,QAAA;AAAA,cACV,QAAA,EAAe,KAAA,CAAA,QAAA,CAAS,IAAA,EAAM,GAAG,CAAA;AAAA,cACjC,SAAA,EAAW,GAAA;AAAA,cACX,MAAM,IAAA,CAAK,IAAA;AAAA,cACX,OAAA;AAAA,cACA;AAAA,aACD,CAAA;AAAA;AACH;AACF;AACF,aACO,KAAA,EAAO;AACd,MAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,mDAAA,EAAc,WAAW,CAAA,CAAA,CAAA,EAAK,KAAA,YAAiB,QAAQ,KAAA,CAAM,OAAA,GAAU,MAAA,CAAO,KAAK,CAAC,CAAA;AAAA;AACnG;AAGF,EAAA,aAAA,CAAc,OAAO,CAAA;AACrB,EAAA,OAAO,KAAA;AACT;AAKA,SAAS,YAAA,GAAwB;AAC/B,EAAA,IAAI;AACF,IAAA,QAAA,CAAS,kBAAA,EAAoB,EAAE,KAAA,EAAO,QAAA,EAAU,CAAA;AAChD,IAAA,OAAO,IAAA;AAAA,GACT,CAAA,MAAQ;AACN,IAAA,OAAO,KAAA;AAAA;AAEX;AAKA,SAAS,wBAAwB,QAAA,EAAiG;AAChI,EAAA,IAAI;AACF,IAAA,MAAM,OAAA,GAAU,mEAAmE,QAAQ,CAAA,CAAA,CAAA;AAC3F,IAAA,MAAM,SAAS,QAAA,CAAS,OAAA,EAAS,EAAE,QAAA,EAAU,SAAS,CAAA;AACtD,IAAA,MAAM,IAAA,GAAO,IAAA,CAAK,KAAA,CAAM,MAAM,CAAA;AAE9B,IAAA,IAAI,QAAA,GAAW,CAAA;AACf,IAAA,IAAI,KAAA,GAAQ,IAAA;AACZ,IAAA,IAAI,MAAA,GAAS,IAAA;AACb,IAAA,IAAI,QAAA,GAAW,KAAA;AAGf,IAAA,IAAI,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU;AACvC,MAAA,QAAA,GAAW,KAAK,KAAA,CAAM,UAAA,CAAW,KAAK,MAAA,CAAO,QAAQ,IAAI,GAAO,CAAA;AAAA;AAIlE,IAAA,IAAI,KAAK,OAAA,EAAS;AAChB,MAAA,KAAA,MAAW,MAAA,IAAU,KAAK,OAAA,EAAS;AACjC,QAAA,IAAI,MAAA,CAAO,eAAe,OAAA,EAAS;AACjC,UAAA,KAAA,GAAQ,OAAO,KAAA,IAAS,KAAA;AACxB,UAAA,MAAA,GAAS,OAAO,MAAA,IAAU,MAAA;AAAA,SAC5B,MAAA,IAAW,MAAA,CAAO,UAAA,KAAe,OAAA,EAAS;AACxC,UAAA,QAAA,GAAW,IAAA;AAAA;AACb;AACF;AAGF,IAAA,OAAO,EAAE,QAAA,EAAU,KAAA,EAAO,MAAA,EAAQ,QAAA,EAAS;AAAA,WACpC,KAAA,EAAO;AACd,IAAA,OAAA,CAAQ,IAAA,CAAK,CAAA,+DAAA,EAAgB,QAAQ,CAAA,CAAA,CAAA,EAAK,KAAA,YAAiB,QAAQ,KAAA,CAAM,OAAA,GAAU,MAAA,CAAO,KAAK,CAAC,CAAA;AAChG,IAAA,OAAO,IAAA;AAAA;AAEX;AAKA,SAAS,YAAA,CAAa,IAAA,EAAqB,UAAA,GAAsB,IAAA,EAAM;AAErE,EAAA,IAAI,UAAA,IAAc,cAAa,EAAG;AAChC,IAAA,MAAM,QAAA,GAAW,uBAAA,CAAwB,IAAA,CAAK,QAAQ,CAAA;AACtD,IAAA,IAAI,QAAA,EAAU;AACZ,MAAA,OAAO,QAAA;AAAA;AACT;AAIF,EAAA,MAAM,YAAA,GAAe,IAAA;AACrB,EAAA,MAAM,aAAA,GAAgB,IAAA;AAGtB,EAAA,MAAM,iBAAA,GAAoB,IAAA,CAAK,GAAA,CAAI,GAAA,EAAS,IAAA,CAAK,IAAI,GAAA,EAAU,IAAA,CAAK,IAAA,GAAO,GAAI,CAAC,CAAA;AAEhF,EAAA,OAAO;AAAA,IACL,QAAA,EAAU,IAAA,CAAK,KAAA,CAAM,iBAAiB,CAAA;AAAA,IACtC,KAAA,EAAO,YAAA;AAAA,IACP,MAAA,EAAQ,aAAA;AAAA,IACR,UAAU,IAAA,CAAK;AAAA;AAAA,GACjB;AACF;AAKA,SAAS,kBAAA,CAAmB,UAAA,EAA6B,UAAA,GAAsB,IAAA,EAA4B;AACzG,EAAA,OAAO,UAAA,CAAW,IAAI,CAAA,IAAA,KAAQ;AAC5B,IAAA,MAAM,IAAA,GAAO,YAAA,CAAa,IAAA,EAAM,UAAU,CAAA;AAE1C,IAAA,OAAO;AAAA,MACL,SAAA,EAAW,MAAA;AAAA,MACX,UAAA,EAAY,IAAA;AAAA,MACZ,YAAA,EAAc,EAAA;AAAA,MACd,WAAA,EAAa,EAAA;AAAA,MACb,aAAA,EAAe,EAAA;AAAA,MACf,UAAA,EAAY,KAAA;AAAA,MACZ,IAAA,EAAM;AAAA,QACJ,YAAA,EAAc,CAAA;AAAA,QACd,YAAA,EAAc,CAAA;AAAA,QACd,aAAA,EAAe,CAAA;AAAA,QACf,aAAA,EAAe,CAAA;AAAA,QACf,YAAA,EAAc,CAAA;AAAA,QACd,YAAA,EAAc,CAAA;AAAA,QACd,aAAA,EAAe,CAAA;AAAA,QACf,aAAA,EAAe;AAAA,OACjB;AAAA,MACA,UAAA,EAAY,MAAA;AAAA,MACZ,UAAA,EAAY,CAAA;AAAA,MACZ,UAAU,IAAA,CAAK,QAAA;AAAA,MACf,iBAAA,EAAmB,CAAA;AAAA,MACnB,UAAA,EAAY,EAAA;AAAA,MACZ,MAAA,EAAQ,IAAA;AAAA,MACR,WAAW,IAAA,CAAK,QAAA;AAAA,MAChB,QAAQ,IAAA,CAAK,MAAA;AAAA,MACb,EAAA,EAAI,YAAA,EAAa,CAAE,WAAA,EAAY;AAAA,MAC/B,sBAAA,EAAwB,EAAA;AAAA,MACxB,gBAAA,EAAkB,EAAA;AAAA,MAClB,sBAAA,EAAwB,KAAA;AAAA,MACxB,YAAA,EAAc,KAAA;AAAA,MACd,oBAAA,EAAsB,KAAA;AAAA,MACtB,sBAAA,EAAwB,KAAA;AAAA,MACxB,QAAA,EAAU,EAAA;AAAA,MACV,iBAAA,EAAmB,EAAA;AAAA,MACnB,WAAA,EAAa,EAAA;AAAA,MACb,aAAA,EAAe,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,SAAA;AAAA,MACpC,YAAA,EAAc,EAAA;AAAA,MACd,OAAA,EAAS;AAAA,QACP,IAAA,EAAM,CAAA;AAAA,QACN,mBAAA,EAAqB,KAAA;AAAA,QACrB,oBAAA,EAAsB,KAAA;AAAA,QACtB,iBAAiB,EAAC;AAAA,QAClB,IAAA,EAAM,EAAA;AAAA,QACN,SAAS;AAAC,OACZ;AAAA,MACA,UAAA,EAAY,EAAA;AAAA,MACZ,aAAA,EAAe,IAAA;AAAA,MACf,kBAAA,EAAoB,EAAA;AAAA,MACpB,MAAM,IAAA,CAAK,QAAA;AAAA,MACX,YAAA,EAAc,MAAA;AAAA,MACd,uBAAA,EAAyB,EAAA;AAAA,MACzB,yBAAA,EAA2B,EAAA;AAAA,MAC3B,UAAA,EAAY,EAAA;AAAA,MACZ,wBAAA,EAA0B,EAAA;AAAA,MAC1B,YAAA,EAAc,EAAA;AAAA,MACd,YAAA,EAAc,IAAA;AAAA,MACd,MAAA,EAAQ,CAAA;AAAA,MACR,eAAA,EAAiB,CAAA;AAAA,MACjB,MAAA,EAAQ;AAAA,QACN,WAAA,EAAa,EAAA;AAAA,QACb,YAAA,EAAc,CAAA;AAAA,QACd,UAAA,EAAY;AAAA,UACV,UAAU,IAAA,CAAK,QAAA;AAAA,UACf,KAAA,EAAO;AAAA;AACT,OACF;AAAA,MACA,OAAA,EAAS,EAAA;AAAA,MACT,IAAA,EAAM,OAAA;AAAA,MACN,eAAA,EAAiB;AAAA,QACf,YAAY,EAAC;AAAA,QACb,SAAA,EAAW,IAAA;AAAA,QACX,kBAAA,EAAoB,IAAA;AAAA,QACpB,eAAA,EAAiB,IAAA;AAAA,QACjB,IAAA,EAAM,EAAA;AAAA,QACN,UAAA,EAAY;AAAA,OACd;AAAA,MACA,OAAO,IAAA,CAAK;AAAA,KACd;AAAA,GACD,CAAA;AACH;AAKA,SAAS,kBAAA,CAAmB,UAAA,EAA6B,UAAA,GAAsB,IAAA,EAA4B;AACzG,EAAA,OAAO,UAAA,CAAW,IAAI,CAAA,IAAA,KAAQ;AAC5B,IAAA,MAAM,IAAA,GAAO,YAAA,CAAa,IAAA,EAAM,UAAU,CAAA;AAE1C,IAAA,OAAO;AAAA,MACL,MAAA,EAAQ,CAAA;AAAA,MACR,WAAA,EAAa,EAAA;AAAA,MACb,aAAA,EAAe,EAAA;AAAA,MACf,UAAA,EAAY,CAAA;AAAA,MACZ,oBAAA,EAAsB,MAAA;AAAA,MACtB,UAAU,IAAA,CAAK,QAAA;AAAA,MACf,SAAA,EAAW,EAAA;AAAA,MACX,UAAA,EAAY,EAAA;AAAA,MACZ,EAAA,EAAI,YAAA,EAAa,CAAE,WAAA,EAAY;AAAA,MAC/B,gBAAA,EAAkB,EAAA;AAAA,MAClB,gBAAA,EAAkB,KAAA;AAAA,MAClB,oBAAA,EAAsB,KAAA;AAAA,MACtB,MAAA,EAAQ,KAAA;AAAA,MACR,iBAAA,EAAmB,EAAA;AAAA,MACnB,QAAA,EAAU,EAAA;AAAA,MACV,MAAM,IAAA,CAAK,QAAA;AAAA,MACX,MAAM,IAAA,CAAK,QAAA;AAAA,MACX,KAAA,EAAO,EAAA;AAAA,MACP,UAAA,EAAY,EAAA;AAAA,MACZ,WAAA,EAAa,EAAA;AAAA,MACb,SAAA,EAAW,EAAA;AAAA,MACX,WAAA,EAAa,EAAA;AAAA,MACb,eAAA,EAAiB,CAAA;AAAA,MACjB,OAAA,EAAS,EAAA;AAAA,MACT,OAAA,EAAS,EAAA;AAAA,MACT,gBAAA,EAAkB,EAAA;AAAA,MAClB,kBAAA,EAAoB,EAAA;AAAA,MACpB,cAAA,EAAgB,EAAA;AAAA,MAChB,gBAAA,EAAkB,EAAA;AAAA,MAClB,aAAA,EAAe,EAAA;AAAA,MACf,uBAAA,EAAyB,EAAA;AAAA,MACzB,yBAAA,EAA2B,EAAA;AAAA,MAC3B,YAAA,EAAc,EAAA;AAAA,MACd,SAAA,EAAW,EAAA;AAAA,MACX,IAAA,EAAM,IAAA,CAAK,OAAA,GAAU,sBAAA,GAAyB,OAAA;AAAA,MAC9C,QAAA,EAAU,EAAA;AAAA,MACV,aAAa;AAAC,KAChB;AAAA,GACD,CAAA;AACH;AAKA,SAAS,cAAA,CAAe,YAAkC,UAAA,EAAoD;AAC5G,EAAA,MAAM,SAA2B,EAAC;AAGlC,EAAA,IAAI,UAAA,CAAW,SAAS,CAAA,EAAG;AACzB,IAAA,IAAI,WAAA,GAAc,CAAA;AAClB,IAAA,MAAM,WAAoC,EAAC;AAE3C,IAAA,KAAA,MAAW,QAAQ,UAAA,EAAY;AAC7B,MAAA,QAAA,CAAS,IAAA,CAAK;AAAA,QACZ,IAAA,EAAM;AAAA,UACJ,KAAA,EAAO,CAAA;AAAA,UACP,IAAA,EAAM;AAAA,YACJ,UAAA,EAAY,KAAA;AAAA,YACZ,QAAA,EAAU;AAAA,WACZ;AAAA,UACA,QAAA,EAAU,CAAA;AAAA,UACV,KAAA,EAAO;AAAA,YACL,CAAA,EAAG,CAAA;AAAA,YACH,CAAA,EAAG;AAAA,WACL;AAAA,UACA,SAAA,EAAW;AAAA,YACT,CAAA,EAAG,CAAA;AAAA,YACH,CAAA,EAAG;AAAA;AACL,SACF;AAAA,QACA,aAAa,IAAA,CAAK,EAAA;AAAA,QAClB,gBAAA,EAAkB;AAAA,UAChB,UAAU,IAAA,CAAK,QAAA;AAAA,UACf,KAAA,EAAO;AAAA,SACT;AAAA,QACA,gBAAA,EAAkB;AAAA,UAChB,UAAU,IAAA,CAAK,QAAA;AAAA,UACf,KAAA,EAAO;AAAA;AACT,OACD,CAAA;AAED,MAAA,WAAA,IAAe,IAAA,CAAK,QAAA;AAAA;AAGtB,IAAA,MAAA,CAAO,IAAA,CAAK;AAAA,MACV,SAAA,EAAW,CAAA;AAAA,MACX,IAAA,EAAM,CAAA;AAAA,MACN,EAAA,EAAI,YAAA,EAAa,CAAE,WAAA,EAAY;AAAA,MAC/B,QAAA;AAAA,MACA,IAAA,EAAM;AAAA,KACP,CAAA;AAAA;AAIH,EAAA,IAAI,UAAA,CAAW,SAAS,CAAA,EAAG;AACzB,IAAA,IAAI,WAAA,GAAc,CAAA;AAClB,IAAA,MAAM,WAAoC,EAAC;AAE3C,IAAA,KAAA,MAAW,QAAQ,UAAA,EAAY;AAC7B,MAAA,QAAA,CAAS,IAAA,CAAK;AAAA,QACZ,aAAa,IAAA,CAAK,EAAA;AAAA,QAClB,gBAAA,EAAkB;AAAA,UAChB,UAAU,IAAA,CAAK,QAAA;AAAA,UACf,KAAA,EAAO;AAAA,SACT;AAAA,QACA,gBAAA,EAAkB;AAAA,UAChB,UAAU,IAAA,CAAK,QAAA;AAAA,UACf,KAAA,EAAO;AAAA;AACT,OACD,CAAA;AAED,MAAA,WAAA,IAAe,IAAA,CAAK,QAAA;AAAA;AAGtB,IAAA,MAAA,CAAO,IAAA,CAAK;AAAA,MACV,SAAA,EAAW,CAAA;AAAA,MACX,IAAA,EAAM,CAAA;AAAA,MACN,EAAA,EAAI,YAAA,EAAa,CAAE,WAAA,EAAY;AAAA,MAC/B,QAAA;AAAA,MACA,IAAA,EAAM;AAAA,KACP,CAAA;AAAA;AAGH,EAAA,OAAO,MAAA;AACT;AAKA,SAAS,aAAA,CAAc,OAAwB,OAAA,EAM5B;AACjB,EAAA,MAAM,UAAA,GAAa,KAAA,CAAM,MAAA,CAAO,CAAA,CAAA,KAAK,EAAE,OAAO,CAAA;AAC9C,EAAA,MAAM,UAAA,GAAa,KAAA,CAAM,MAAA,CAAO,CAAA,CAAA,KAAK,EAAE,OAAO,CAAA;AAE9C,EAAA,MAAM,UAAA,GAAa,QAAQ,UAAA,KAAe,KAAA;AAC1C,EAAA,MAAM,UAAA,GAAa,kBAAA,CAAmB,UAAA,EAAY,UAAU,CAAA;AAC5D,EAAA,MAAM,UAAA,GAAa,kBAAA,CAAmB,UAAA,EAAY,UAAU,CAAA;AAC5D,EAAA,MAAM,MAAA,GAAS,cAAA,CAAe,UAAA,EAAY,UAAU,CAAA;AAGpD,EAAA,MAAM,gBAAgB,IAAA,CAAK,GAAA;AAAA,IACzB,GAAG,MAAA,CAAO,GAAA;AAAA,MAAI,WACZ,KAAA,CAAM,QAAA,CAAS,MAAA,CAAO,CAAC,KAAK,GAAA,KAAQ,IAAA,CAAK,GAAA,CAAI,GAAA,EAAK,IAAI,gBAAA,CAAiB,KAAA,GAAQ,IAAI,gBAAA,CAAiB,QAAQ,GAAG,CAAC;AAAA,KAClH;AAAA,IACA;AAAA,GACF;AAEA,EAAA,MAAM,WAAA,GAAc,QAAQ,WAAA,IAAe,IAAA;AAC3C,EAAA,MAAM,YAAA,GAAe,QAAQ,YAAA,IAAgB,IAAA;AAC7C,EAAA,MAAM,KAAA,GAAQ,CAAA,EAAG,WAAW,CAAA,CAAA,EAAI,YAAY,CAAA,CAAA;AAC5C,EAAA,MAAM,QAAA,GAAW,iBAAA,GAAoB,IAAA,CAAK,GAAA,EAAI;AAC9C,EAAA,MAAM,WAAA,GAAc,IAAA,CAAK,GAAA,EAAI,GAAI,GAAA;AAEjC,EAAA,OAAO;AAAA,IACL,aAAA,EAAe;AAAA,MACb,MAAA,EAAQ,YAAA;AAAA,MACR,KAAA;AAAA,MACA,KAAA,EAAO;AAAA,KACT;AAAA,IACA,WAAA,EAAa,CAAA;AAAA,IACb,MAAA,EAAQ;AAAA,MACN,gBAAA,EAAkB,CAAA;AAAA,MAClB,iBAAiB,EAAC;AAAA,MAClB,qBAAA,EAAuB,CAAA;AAAA,MACvB,YAAA,EAAc,IAAA;AAAA,MACd,wBAAA,EAA0B,CAAA;AAAA,MAC1B,qBAAA,EAAuB,EAAA;AAAA,MACvB,WAAA,EAAa,IAAA;AAAA,MACb,iBAAiB,EAAC;AAAA,MAClB,gBAAA,EAAkB,IAAA;AAAA,MAClB,kBAAA,EAAoB,CAAA;AAAA,MACpB,sBAAA,EAAwB,MAAA;AAAA,MACxB,qBAAqB,EAAC;AAAA,MACtB,mBAAA,EAAqB,MAAA;AAAA,MACrB,mBAAA,EAAqB,MAAA;AAAA,MACrB,yBAAA,EAA2B,CAAA;AAAA,MAC3B,uBAAA,EAAyB,CAAA;AAAA,MACzB,iBAAA,EAAmB,CAAA;AAAA,MACnB,wBAAA,EAA0B,IAAA;AAAA,MAC1B,uBAAA,EAAyB,EAAA;AAAA,MACzB,aAAA,EAAe,IAAA;AAAA,MACf,mBAAmB,EAAC;AAAA,MACpB,kBAAkB,EAAC;AAAA,MACnB,mBAAA,EAAqB,EAAA;AAAA,MACrB,SAAA,EAAW,IAAA;AAAA,MACX,eAAe,EAAC;AAAA,MAChB,oBAAA,EAAsB,EAAA;AAAA,MACtB,UAAA,EAAY,IAAA;AAAA,MACZ,gBAAgB;AAAC,KACnB;AAAA,IACA,WAAA,EAAa,WAAA;AAAA,IACb,eAAA,EAAiB,EAAA;AAAA,IACjB,QAAA,EAAU,YAAA,EAAa,CAAE,WAAA,EAAY;AAAA,IACrC,UAAA,EAAY,QAAQ,WAAA,IAAe,wBAAA;AAAA,IACnC,8BAAA,EAAgC,EAAA;AAAA,IAChC,QAAA,EAAU,aAAA;AAAA,IACV,GAAA,EAAK,QAAQ,GAAA,IAAO,EAAA;AAAA,IACpB,EAAA,EAAI,YAAA,EAAa,CAAE,WAAA,EAAY;AAAA,IAC/B,sBAAA,EAAwB;AAAA,MACtB,MAAA,EAAQ,IAAA;AAAA,MACR,UAAA,EAAY,gBAAA;AAAA,MACZ,WAAA,EAAa,OAAA;AAAA,MACb,SAAA,EAAW,QAAA;AAAA,MACX,YAAA,EAAc,EAAA;AAAA,MACd,WAAA,EAAa,EAAA;AAAA,MACb,UAAA,EAAY,MAAA;AAAA,MACZ,QAAA,EAAU,MAAA;AAAA,MACV,aAAA,EAAe,IAAA;AAAA,MACf,YAAA,EAAc;AAAA,KAChB;AAAA,IACA,SAAA,EAAW;AAAA,MACT,eAAe,EAAC;AAAA,MAChB,aAAa,EAAC;AAAA,MACd,qBAAqB,EAAC;AAAA,MACtB,MAAA,EAAQ,UAAA;AAAA,MACR,OAAO,EAAC;AAAA,MACR,UAAU,EAAC;AAAA,MACX,SAAS,EAAC;AAAA,MACV,cAAc,EAAC;AAAA,MACf,cAAc,EAAC;AAAA,MACf,SAAS,EAAC;AAAA,MACV,SAAS,EAAC;AAAA,MACV,YAAY,EAAC;AAAA,MACb,KAAK,EAAC;AAAA,MACN,QAAQ,EAAC;AAAA,MACT,WAAW,EAAC;AAAA,MACZ,OAAO,EAAC;AAAA,MACR,qBAAqB,EAAC;AAAA,MACtB,cAAc,EAAC;AAAA,MACf,gBAAgB,EAAC;AAAA,MACjB,QAAQ,EAAC;AAAA,MACT,QAAQ,EAAC;AAAA,MACT,UAAU,EAAC;AAAA,MACX,OAAO,EAAC;AAAA,MACR,MAAA,EAAQ;AAAA,KACV;AAAA,IACA,cAAA,EAAgB,IAAA;AAAA,IAChB,IAAA,EAAM,QAAQ,WAAA,IAAe,EAAA;AAAA,IAC7B,WAAA,EAAa,SAAA;AAAA,IACb,QAAA,EAAU;AAAA,MACR,MAAA,EAAQ,IAAA;AAAA,MACR,UAAA,EAAY,gBAAA;AAAA,MACZ,WAAA,EAAa,OAAA;AAAA,MACb,SAAA,EAAW,QAAA;AAAA,MACX,YAAA,EAAc,EAAA;AAAA,MACd,WAAA,EAAa,EAAA;AAAA,MACb,UAAA,EAAY,MAAA;AAAA,MACZ,QAAA,EAAU,MAAA;AAAA,MACV,aAAA,EAAe,IAAA;AAAA,MACf,YAAA,EAAc;AAAA,KAChB;AAAA,IACA,eAAe,EAAC;AAAA,IAChB,8BAAA,EAAgC,IAAA;AAAA,IAChC,MAAA,EAAQ,gBAAA;AAAA,IACR,MAAA;AAAA,IACA,WAAA,EAAa,WAAA;AAAA,IACb,OAAA,EAAS;AAAA,GACX;AACF;AAKA,SAASA,KAAAA,GAAa;AACpB,EAAA,MAAM,IAAA,GAAO,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA;AAEjC,EAAA,IAAI,IAAA,CAAK,WAAW,CAAA,EAAG;AACrB,IAAA,OAAA,CAAQ,IAAI,2BAAO,CAAA;AACnB,IAAA,OAAA,CAAQ,IAAI,8GAAkD,CAAA;AAC9D,IAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AACd,IAAA,OAAA,CAAQ,IAAI,eAAK,CAAA;AACjB,IAAA,OAAA,CAAQ,IAAI,sCAAsC,CAAA;AAClD,IAAA,OAAA,CAAQ,IAAI,6DAA6D,CAAA;AACzE,IAAA,OAAA,CAAQ,IAAI,uFAAuF,CAAA;AACnG,IAAA,OAAA,CAAQ,IAAI,EAAE,CAAA;AACd,IAAA,OAAA,CAAQ,IAAI,eAAK,CAAA;AACjB,IAAA,OAAA,CAAQ,IAAI,4EAAoC,CAAA;AAChD,IAAA,OAAA,CAAQ,IAAI,4EAAoC,CAAA;AAChD,IAAA,OAAA,CAAQ,IAAI,8DAAgC,CAAA;AAC5C,IAAA,OAAA,CAAQ,IAAI,2DAAwB,CAAA;AACpC,IAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAGhB,EAAA,MAAM,OAAA,GAAU,KAAK,CAAC,CAAA;AACtB,EAAA,MAAM,UAAA,GAAa,IAAA,CAAK,CAAC,CAAA,IAAK,gCAAA;AAG9B,EAAA,MAAM,UAAe,EAAC;AACtB,EAAA,KAAA,IAAS,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,IAAA,CAAK,QAAQ,CAAA,EAAA,EAAK;AACpC,IAAA,MAAM,GAAA,GAAM,KAAK,CAAC,CAAA;AAClB,IAAA,IAAI,GAAA,CAAI,UAAA,CAAW,UAAU,CAAA,EAAG;AAC9B,MAAA,OAAA,CAAQ,cAAc,QAAA,CAAS,GAAA,CAAI,MAAM,GAAG,CAAA,CAAE,CAAC,CAAC,CAAA;AAAA,KAClD,MAAA,IAAW,GAAA,CAAI,UAAA,CAAW,WAAW,CAAA,EAAG;AACtC,MAAA,OAAA,CAAQ,eAAe,QAAA,CAAS,GAAA,CAAI,MAAM,GAAG,CAAA,CAAE,CAAC,CAAC,CAAA;AAAA,KACnD,MAAA,IAAW,GAAA,CAAI,UAAA,CAAW,QAAQ,CAAA,EAAG;AACnC,MAAA,OAAA,CAAQ,MAAM,QAAA,CAAS,GAAA,CAAI,MAAM,GAAG,CAAA,CAAE,CAAC,CAAC,CAAA;AAAA,KAC1C,MAAA,IAAW,GAAA,CAAI,UAAA,CAAW,SAAS,CAAA,EAAG;AACpC,MAAA,OAAA,CAAQ,WAAA,GAAc,GAAA,CAAI,KAAA,CAAM,GAAG,EAAE,CAAC,CAAA;AAAA;AACxC;AAGF,EAAA,IAAI;AACF,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,gDAAA,EAAc,OAAO,CAAA,CAAE,CAAA;AAEnC,IAAA,IAAI,CAAI,GAAA,CAAA,UAAA,CAAW,OAAO,CAAA,EAAG;AAC3B,MAAA,MAAM,IAAI,KAAA,CAAM,CAAA,gCAAA,EAAU,OAAO,CAAA,CAAE,CAAA;AAAA;AAGrC,IAAA,MAAM,KAAA,GAAQ,cAAc,OAAO,CAAA;AAEnC,IAAA,IAAI,KAAA,CAAM,WAAW,CAAA,EAAG;AACtB,MAAA,OAAA,CAAQ,IAAI,+DAAa,CAAA;AACzB,MAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAGhB,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,uBAAA,EAAS,KAAA,CAAM,MAAM,CAAA,gCAAA,CAAS,CAAA;AAC1C,IAAA,KAAA,CAAM,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAA,KAAU;AAC7B,MAAA,MAAM,IAAA,GAAO,IAAA,CAAK,OAAA,GAAU,WAAA,GAAO,WAAA;AACnC,MAAA,OAAA,CAAQ,GAAA,CAAI,KAAK,KAAA,GAAQ,CAAC,KAAK,IAAI,CAAA,CAAA,EAAI,KAAK,QAAQ,CAAA,EAAG,KAAK,SAAS,CAAA,EAAA,EAAA,CAAM,KAAK,IAAA,GAAO,IAAA,GAAO,MAAM,OAAA,CAAQ,CAAC,CAAC,CAAA,IAAA,CAAM,CAAA;AAAA,KACrH,CAAA;AAED,IAAA,OAAA,CAAQ,IAAI,iEAAkB,CAAA;AAC9B,IAAA,MAAM,KAAA,GAAQ,aAAA,CAAc,KAAA,EAAO,OAAO,CAAA;AAG1C,IAAA,MAAM,WAAA,GAAc,IAAA,CAAK,SAAA,CAAU,KAAA,EAAO,MAAM,CAAC,CAAA;AACjD,IAAG,GAAA,CAAA,aAAA,CAAc,UAAA,EAAY,WAAA,EAAa,OAAO,CAAA;AAEjD,IAAA,OAAA,CAAQ,GAAA,CAAI;AAAA,wDAAA,CAAe,CAAA;AAC3B,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,oCAAA,EAAY,UAAU,CAAA,CAAE,CAAA;AACpC,IAAA,OAAA,CAAQ,IAAI,CAAA,mCAAA,CAAU,CAAA;AACtB,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,mBAAA,EAAY,KAAA,CAAM,EAAE,CAAA,CAAE,CAAA;AAClC,IAAA,OAAA,CAAQ,GAAA,CAAI,2BAAY,KAAA,CAAM,QAAA,GAAW,KAAS,OAAA,CAAQ,CAAC,CAAC,CAAA,MAAA,CAAG,CAAA;AAC/D,IAAA,OAAA,CAAQ,GAAA,CAAI,gCAAY,KAAA,CAAM,aAAA,CAAc,KAAK,CAAA,CAAA,EAAI,KAAA,CAAM,aAAA,CAAc,MAAM,CAAA,CAAE,CAAA;AACjF,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,iBAAA,EAAU,KAAA,CAAM,GAAG,CAAA,IAAA,CAAM,CAAA;AACrC,IAAA,OAAA,CAAQ,IAAI,CAAA,6BAAA,EAAY,KAAA,CAAM,SAAA,CAAU,MAAA,CAAO,MAAM,CAAA,MAAA,CAAG,CAAA;AACxD,IAAA,OAAA,CAAQ,IAAI,CAAA,6BAAA,EAAY,KAAA,CAAM,SAAA,CAAU,MAAA,CAAO,MAAM,CAAA,MAAA,CAAG,CAAA;AACxD,IAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,6BAAA,EAAY,KAAA,CAAM,MAAA,CAAO,MAAM,CAAA,MAAA,CAAG,CAAA;AAE9C,IAAA,OAAA,CAAQ,IAAI,4HAA2B,CAAA;AACvC,IAAA,OAAA,CAAQ,IAAI,oHAA+B,CAAA;AAAA,WAEpC,KAAA,EAAO;AACd,IAAA,OAAA,CAAQ,KAAA,CAAM,oCAAW,KAAA,YAAiB,KAAA,GAAQ,MAAM,OAAA,GAAU,MAAA,CAAO,KAAK,CAAC,CAAA;AAC/E,IAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAElB;AAGA,IAAI,SAAA,CAAQ,SAAS,MAAA,EAAQ;AAC3B,EAAAA,KAAAA,EAAK;AACP;;;ACtyBO,IAAM,aAAA,GAAgB;AAAA;AAAA,EAE3B,qBAAA,EAAuB,CAAC,YAAA,KAAiC,YAAA,GAAe,GAAA;AAAA,EACxE,qBAAA,EAAuB,CAAC,OAAA,KAA4B,OAAA,GAAU,GAAA;AAAA;AAAA,EAG9D,oBAAA,EAAsB,CAAC,KAAA,EAAe,MAAA,KAA2B;AAC/D,IAAA,MAAM,GAAA,GAAM,CAAC,CAAA,EAAW,CAAA,KAAsB,CAAA,KAAM,IAAI,CAAA,GAAI,GAAA,CAAI,CAAA,EAAG,CAAA,GAAI,CAAC,CAAA;AACxE,IAAA,MAAM,OAAA,GAAU,GAAA,CAAI,KAAA,EAAO,MAAM,CAAA;AACjC,IAAA,OAAO,CAAA,EAAG,KAAA,GAAQ,OAAO,CAAA,CAAA,EAAI,SAAS,OAAO,CAAA,CAAA;AAAA,GAC/C;AAAA;AAAA,EAGA,gBAAA,EAAkB,CAAC,MAAA,EAAQ,MAAA,EAAQ,QAAQ,MAAA,EAAQ,MAAA,EAAQ,MAAA,EAAQ,OAAA,EAAS,MAAM,CAAA;AAAA,EAClF,kBAAkB,CAAC,MAAA,EAAQ,QAAQ,MAAA,EAAQ,MAAA,EAAQ,SAAS,MAAM,CAAA;AAAA;AAAA,EAGlE,WAAA,EAAa,CAAC,QAAA,KAA8B;AAC1C,IAAA,MAAM,GAAA,GAAM,SAAS,WAAA,EAAY,CAAE,UAAU,QAAA,CAAS,WAAA,CAAY,GAAG,CAAC,CAAA;AACtE,IAAA,OAAO,aAAA,CAAc,gBAAA,CAAiB,QAAA,CAAS,GAAG,CAAA;AAAA,GACpD;AAAA,EAEA,WAAA,EAAa,CAAC,QAAA,KAA8B;AAC1C,IAAA,MAAM,GAAA,GAAM,SAAS,WAAA,EAAY,CAAE,UAAU,QAAA,CAAS,WAAA,CAAY,GAAG,CAAC,CAAA;AACtE,IAAA,OAAO,aAAA,CAAc,gBAAA,CAAiB,QAAA,CAAS,GAAG,CAAA;AAAA,GACpD;AAAA,EAEA,WAAA,EAAa,CAAC,QAAA,KAA8B;AAC1C,IAAA,OAAO,cAAc,WAAA,CAAY,QAAQ,CAAA,IAAK,aAAA,CAAc,YAAY,QAAQ,CAAA;AAAA;AAEpF;AAGO,IAAM,kBAAA,GAAqB;AAAA;AAAA,EAEhC,WAAA,EAAa,EAAA;AAAA,EACb,oBAAA,EAAsB,IAAA;AAAA,EACtB,qBAAA,EAAuB,IAAA;AAAA,EACvB,oBAAA,EAAsB,MAAA;AAAA;AAAA,EAGtB,uBAAA,EAAyB,GAAA;AAAA;AAAA,EAGzB,kBAAA,EAAoB,CAAC,QAAA,EAAU,SAAS,CAAA;AAAA,EACxC,eAAA,EAAiB,QAAA;AAAA;AAAA,EAGjB,aAAA,EAAe;AAAA,IACb,MAAA,EAAQ,IAAA;AAAA,IACR,UAAA,EAAY,gBAAA;AAAA,IACZ,QAAA,EAAU;AAAA;AAEd", "file": "index.mjs", "sourcesContent": ["#!/usr/bin/env ts-node\n\n/**\n * 剪映草稿文件解析器\n * 解析 draft_content.json 文件，提取视频的详细信息\n */\n\nimport * as fs from 'fs';\n\n// 类型定义\ninterface CanvasConfig {\n  height: number;\n  width: number;\n  ratio: string;\n}\n\ninterface CropInfo {\n  lower_left_x: number;\n  lower_left_y: number;\n  lower_right_x: number;\n  lower_right_y: number;\n  upper_left_x: number;\n  upper_left_y: number;\n  upper_right_x: number;\n  upper_right_y: number;\n}\n\ninterface VideoClip {\n  id: string;\n  material_name: string;\n  path: string;\n  duration: number;\n  width: number;\n  height: number;\n  has_audio: boolean;\n  crop: CropInfo;\n  crop_ratio: string;\n  crop_scale: number;\n  type: string;\n  source: number;\n  is_ai_generate_content: boolean;\n  is_copyright: boolean;\n}\n\ninterface AudioClip {\n  id: string;\n  name: string;\n  path: string;\n  duration: number;\n  type: string;\n  source_platform: number;\n  is_ai_clone_tone: boolean;\n  is_text_edit_overdub: boolean;\n}\n\ninterface TrackSegment {\n  clip?: {\n    alpha: number;\n    flip: {\n      horizontal: boolean;\n      vertical: boolean;\n    };\n    rotation: number;\n    scale: {\n      x: number;\n      y: number;\n    };\n    transform: {\n      x: number;\n      y: number;\n    };\n  };\n  material_id: string;\n  target_timerange: {\n    duration: number;\n    start: number;\n  };\n  source_timerange?: {\n    duration: number;\n    start: number;\n  } | null;\n}\n\ninterface Track {\n  id: string;\n  name: string;\n  attribute: number;\n  flag: number;\n  is_default_name: boolean;\n  segments: TrackSegment[];\n}\n\ninterface JianyingDraft {\n  id: string;\n  duration: number;\n  fps: number;\n  canvas_config: CanvasConfig;\n  color_space: number;\n  create_time: number;\n  last_modified_platform: {\n    app_id: number;\n    app_source: string;\n    app_version: string;\n    device_id: string;\n    os: string;\n    platform: string;\n  };\n  materials: {\n    videos: VideoClip[];\n    audios: AudioClip[];\n  };\n  tracks: Track[];\n}\n\ninterface Transform {\n  position: { x: number; y: number };\n  rotation: number;\n  scale: { x: number; y: number };\n}\n\ninterface TimeRange {\n  start: number;\n  duration: number;\n  durationSeconds: number;\n}\n\ninterface AppInfo {\n  appId: string;\n  version: string;\n  platform: string;\n}\n\ninterface Statistics {\n  totalVideoClips: number;\n  totalAudioClips: number;\n  totalTracks: number;\n  totalSegments: number;\n  uniqueVideoFiles: number;\n  uniqueAudioFiles: number;\n}\n\ninterface VideoInfo {\n  // 项目基本信息\n  projectId: string;\n  projectDuration: number; // 微秒\n  projectDurationSeconds: number; // 秒\n  fps: number;\n  canvasSize: {\n    width: number;\n    height: number;\n    ratio: string;\n  };\n  \n  // 视频素材信息\n  videoClips: Array<{\n    id: string;\n    fileName: string;\n    filePath: string;\n    duration: number; // 微秒\n    durationSeconds: number; // 秒\n    resolution: {\n      width: number;\n      height: number;\n      aspectRatio: string;\n    };\n    hasAudio: boolean;\n    cropInfo: {\n      ratio: string;\n      scale: number;\n      coordinates: CropInfo;\n    };\n    isAIGenerated: boolean;\n    isCopyrighted: boolean;\n  }>;\n  \n  // 音频素材信息\n  audioClips: Array<{\n    id: string;\n    name: string;\n    filePath: string;\n    duration: number; // 微秒\n    durationSeconds: number; // 秒\n    type: string;\n    isAIClone: boolean;\n    isTextOverdub: boolean;\n  }>;\n  \n  // 轨道信息\n  tracks: Array<{\n    id: string;\n    name: string;\n    type: string; // video, audio, etc.\n    segmentCount: number;\n    segments: Array<{\n      materialId: string;\n      timeRange: {\n        start: number; // 微秒\n        duration: number; // 微秒\n        startSeconds: number; // 秒\n        durationSeconds: number; // 秒\n      };\n      sourceTimeRange: {\n        start: number; // 微秒\n        duration: number; // 微秒\n        startSeconds: number; // 秒\n        durationSeconds: number; // 秒\n      };\n      transform: {\n        alpha: number;\n        rotation: number;\n        scale: { x: number; y: number };\n        position: { x: number; y: number };\n        flip: { horizontal: boolean; vertical: boolean };\n      };\n    }>;\n  }>;\n  \n  // 应用信息\n  appInfo: {\n    appId: number;\n    appSource: string;\n    appVersion: string;\n    platform: string;\n    os: string;\n    deviceId: string;\n  };\n  \n  // 统计信息\n  statistics: {\n    totalVideoClips: number;\n    totalAudioClips: number;\n    totalTracks: number;\n    totalSegments: number;\n    uniqueVideoFiles: string[];\n    uniqueAudioFiles: string[];\n  };\n}\n\n/**\n * 微秒转换为秒\n */\nfunction microsecondsToSeconds(microseconds: number): number {\n  return Math.round((microseconds / 1000000) * 100) / 100;\n}\n\n/**\n * 计算宽高比\n */\nfunction calculateAspectRatio(width: number, height: number): string {\n  const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);\n  const divisor = gcd(width, height);\n  return `${width / divisor}:${height / divisor}`;\n}\n\n/**\n * 解析剪映草稿文件\n */\nfunction parseJianyingDraft(filePath: string): VideoInfo {\n  if (!fs.existsSync(filePath)) {\n    throw new Error(`文件不存在: ${filePath}`);\n  }\n\n  const content = fs.readFileSync(filePath, 'utf-8');\n  const draft: JianyingDraft = JSON.parse(content);\n\n  // 解析视频素材\n  const videoClips = draft.materials.videos.map(video => ({\n    id: video.id,\n    fileName: video.material_name,\n    filePath: video.path,\n    duration: video.duration,\n    durationSeconds: microsecondsToSeconds(video.duration),\n    resolution: {\n      width: video.width,\n      height: video.height,\n      aspectRatio: calculateAspectRatio(video.width, video.height)\n    },\n    hasAudio: video.has_audio,\n    cropInfo: {\n      ratio: video.crop_ratio,\n      scale: video.crop_scale,\n      coordinates: video.crop\n    },\n    isAIGenerated: video.is_ai_generate_content,\n    isCopyrighted: video.is_copyright\n  }));\n\n  // 解析音频素材\n  const audioClips = draft.materials.audios.map(audio => ({\n    id: audio.id,\n    name: audio.name,\n    filePath: audio.path,\n    duration: audio.duration,\n    durationSeconds: microsecondsToSeconds(audio.duration),\n    type: audio.type,\n    isAIClone: audio.is_ai_clone_tone,\n    isTextOverdub: audio.is_text_edit_overdub\n  }));\n\n  // 解析轨道信息\n  const tracks = draft.tracks.map(track => {\n    const segments = track.segments.map(segment => ({\n      materialId: segment.material_id,\n      timeRange: {\n        start: segment.target_timerange.start,\n        duration: segment.target_timerange.duration,\n        startSeconds: microsecondsToSeconds(segment.target_timerange.start),\n        durationSeconds: microsecondsToSeconds(segment.target_timerange.duration)\n      },\n      sourceTimeRange: segment.source_timerange ? {\n        start: segment.source_timerange.start,\n        duration: segment.source_timerange.duration,\n        startSeconds: microsecondsToSeconds(segment.source_timerange.start),\n        durationSeconds: microsecondsToSeconds(segment.source_timerange.duration)\n      } : {\n        start: 0,\n        duration: 0,\n        startSeconds: 0,\n        durationSeconds: 0\n      },\n      transform: segment.clip ? {\n        alpha: segment.clip.alpha,\n        rotation: segment.clip.rotation,\n        scale: segment.clip.scale,\n        position: segment.clip.transform,\n        flip: segment.clip.flip\n      } : {\n        alpha: 1.0,\n        rotation: 0.0,\n        scale: { x: 1.0, y: 1.0 },\n        position: { x: 0.0, y: 0.0 },\n        flip: { horizontal: false, vertical: false }\n      }\n    }));\n\n    return {\n      id: track.id,\n      name: track.name || `轨道 ${track.attribute}`,\n      type: track.attribute === 0 ? 'video' : 'audio',\n      segmentCount: segments.length,\n      segments\n    };\n  });\n\n  // 统计信息\n  const uniqueVideoFiles = [...new Set(videoClips.map(v => v.filePath))];\n  const uniqueAudioFiles = [...new Set(audioClips.map(a => a.filePath))];\n  const totalSegments = tracks.reduce((sum, track) => sum + track.segmentCount, 0);\n\n  return {\n    projectId: draft.id,\n    projectDuration: draft.duration,\n    projectDurationSeconds: microsecondsToSeconds(draft.duration),\n    fps: draft.fps,\n    canvasSize: {\n      width: draft.canvas_config.width,\n      height: draft.canvas_config.height,\n      ratio: draft.canvas_config.ratio\n    },\n    videoClips,\n    audioClips,\n    tracks,\n    appInfo: {\n      appId: draft.last_modified_platform.app_id,\n      appSource: draft.last_modified_platform.app_source,\n      appVersion: draft.last_modified_platform.app_version,\n      platform: draft.last_modified_platform.platform,\n      os: draft.last_modified_platform.os,\n      deviceId: draft.last_modified_platform.device_id\n    },\n    statistics: {\n      totalVideoClips: videoClips.length,\n      totalAudioClips: audioClips.length,\n      totalTracks: tracks.length,\n      totalSegments,\n      uniqueVideoFiles,\n      uniqueAudioFiles\n    }\n  };\n}\n\n/**\n * 格式化输出视频信息\n */\nfunction formatVideoInfo(info: VideoInfo): void {\n  console.log('🎬 剪映项目详细信息');\n  console.log('=' .repeat(60));\n\n  // 项目基本信息\n  console.log('\\n📋 项目基本信息:');\n  console.log(`  项目ID: ${info.projectId}`);\n  console.log(`  总时长: ${info.projectDurationSeconds}秒 (${info.projectDuration}微秒)`);\n  console.log(`  帧率: ${info.fps} FPS`);\n  console.log(`  画布尺寸: ${info.canvasSize.width}x${info.canvasSize.height} (${info.canvasSize.ratio})`);\n\n  // 应用信息\n  console.log('\\n📱 应用信息:');\n  console.log(`  应用: ${info.appInfo.appSource} v${info.appInfo.appVersion}`);\n  console.log(`  平台: ${info.appInfo.platform} (${info.appInfo.os})`);\n  console.log(`  设备ID: ${info.appInfo.deviceId}`);\n\n  // 统计信息\n  console.log('\\n📊 统计信息:');\n  console.log(`  视频素材: ${info.statistics.totalVideoClips}个`);\n  console.log(`  音频素材: ${info.statistics.totalAudioClips}个`);\n  console.log(`  轨道数量: ${info.statistics.totalTracks}个`);\n  console.log(`  片段总数: ${info.statistics.totalSegments}个`);\n  console.log(`  独立视频文件: ${info.statistics.uniqueVideoFiles.length}个`);\n  console.log(`  独立音频文件: ${info.statistics.uniqueAudioFiles.length}个`);\n\n  // 视频素材详情\n  if (info.videoClips.length > 0) {\n    console.log('\\n🎥 视频素材详情:');\n    info.videoClips.forEach((clip, index) => {\n      console.log(`  ${index + 1}. ${clip.fileName}`);\n      console.log(`     文件路径: ${clip.filePath}`);\n      console.log(`     时长: ${clip.durationSeconds}秒`);\n      console.log(`     分辨率: ${clip.resolution.width}x${clip.resolution.height} (${clip.resolution.aspectRatio})`);\n      console.log(`     包含音频: ${clip.hasAudio ? '是' : '否'}`);\n      console.log(`     裁剪比例: ${clip.cropInfo.ratio} (缩放: ${clip.cropInfo.scale})`);\n      console.log(`     AI生成: ${clip.isAIGenerated ? '是' : '否'}`);\n      console.log(`     版权内容: ${clip.isCopyrighted ? '是' : '否'}`);\n      console.log('');\n    });\n  }\n\n  // 音频素材详情\n  if (info.audioClips.length > 0) {\n    console.log('\\n🎵 音频素材详情:');\n    info.audioClips.forEach((clip, index) => {\n      console.log(`  ${index + 1}. ${clip.name}`);\n      console.log(`     文件路径: ${clip.filePath}`);\n      console.log(`     时长: ${clip.durationSeconds}秒`);\n      console.log(`     类型: ${clip.type}`);\n      console.log(`     AI克隆音色: ${clip.isAIClone ? '是' : '否'}`);\n      console.log(`     文本配音: ${clip.isTextOverdub ? '是' : '否'}`);\n      console.log('');\n    });\n  }\n\n  // 轨道详情\n  if (info.tracks.length > 0) {\n    console.log('\\n🛤️ 轨道详情:');\n    info.tracks.forEach((track, index) => {\n      console.log(`  ${index + 1}. ${track.name} (${track.type})`);\n      console.log(`     轨道ID: ${track.id}`);\n      console.log(`     片段数量: ${track.segmentCount}个`);\n\n      if (track.segments.length > 0) {\n        console.log('     片段详情:');\n        track.segments.forEach((segment, segIndex) => {\n          console.log(`       ${segIndex + 1}. 素材ID: ${segment.materialId}`);\n          console.log(`          时间轴: ${segment.timeRange.startSeconds}s - ${segment.timeRange.startSeconds + segment.timeRange.durationSeconds}s (时长: ${segment.timeRange.durationSeconds}s)`);\n          console.log(`          源时间: ${segment.sourceTimeRange.startSeconds}s - ${segment.sourceTimeRange.startSeconds + segment.sourceTimeRange.durationSeconds}s`);\n          console.log(`          变换: 透明度=${segment.transform.alpha}, 旋转=${segment.transform.rotation}°`);\n          console.log(`          缩放: x=${segment.transform.scale.x}, y=${segment.transform.scale.y}`);\n          console.log(`          位置: x=${segment.transform.position.x}, y=${segment.transform.position.y}`);\n          console.log(`          翻转: 水平=${segment.transform.flip.horizontal}, 垂直=${segment.transform.flip.vertical}`);\n        });\n      }\n      console.log('');\n    });\n  }\n\n  // 文件列表\n  console.log('\\n📁 使用的文件列表:');\n  console.log('  视频文件:');\n  info.statistics.uniqueVideoFiles.forEach((file, index) => {\n    console.log(`    ${index + 1}. ${file}`);\n  });\n\n  if (info.statistics.uniqueAudioFiles.length > 0) {\n    console.log('  音频文件:');\n    info.statistics.uniqueAudioFiles.forEach((file, index) => {\n      console.log(`    ${index + 1}. ${file}`);\n    });\n  }\n}\n\n/**\n * 导出为JSON文件\n */\nfunction exportToJson(info: VideoInfo, outputPath: string): void {\n  const jsonContent = JSON.stringify(info, null, 2);\n  fs.writeFileSync(outputPath, jsonContent, 'utf-8');\n  console.log(`\\n💾 详细信息已导出到: ${outputPath}`);\n}\n\n/**\n * 主函数\n */\nfunction main(): void {\n  const args = process.argv.slice(2);\n\n  if (args.length === 0) {\n    console.log('使用方法:');\n    console.log('  ts-node parse-draft.ts <draft_content.json路径> [输出JSON路径]');\n    console.log('');\n    console.log('示例:');\n    console.log('  ts-node parse-draft.ts ./draft_content.json');\n    console.log('  ts-node parse-draft.ts ./draft_content.json ./output.json');\n    process.exit(1);\n  }\n\n  const inputPath = args[0];\n  const outputPath = args[1];\n\n  try {\n    console.log(`🔍 正在解析剪映草稿文件: ${inputPath}`);\n\n    const videoInfo = parseJianyingDraft(inputPath);\n\n    // 输出格式化信息\n    formatVideoInfo(videoInfo);\n\n    // 如果指定了输出路径，导出JSON\n    if (outputPath) {\n      exportToJson(videoInfo, outputPath);\n    }\n\n    console.log('\\n✅ 解析完成!');\n\n  } catch (error) {\n    console.error('❌ 解析失败:', error instanceof Error ? error.message : String(error));\n    process.exit(1);\n  }\n}\n\n// 如果直接运行此脚本，执行主函数\nif (require.main === module) {\n  main();\n}\n\n// 导出主要函数\nexport { parseJianyingDraft, formatVideoInfo, exportToJson };\n\n// 导出工具函数\nexport { microsecondsToSeconds, calculateAspectRatio };\n\n// 添加缺失的工具函数\nexport function secondsToMicroseconds(seconds: number): number {\n  return seconds * 1000000;\n}\n\n// 导出类型定义\nexport type {\n  VideoInfo,\n  VideoClip,\n  AudioClip,\n  TrackSegment,\n  Track,\n  AppInfo,\n  Statistics,\n  CanvasConfig,\n  Transform,\n  TimeRange,\n  CropInfo\n};\n", "#!/usr/bin/env ts-node\n\n/**\n * 增强版剪映草稿文件解析器\n * 提供更多分析功能和可视化输出\n */\n\nimport { parseJianyingDraft, VideoInfo } from './parse-draft';\nimport * as fs from 'fs';\nimport * as path from 'path';\n\ninterface EnhancedAnalysis {\n  basicInfo: VideoInfo;\n  analysis: {\n    complexity: {\n      score: number;\n      level: 'Simple' | 'Medium' | 'Complex' | 'Very Complex';\n      factors: string[];\n    };\n    timeline: {\n      totalDuration: number;\n      segmentCount: number;\n      averageSegmentLength: number;\n      shortestSegment: number;\n      longestSegment: number;\n    };\n    materials: {\n      videoFileUsage: Array<{\n        filePath: string;\n        usageCount: number;\n        totalDuration: number;\n        segments: Array<{\n          trackId: string;\n          startTime: number;\n          duration: number;\n        }>;\n      }>;\n      audioFileUsage: Array<{\n        filePath: string;\n        usageCount: number;\n        totalDuration: number;\n      }>;\n    };\n    editing: {\n      hasTransformations: boolean;\n      hasScaling: boolean;\n      hasRotation: boolean;\n      hasPositionChanges: boolean;\n      hasFlipping: boolean;\n      transformationCount: number;\n    };\n  };\n  recommendations: Array<{\n    type: string;\n    description: string;\n  }>;\n}\n\n/**\n * 计算项目复杂度\n */\nfunction calculateComplexity(info: VideoInfo): EnhancedAnalysis['analysis']['complexity'] {\n  let score = 0;\n  const factors: string[] = [];\n\n  // 基于轨道数量\n  if (info.tracks.length > 3) {\n    score += 20;\n    factors.push(`多轨道编辑 (${info.tracks.length}个轨道)`);\n  }\n\n  // 基于片段数量\n  const totalSegments = info.statistics.totalSegments;\n  if (totalSegments > 20) {\n    score += 30;\n    factors.push(`大量片段 (${totalSegments}个片段)`);\n  } else if (totalSegments > 10) {\n    score += 15;\n    factors.push(`中等片段数量 (${totalSegments}个片段)`);\n  }\n\n  // 基于素材数量\n  if (info.statistics.totalVideoClips > 10) {\n    score += 20;\n    factors.push(`多个视频素材 (${info.statistics.totalVideoClips}个)`);\n  }\n\n  // 基于变换复杂度\n  let transformationCount = 0;\n  info.tracks.forEach(track => {\n    track.segments.forEach(segment => {\n      if (segment.transform.rotation !== 0) transformationCount++;\n      if (segment.transform.scale.x !== 1 || segment.transform.scale.y !== 1) transformationCount++;\n      if (segment.transform.position.x !== 0 || segment.transform.position.y !== 0) transformationCount++;\n      if (segment.transform.flip.horizontal || segment.transform.flip.vertical) transformationCount++;\n    });\n  });\n\n  if (transformationCount > 10) {\n    score += 25;\n    factors.push(`复杂变换 (${transformationCount}个变换)`);\n  } else if (transformationCount > 5) {\n    score += 10;\n    factors.push(`中等变换 (${transformationCount}个变换)`);\n  }\n\n  // 基于时长\n  if (info.projectDurationSeconds > 300) { // 5分钟以上\n    score += 15;\n    factors.push(`长视频 (${info.projectDurationSeconds}秒)`);\n  }\n\n  let level: EnhancedAnalysis['analysis']['complexity']['level'];\n  if (score >= 70) level = 'Very Complex';\n  else if (score >= 50) level = 'Complex';\n  else if (score >= 30) level = 'Medium';\n  else level = 'Simple';\n\n  return { score, level, factors };\n}\n\n/**\n * 分析时间轴信息\n */\nfunction analyzeTimeline(info: VideoInfo): EnhancedAnalysis['analysis']['timeline'] {\n  const allSegments = info.tracks.flatMap(track => track.segments);\n  const durations = allSegments.map(seg => seg.timeRange.durationSeconds);\n  \n  return {\n    totalDuration: info.projectDurationSeconds,\n    segmentCount: allSegments.length,\n    averageSegmentLength: durations.reduce((a, b) => a + b, 0) / durations.length,\n    shortestSegment: Math.min(...durations),\n    longestSegment: Math.max(...durations)\n  };\n}\n\n/**\n * 分析素材使用情况\n */\nfunction analyzeMaterials(info: VideoInfo): EnhancedAnalysis['analysis']['materials'] {\n  // 分析视频文件使用情况\n  const videoFileUsage = new Map<string, {\n    usageCount: number;\n    totalDuration: number;\n    segments: Array<{ trackId: string; startTime: number; duration: number; }>;\n  }>();\n\n  info.tracks.forEach(track => {\n    track.segments.forEach(segment => {\n      // 通过素材ID找到对应的视频文件\n      const videoClip = info.videoClips.find(clip => clip.id === segment.materialId);\n      if (videoClip) {\n        const filePath = videoClip.filePath;\n        if (!videoFileUsage.has(filePath)) {\n          videoFileUsage.set(filePath, { usageCount: 0, totalDuration: 0, segments: [] });\n        }\n        const usage = videoFileUsage.get(filePath)!;\n        usage.usageCount++;\n        usage.totalDuration += segment.timeRange.durationSeconds;\n        usage.segments.push({\n          trackId: track.id,\n          startTime: segment.timeRange.startSeconds,\n          duration: segment.timeRange.durationSeconds\n        });\n      }\n    });\n  });\n\n  // 分析音频文件使用情况\n  const audioFileUsage = info.audioClips.map(clip => ({\n    filePath: clip.filePath,\n    usageCount: 1, // 简化处理，假设每个音频素材只使用一次\n    totalDuration: clip.durationSeconds\n  }));\n\n  return {\n    videoFileUsage: Array.from(videoFileUsage.entries()).map(([filePath, usage]) => ({\n      filePath,\n      ...usage\n    })),\n    audioFileUsage\n  };\n}\n\n/**\n * 分析编辑特性\n */\nfunction analyzeEditing(info: VideoInfo): EnhancedAnalysis['analysis']['editing'] {\n  let hasTransformations = false;\n  let hasScaling = false;\n  let hasRotation = false;\n  let hasPositionChanges = false;\n  let hasFlipping = false;\n  let transformationCount = 0;\n\n  info.tracks.forEach(track => {\n    track.segments.forEach(segment => {\n      if (segment.transform.rotation !== 0) {\n        hasRotation = true;\n        hasTransformations = true;\n        transformationCount++;\n      }\n      if (segment.transform.scale.x !== 1 || segment.transform.scale.y !== 1) {\n        hasScaling = true;\n        hasTransformations = true;\n        transformationCount++;\n      }\n      if (segment.transform.position.x !== 0 || segment.transform.position.y !== 0) {\n        hasPositionChanges = true;\n        hasTransformations = true;\n        transformationCount++;\n      }\n      if (segment.transform.flip.horizontal || segment.transform.flip.vertical) {\n        hasFlipping = true;\n        hasTransformations = true;\n        transformationCount++;\n      }\n    });\n  });\n\n  return {\n    hasTransformations,\n    hasScaling,\n    hasRotation,\n    hasPositionChanges,\n    hasFlipping,\n    transformationCount\n  };\n}\n\n/**\n * 生成建议\n */\nfunction generateRecommendations(analysis: Omit<EnhancedAnalysis['analysis'], 'recommendations'>): Array<{type: string; description: string}> {\n  const recommendations: Array<{type: string; description: string}> = [];\n\n  if (analysis.complexity.level === 'Very Complex') {\n    recommendations.push({\n      type: 'performance',\n      description: '项目复杂度很高，建议分段处理或简化编辑'\n    });\n  }\n\n  if (analysis.timeline.segmentCount > 50) {\n    recommendations.push({\n      type: 'optimization',\n      description: '片段数量较多，考虑合并相似片段以提高性能'\n    });\n  }\n\n  if (analysis.timeline.averageSegmentLength < 1) {\n    recommendations.push({\n      type: 'content',\n      description: '平均片段时长较短，可能影响观看体验'\n    });\n  }\n\n  if (analysis.editing.transformationCount > 20) {\n    recommendations.push({\n      type: 'performance',\n      description: '变换操作较多，注意检查渲染性能'\n    });\n  }\n\n  const videoFileCount = analysis.materials.videoFileUsage.length;\n  if (videoFileCount === 1) {\n    recommendations.push({\n      type: 'content',\n      description: '只使用了一个视频文件，考虑添加更多素材丰富内容'\n    });\n  } else if (videoFileCount > 10) {\n    recommendations.push({\n      type: 'management',\n      description: '使用了大量视频文件，注意素材管理和存储空间'\n    });\n  }\n\n  if (recommendations.length === 0) {\n    recommendations.push({\n      type: 'general',\n      description: '项目结构良好，无特殊建议'\n    });\n  }\n\n  return recommendations;\n}\n\n/**\n * 执行增强分析\n */\nfunction performEnhancedAnalysis(filePath: string): EnhancedAnalysis {\n  const basicInfo = parseJianyingDraft(filePath);\n\n  const complexity = calculateComplexity(basicInfo);\n  const timeline = analyzeTimeline(basicInfo);\n  const materials = analyzeMaterials(basicInfo);\n  const editing = analyzeEditing(basicInfo);\n  const analysisData = { complexity, timeline, materials, editing };\n  const recommendations = generateRecommendations(analysisData);\n\n  return {\n    basicInfo,\n    analysis: {\n      complexity,\n      timeline,\n      materials,\n      editing\n    },\n    recommendations\n  };\n}\n\n/**\n * 格式化增强分析结果\n */\nfunction formatEnhancedAnalysis(analysis: EnhancedAnalysis): void {\n  console.log('🚀 剪映项目增强分析报告');\n  console.log('=' .repeat(80));\n  \n  // 复杂度分析\n  console.log('\\n📊 项目复杂度分析:');\n  console.log(`  复杂度等级: ${analysis.analysis.complexity.level}`);\n  console.log(`  复杂度评分: ${analysis.analysis.complexity.score}/100`);\n  console.log('  影响因素:');\n  analysis.analysis.complexity.factors.forEach(factor => {\n    console.log(`    • ${factor}`);\n  });\n\n  // 时间轴分析\n  console.log('\\n⏱️ 时间轴分析:');\n  console.log(`  总时长: ${analysis.analysis.timeline.totalDuration.toFixed(2)}秒`);\n  console.log(`  片段总数: ${analysis.analysis.timeline.segmentCount}个`);\n  console.log(`  平均片段时长: ${analysis.analysis.timeline.averageSegmentLength.toFixed(2)}秒`);\n  console.log(`  最短片段: ${analysis.analysis.timeline.shortestSegment.toFixed(2)}秒`);\n  console.log(`  最长片段: ${analysis.analysis.timeline.longestSegment.toFixed(2)}秒`);\n\n  // 素材使用分析\n  console.log('\\n📁 素材使用分析:');\n  console.log('  视频文件使用情况:');\n  analysis.analysis.materials.videoFileUsage.forEach((usage, index) => {\n    console.log(`    ${index + 1}. ${path.basename(usage.filePath)}`);\n    console.log(`       使用次数: ${usage.usageCount}次`);\n    console.log(`       总使用时长: ${usage.totalDuration.toFixed(2)}秒`);\n    console.log(`       片段分布: ${usage.segments.length}个片段`);\n  });\n\n  // 编辑特性分析\n  console.log('\\n🎨 编辑特性分析:');\n  console.log(`  使用变换效果: ${analysis.analysis.editing.hasTransformations ? '是' : '否'}`);\n  console.log(`  使用缩放: ${analysis.analysis.editing.hasScaling ? '是' : '否'}`);\n  console.log(`  使用旋转: ${analysis.analysis.editing.hasRotation ? '是' : '否'}`);\n  console.log(`  使用位置调整: ${analysis.analysis.editing.hasPositionChanges ? '是' : '否'}`);\n  console.log(`  使用翻转: ${analysis.analysis.editing.hasFlipping ? '是' : '否'}`);\n  console.log(`  变换操作总数: ${analysis.analysis.editing.transformationCount}个`);\n\n  // 建议\n  console.log('\\n💡 优化建议:');\n  analysis.recommendations.forEach(rec => {\n    console.log(`  • [${rec.type}] ${rec.description}`);\n  });\n}\n\n/**\n * 主函数\n */\nfunction main(): void {\n  const args = process.argv.slice(2);\n  \n  if (args.length === 0) {\n    console.log('使用方法:');\n    console.log('  ts-node enhanced-parser.ts <draft_content.json路径> [输出JSON路径]');\n    console.log('');\n    console.log('示例:');\n    console.log('  ts-node enhanced-parser.ts ./draft_content.json');\n    console.log('  ts-node enhanced-parser.ts ./draft_content.json ./enhanced_analysis.json');\n    process.exit(1);\n  }\n  \n  const inputPath = args[0];\n  const outputPath = args[1];\n  \n  try {\n    console.log(`🔍 正在执行增强分析: ${inputPath}`);\n    \n    const analysis = performEnhancedAnalysis(inputPath);\n    \n    // 输出分析结果\n    formatEnhancedAnalysis(analysis);\n    \n    // 如果指定了输出路径，导出JSON\n    if (outputPath) {\n      const jsonContent = JSON.stringify(analysis, null, 2);\n      fs.writeFileSync(outputPath, jsonContent, 'utf-8');\n      console.log(`\\n💾 增强分析结果已导出到: ${outputPath}`);\n    }\n    \n    console.log('\\n✅ 增强分析完成!');\n    \n  } catch (error) {\n    console.error('❌ 分析失败:', error instanceof Error ? error.message : String(error));\n    process.exit(1);\n  }\n}\n\n// 如果直接运行此脚本，执行主函数\nif (require.main === module) {\n  main();\n}\n\n// 主要分析函数\nexport { performEnhancedAnalysis, formatEnhancedAnalysis };\n\n// 添加单独的分析函数导出\nexport function analyzeJianyingProject(filePath: string): EnhancedAnalysis {\n  return performEnhancedAnalysis(filePath);\n}\n\nexport function analyzeJianyingProjectFromData(videoInfo: VideoInfo): EnhancedAnalysis {\n  const complexity = calculateComplexity(videoInfo);\n  const timeline = analyzeTimeline(videoInfo);\n  const materials = analyzeMaterials(videoInfo);\n  const editing = analyzeEditing(videoInfo);\n  const analysisData = { complexity, timeline, materials, editing };\n  const recommendations = generateRecommendationsFromAnalysis(analysisData);\n\n  return {\n    basicInfo: videoInfo,\n    analysis: analysisData,\n    recommendations\n  };\n}\n\nexport function generateRecommendationsFromAnalysis(analysis: any): any[] {\n  const recommendations: any[] = [];\n\n  if (analysis.complexity.level === 'Very Complex') {\n    recommendations.push({\n      type: 'performance',\n      description: '项目复杂度很高，建议优化性能'\n    });\n  }\n\n  return recommendations;\n}\n\n// 导出类型定义\nexport type { EnhancedAnalysis };\n\n// 添加类型别名\nexport type ComplexityAnalysis = EnhancedAnalysis['analysis']['complexity'];\nexport type TimelineAnalysis = EnhancedAnalysis['analysis']['timeline'];\nexport type MaterialUsage = EnhancedAnalysis['analysis']['materials'];\nexport type EditingFeatures = EnhancedAnalysis['analysis']['editing'];\nexport type Recommendation = {\n  type: string;\n  description: string;\n};\n", "#!/usr/bin/env ts-node\n\n/**\n * 自动生成剪映草稿文件生成器\n * 扫描指定目录的视频文件，自动生成 draft_content.json\n */\n\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { execSync } from 'child_process';\n\n// 简单的 UUID 生成函数\nfunction generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\n// 支持的视频文件扩展名\nconst VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'];\nconst AUDIO_EXTENSIONS = ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg'];\n\ninterface VideoFileInfo {\n  filePath: string;\n  fileName: string;\n  extension: string;\n  size: number;\n  isVideo: boolean;\n  isAudio: boolean;\n}\n\ninterface GeneratedVideoClip {\n  aigc_type: string;\n  audio_fade: null;\n  cartoon_path: string;\n  category_id: string;\n  category_name: string;\n  check_flag: number;\n  crop: {\n    lower_left_x: number;\n    lower_left_y: number;\n    lower_right_x: number;\n    lower_right_y: number;\n    upper_left_x: number;\n    upper_left_y: number;\n    upper_right_x: number;\n    upper_right_y: number;\n  };\n  crop_ratio: string;\n  crop_scale: number;\n  duration: number;\n  extra_type_option: number;\n  formula_id: string;\n  freeze: null;\n  has_audio: boolean;\n  height: number;\n  id: string;\n  intensifies_audio_path: string;\n  intensifies_path: string;\n  is_ai_generate_content: boolean;\n  is_copyright: boolean;\n  is_text_edit_overdub: boolean;\n  is_unified_beauty_mode: boolean;\n  local_id: string;\n  local_material_id: string;\n  material_id: string;\n  material_name: string;\n  material_url: string;\n  matting: {\n    flag: number;\n    has_use_quick_brush: boolean;\n    has_use_quick_eraser: boolean;\n    interactiveTime: any[];\n    path: string;\n    strokes: any[];\n  };\n  media_path: string;\n  object_locked: null;\n  origin_material_id: string;\n  path: string;\n  picture_from: string;\n  picture_set_category_id: string;\n  picture_set_category_name: string;\n  request_id: string;\n  reverse_intensifies_path: string;\n  reverse_path: string;\n  smart_motion: null;\n  source: number;\n  source_platform: number;\n  stable: {\n    matrix_path: string;\n    stable_level: number;\n    time_range: {\n      duration: number;\n      start: number;\n    };\n  };\n  team_id: string;\n  type: string;\n  video_algorithm: {\n    algorithms: any[];\n    deflicker: null;\n    motion_blur_config: null;\n    noise_reduction: null;\n    path: string;\n    time_range: null;\n  };\n  width: number;\n}\n\ninterface GeneratedAudioClip {\n  app_id: number;\n  category_id: string;\n  category_name: string;\n  check_flag: number;\n  copyright_limit_type: string;\n  duration: number;\n  effect_id: string;\n  formula_id: string;\n  id: string;\n  intensifies_path: string;\n  is_ai_clone_tone: boolean;\n  is_text_edit_overdub: boolean;\n  is_ugc: boolean;\n  local_material_id: string;\n  music_id: string;\n  name: string;\n  path: string;\n  query: string;\n  request_id: string;\n  resource_id: string;\n  search_id: string;\n  source_from: string;\n  source_platform: number;\n  team_id: string;\n  text_id: string;\n  tone_category_id: string;\n  tone_category_name: string;\n  tone_effect_id: string;\n  tone_effect_name: string;\n  tone_platform: string;\n  tone_second_category_id: string;\n  tone_second_category_name: string;\n  tone_speaker: string;\n  tone_type: string;\n  type: string;\n  video_id: string;\n  wave_points: any[];\n}\n\ninterface GeneratedTrackSegment {\n  clip?: {\n    alpha: number;\n    flip: {\n      horizontal: boolean;\n      vertical: boolean;\n    };\n    rotation: number;\n    scale: {\n      x: number;\n      y: number;\n    };\n    transform: {\n      x: number;\n      y: number;\n    };\n  };\n  material_id: string;\n  target_timerange: {\n    duration: number;\n    start: number;\n  };\n  source_timerange?: {\n    duration: number;\n    start: number;\n  } | null;\n}\n\ninterface GeneratedTrack {\n  attribute: number;\n  flag: number;\n  id: string;\n  segments: GeneratedTrackSegment[];\n  type: string;\n}\n\ninterface GeneratedDraft {\n  canvas_config: {\n    height: number;\n    ratio: string;\n    width: number;\n  };\n  color_space: number;\n  config: {\n    adjust_max_index: number;\n    attachment_info: any[];\n    combination_max_index: number;\n    export_range: null;\n    extract_audio_last_index: number;\n    lyrics_recognition_id: string;\n    lyrics_sync: boolean;\n    lyrics_taskinfo: any[];\n    maintrack_adsorb: boolean;\n    material_save_mode: number;\n    multi_language_current: string;\n    multi_language_list: any[];\n    multi_language_main: string;\n    multi_language_mode: string;\n    original_sound_last_index: number;\n    record_audio_last_index: number;\n    sticker_max_index: number;\n    subtitle_keywords_config: null;\n    subtitle_recognition_id: string;\n    subtitle_sync: boolean;\n    subtitle_taskinfo: any[];\n    system_font_list: any[];\n    text_recognition_id: string;\n    text_sync: boolean;\n    text_taskinfo: any[];\n    video_recognition_id: string;\n    video_sync: boolean;\n    video_taskinfo: any[];\n  };\n  create_time: number;\n  draft_fold_path: string;\n  draft_id: string;\n  draft_name: string;\n  draft_removable_storage_device: string;\n  duration: number;\n  fps: number;\n  id: string;\n  last_modified_platform: {\n    app_id: number;\n    app_source: string;\n    app_version: string;\n    device_id: string;\n    hard_disk_id: string;\n    mac_address: string;\n    os_version: string;\n    platform: string;\n    screen_height: number;\n    screen_width: number;\n  };\n  materials: {\n    audio_effects: any[];\n    audio_fades: any[];\n    audio_track_indexes: any[];\n    audios: any[];\n    beats: any[];\n    canvases: any[];\n    chromas: any[];\n    color_curves: any[];\n    color_wheels: any[];\n    effects: any[];\n    flowers: any[];\n    handwrites: any[];\n    hsl: any[];\n    images: any[];\n    keyframes: any[];\n    masks: any[];\n    material_animations: any[];\n    placeholders: any[];\n    plugin_effects: any[];\n    shapes: any[];\n    sounds: any[];\n    stickers: any[];\n    texts: any[];\n    videos: any[];\n  };\n  mutable_config: null;\n  name: string;\n  new_version: string;\n  platform: {\n    app_id: number;\n    app_source: string;\n    app_version: string;\n    device_id: string;\n    hard_disk_id: string;\n    mac_address: string;\n    os_version: string;\n    platform: string;\n    screen_height: number;\n    screen_width: number;\n  };\n  relationships: any[];\n  revert_generate_segment_config: null;\n  source: string;\n  tracks: GeneratedTrack[];\n  update_time: number;\n  version: string;\n}\n\n/**\n * 扫描目录获取所有媒体文件\n */\nfunction scanDirectory(dirPath: string): VideoFileInfo[] {\n  const files: VideoFileInfo[] = [];\n  \n  function scanRecursive(currentPath: string) {\n    try {\n      const items = fs.readdirSync(currentPath);\n      \n      for (const item of items) {\n        const fullPath = path.join(currentPath, item);\n        const stat = fs.statSync(fullPath);\n        \n        if (stat.isDirectory()) {\n          // 递归扫描子目录\n          scanRecursive(fullPath);\n        } else if (stat.isFile()) {\n          const ext = path.extname(item).toLowerCase();\n          const isVideo = VIDEO_EXTENSIONS.includes(ext);\n          const isAudio = AUDIO_EXTENSIONS.includes(ext);\n          \n          if (isVideo || isAudio) {\n            files.push({\n              filePath: fullPath,\n              fileName: path.basename(item, ext),\n              extension: ext,\n              size: stat.size,\n              isVideo,\n              isAudio\n            });\n          }\n        }\n      }\n    } catch (error) {\n      console.warn(`警告: 无法访问目录 ${currentPath}:`, error instanceof Error ? error.message : String(error));\n    }\n  }\n  \n  scanRecursive(dirPath);\n  return files;\n}\n\n/**\n * 检查是否安装了 ffprobe\n */\nfunction checkFFProbe(): boolean {\n  try {\n    execSync('ffprobe -version', { stdio: 'ignore' });\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * 使用 ffprobe 获取真实的视频信息\n */\nfunction getVideoInfoWithFFProbe(filePath: string): { duration: number; width: number; height: number; hasAudio: boolean } | null {\n  try {\n    const command = `ffprobe -v quiet -print_format json -show_format -show_streams \"${filePath}\"`;\n    const output = execSync(command, { encoding: 'utf-8' });\n    const data = JSON.parse(output);\n\n    let duration = 0;\n    let width = 1920;\n    let height = 1080;\n    let hasAudio = false;\n\n    // 获取时长\n    if (data.format && data.format.duration) {\n      duration = Math.floor(parseFloat(data.format.duration) * 1000000); // 转换为微秒\n    }\n\n    // 获取视频流信息\n    if (data.streams) {\n      for (const stream of data.streams) {\n        if (stream.codec_type === 'video') {\n          width = stream.width || width;\n          height = stream.height || height;\n        } else if (stream.codec_type === 'audio') {\n          hasAudio = true;\n        }\n      }\n    }\n\n    return { duration, width, height, hasAudio };\n  } catch (error) {\n    console.warn(`警告: 无法获取文件信息 ${filePath}:`, error instanceof Error ? error.message : String(error));\n    return null;\n  }\n}\n\n/**\n * 获取视频文件的基本信息（优先使用 ffprobe，回退到估算）\n */\nfunction getVideoInfo(file: VideoFileInfo, useFFProbe: boolean = true) {\n  // 如果可以使用 ffprobe，尝试获取真实信息\n  if (useFFProbe && checkFFProbe()) {\n    const realInfo = getVideoInfoWithFFProbe(file.filePath);\n    if (realInfo) {\n      return realInfo;\n    }\n  }\n\n  // 回退到默认估算\n  const defaultWidth = 1920;\n  const defaultHeight = 1080;\n\n  // 根据文件大小粗略估算时长（这只是示例，不准确）\n  const estimatedDuration = Math.max(1000000, Math.min(60000000, file.size / 1000)); // 1秒到60秒\n\n  return {\n    duration: Math.floor(estimatedDuration),\n    width: defaultWidth,\n    height: defaultHeight,\n    hasAudio: file.isVideo // 假设视频文件都有音频\n  };\n}\n\n/**\n * 生成视频素材\n */\nfunction generateVideoClips(videoFiles: VideoFileInfo[], useFFProbe: boolean = true): GeneratedVideoClip[] {\n  return videoFiles.map(file => {\n    const info = getVideoInfo(file, useFFProbe);\n\n    return {\n      aigc_type: \"none\",\n      audio_fade: null,\n      cartoon_path: \"\",\n      category_id: \"\",\n      category_name: \"\",\n      check_flag: 63487,\n      crop: {\n        lower_left_x: 0.0,\n        lower_left_y: 1.0,\n        lower_right_x: 1.0,\n        lower_right_y: 1.0,\n        upper_left_x: 0.0,\n        upper_left_y: 0.0,\n        upper_right_x: 1.0,\n        upper_right_y: 0.0\n      },\n      crop_ratio: \"free\",\n      crop_scale: 1.0,\n      duration: info.duration,\n      extra_type_option: 1,\n      formula_id: \"\",\n      freeze: null,\n      has_audio: info.hasAudio,\n      height: info.height,\n      id: generateUUID().toUpperCase(),\n      intensifies_audio_path: \"\",\n      intensifies_path: \"\",\n      is_ai_generate_content: false,\n      is_copyright: false,\n      is_text_edit_overdub: false,\n      is_unified_beauty_mode: false,\n      local_id: \"\",\n      local_material_id: \"\",\n      material_id: \"\",\n      material_name: file.fileName + file.extension,\n      material_url: \"\",\n      matting: {\n        flag: 0,\n        has_use_quick_brush: false,\n        has_use_quick_eraser: false,\n        interactiveTime: [],\n        path: \"\",\n        strokes: []\n      },\n      media_path: \"\",\n      object_locked: null,\n      origin_material_id: \"\",\n      path: file.filePath,\n      picture_from: \"none\",\n      picture_set_category_id: \"\",\n      picture_set_category_name: \"\",\n      request_id: \"\",\n      reverse_intensifies_path: \"\",\n      reverse_path: \"\",\n      smart_motion: null,\n      source: 0,\n      source_platform: 0,\n      stable: {\n        matrix_path: \"\",\n        stable_level: 0,\n        time_range: {\n          duration: info.duration,\n          start: 0\n        }\n      },\n      team_id: \"\",\n      type: \"video\",\n      video_algorithm: {\n        algorithms: [],\n        deflicker: null,\n        motion_blur_config: null,\n        noise_reduction: null,\n        path: \"\",\n        time_range: null\n      },\n      width: info.width\n    };\n  });\n}\n\n/**\n * 生成音频素材\n */\nfunction generateAudioClips(audioFiles: VideoFileInfo[], useFFProbe: boolean = true): GeneratedAudioClip[] {\n  return audioFiles.map(file => {\n    const info = getVideoInfo(file, useFFProbe);\n\n    return {\n      app_id: 0,\n      category_id: \"\",\n      category_name: \"\",\n      check_flag: 1,\n      copyright_limit_type: \"none\",\n      duration: info.duration,\n      effect_id: \"\",\n      formula_id: \"\",\n      id: generateUUID().toUpperCase(),\n      intensifies_path: \"\",\n      is_ai_clone_tone: false,\n      is_text_edit_overdub: false,\n      is_ugc: false,\n      local_material_id: \"\",\n      music_id: \"\",\n      name: file.fileName,\n      path: file.filePath,\n      query: \"\",\n      request_id: \"\",\n      resource_id: \"\",\n      search_id: \"\",\n      source_from: \"\",\n      source_platform: 0,\n      team_id: \"\",\n      text_id: \"\",\n      tone_category_id: \"\",\n      tone_category_name: \"\",\n      tone_effect_id: \"\",\n      tone_effect_name: \"\",\n      tone_platform: \"\",\n      tone_second_category_id: \"\",\n      tone_second_category_name: \"\",\n      tone_speaker: \"\",\n      tone_type: \"\",\n      type: file.isVideo ? \"video_original_sound\" : \"audio\",\n      video_id: \"\",\n      wave_points: []\n    };\n  });\n}\n\n/**\n * 生成轨道和片段\n */\nfunction generateTracks(videoClips: GeneratedVideoClip[], audioClips: GeneratedAudioClip[]): GeneratedTrack[] {\n  const tracks: GeneratedTrack[] = [];\n  \n  // 生成主视频轨道\n  if (videoClips.length > 0) {\n    let currentTime = 0;\n    const segments: GeneratedTrackSegment[] = [];\n    \n    for (const clip of videoClips) {\n      segments.push({\n        clip: {\n          alpha: 1.0,\n          flip: {\n            horizontal: false,\n            vertical: false\n          },\n          rotation: 0.0,\n          scale: {\n            x: 1.0,\n            y: 1.0\n          },\n          transform: {\n            x: 0.0,\n            y: 0.0\n          }\n        },\n        material_id: clip.id,\n        target_timerange: {\n          duration: clip.duration,\n          start: currentTime\n        },\n        source_timerange: {\n          duration: clip.duration,\n          start: 0\n        }\n      });\n      \n      currentTime += clip.duration;\n    }\n    \n    tracks.push({\n      attribute: 0,\n      flag: 0,\n      id: generateUUID().toUpperCase(),\n      segments,\n      type: \"video\"\n    });\n  }\n  \n  // 生成音频轨道\n  if (audioClips.length > 0) {\n    let currentTime = 0;\n    const segments: GeneratedTrackSegment[] = [];\n    \n    for (const clip of audioClips) {\n      segments.push({\n        material_id: clip.id,\n        target_timerange: {\n          duration: clip.duration,\n          start: currentTime\n        },\n        source_timerange: {\n          duration: clip.duration,\n          start: 0\n        }\n      });\n      \n      currentTime += clip.duration;\n    }\n    \n    tracks.push({\n      attribute: 0,\n      flag: 0,\n      id: generateUUID().toUpperCase(),\n      segments,\n      type: \"audio\"\n    });\n  }\n  \n  return tracks;\n}\n\n/**\n * 生成完整的草稿文件\n */\nfunction generateDraft(files: VideoFileInfo[], options: {\n  canvasWidth?: number;\n  canvasHeight?: number;\n  fps?: number;\n  projectName?: string;\n  useFFProbe?: boolean;\n}): GeneratedDraft {\n  const videoFiles = files.filter(f => f.isVideo);\n  const audioFiles = files.filter(f => f.isAudio);\n\n  const useFFProbe = options.useFFProbe !== false; // 默认启用\n  const videoClips = generateVideoClips(videoFiles, useFFProbe);\n  const audioClips = generateAudioClips(audioFiles, useFFProbe);\n  const tracks = generateTracks(videoClips, audioClips);\n  \n  // 计算总时长\n  const totalDuration = Math.max(\n    ...tracks.map(track => \n      track.segments.reduce((sum, seg) => Math.max(sum, seg.target_timerange.start + seg.target_timerange.duration), 0)\n    ),\n    0\n  );\n  \n  const canvasWidth = options.canvasWidth || 1080;\n  const canvasHeight = options.canvasHeight || 1920;\n  const ratio = `${canvasWidth}:${canvasHeight}`;\n  const deviceId = \"auto-generated-\" + Date.now();\n  const currentTime = Date.now() * 1000;\n\n  return {\n    canvas_config: {\n      height: canvasHeight,\n      ratio: ratio,\n      width: canvasWidth\n    },\n    color_space: 0,\n    config: {\n      adjust_max_index: 2,\n      attachment_info: [],\n      combination_max_index: 1,\n      export_range: null,\n      extract_audio_last_index: 1,\n      lyrics_recognition_id: \"\",\n      lyrics_sync: true,\n      lyrics_taskinfo: [],\n      maintrack_adsorb: true,\n      material_save_mode: 0,\n      multi_language_current: \"none\",\n      multi_language_list: [],\n      multi_language_main: \"none\",\n      multi_language_mode: \"none\",\n      original_sound_last_index: 2,\n      record_audio_last_index: 1,\n      sticker_max_index: 1,\n      subtitle_keywords_config: null,\n      subtitle_recognition_id: \"\",\n      subtitle_sync: true,\n      subtitle_taskinfo: [],\n      system_font_list: [],\n      text_recognition_id: \"\",\n      text_sync: true,\n      text_taskinfo: [],\n      video_recognition_id: \"\",\n      video_sync: true,\n      video_taskinfo: []\n    },\n    create_time: currentTime,\n    draft_fold_path: \"\",\n    draft_id: generateUUID().toUpperCase(),\n    draft_name: options.projectName || \"Auto Generated Project\",\n    draft_removable_storage_device: \"\",\n    duration: totalDuration,\n    fps: options.fps || 30,\n    id: generateUUID().toUpperCase(),\n    last_modified_platform: {\n      app_id: 3704,\n      app_source: \"auto-generator\",\n      app_version: \"1.0.0\",\n      device_id: deviceId,\n      hard_disk_id: \"\",\n      mac_address: \"\",\n      os_version: \"auto\",\n      platform: \"auto\",\n      screen_height: 1080,\n      screen_width: 1920\n    },\n    materials: {\n      audio_effects: [],\n      audio_fades: [],\n      audio_track_indexes: [],\n      audios: audioClips,\n      beats: [],\n      canvases: [],\n      chromas: [],\n      color_curves: [],\n      color_wheels: [],\n      effects: [],\n      flowers: [],\n      handwrites: [],\n      hsl: [],\n      images: [],\n      keyframes: [],\n      masks: [],\n      material_animations: [],\n      placeholders: [],\n      plugin_effects: [],\n      shapes: [],\n      sounds: [],\n      stickers: [],\n      texts: [],\n      videos: videoClips\n    },\n    mutable_config: null,\n    name: options.projectName || \"\",\n    new_version: \"110.0.0\",\n    platform: {\n      app_id: 3704,\n      app_source: \"auto-generator\",\n      app_version: \"1.0.0\",\n      device_id: deviceId,\n      hard_disk_id: \"\",\n      mac_address: \"\",\n      os_version: \"auto\",\n      platform: \"auto\",\n      screen_height: 1080,\n      screen_width: 1920\n    },\n    relationships: [],\n    revert_generate_segment_config: null,\n    source: \"auto-generator\",\n    tracks,\n    update_time: currentTime,\n    version: \"13.2.0\"\n  };\n}\n\n/**\n * 主函数\n */\nfunction main(): void {\n  const args = process.argv.slice(2);\n  \n  if (args.length === 0) {\n    console.log('使用方法:');\n    console.log('  ts-node generate-draft.ts <扫描目录> [输出文件路径] [选项]');\n    console.log('');\n    console.log('示例:');\n    console.log('  ts-node generate-draft.ts ./videos');\n    console.log('  ts-node generate-draft.ts ./videos ./generated_draft.json');\n    console.log('  ts-node generate-draft.ts ./videos ./draft.json --width=1920 --height=1080 --fps=60');\n    console.log('');\n    console.log('选项:');\n    console.log('  --width=<数字>     画布宽度 (默认: 1080)');\n    console.log('  --height=<数字>    画布高度 (默认: 1920)');\n    console.log('  --fps=<数字>       帧率 (默认: 30)');\n    console.log('  --name=<字符串>    项目名称');\n    process.exit(1);\n  }\n  \n  const scanDir = args[0];\n  const outputPath = args[1] || './generated_draft_content.json';\n  \n  // 解析选项\n  const options: any = {};\n  for (let i = 2; i < args.length; i++) {\n    const arg = args[i];\n    if (arg.startsWith('--width=')) {\n      options.canvasWidth = parseInt(arg.split('=')[1]);\n    } else if (arg.startsWith('--height=')) {\n      options.canvasHeight = parseInt(arg.split('=')[1]);\n    } else if (arg.startsWith('--fps=')) {\n      options.fps = parseInt(arg.split('=')[1]);\n    } else if (arg.startsWith('--name=')) {\n      options.projectName = arg.split('=')[1];\n    }\n  }\n  \n  try {\n    console.log(`🔍 正在扫描目录: ${scanDir}`);\n    \n    if (!fs.existsSync(scanDir)) {\n      throw new Error(`目录不存在: ${scanDir}`);\n    }\n    \n    const files = scanDirectory(scanDir);\n    \n    if (files.length === 0) {\n      console.log('❌ 未找到任何媒体文件');\n      process.exit(1);\n    }\n    \n    console.log(`📁 找到 ${files.length} 个媒体文件:`);\n    files.forEach((file, index) => {\n      const type = file.isVideo ? '🎥' : '🎵';\n      console.log(`  ${index + 1}. ${type} ${file.fileName}${file.extension} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\n    });\n    \n    console.log('\\n🚀 正在生成草稿文件...');\n    const draft = generateDraft(files, options);\n    \n    // 保存文件\n    const jsonContent = JSON.stringify(draft, null, 2);\n    fs.writeFileSync(outputPath, jsonContent, 'utf-8');\n    \n    console.log(`\\n✅ 草稿文件生成成功!`);\n    console.log(`📄 输出文件: ${outputPath}`);\n    console.log(`📊 项目信息:`);\n    console.log(`   项目ID: ${draft.id}`);\n    console.log(`   总时长: ${(draft.duration / 1000000).toFixed(2)}秒`);\n    console.log(`   画布尺寸: ${draft.canvas_config.width}x${draft.canvas_config.height}`);\n    console.log(`   帧率: ${draft.fps} FPS`);\n    console.log(`   视频素材: ${draft.materials.videos.length}个`);\n    console.log(`   音频素材: ${draft.materials.audios.length}个`);\n    console.log(`   轨道数量: ${draft.tracks.length}个`);\n    \n    console.log('\\n💡 提示: 生成的文件使用了默认的视频信息。');\n    console.log('   如需准确的视频信息，建议集成 ffprobe 工具。');\n    \n  } catch (error) {\n    console.error('❌ 生成失败:', error instanceof Error ? error.message : String(error));\n    process.exit(1);\n  }\n}\n\n// 如果直接运行此脚本，执行主函数\nif (require.main === module) {\n  main();\n}\n\n// 导出主要函数\nexport {\n  generateDraft,\n  scanDirectory,\n  checkFFProbe,\n  getVideoInfoWithFFProbe,\n  generateVideoClips,\n  generateAudioClips,\n  generateTracks\n};\n\n// 导出类型定义\nexport type {\n  VideoFileInfo,\n  GeneratedVideoClip,\n  GeneratedAudioClip,\n  GeneratedTrack,\n  GeneratedTrackSegment,\n  GeneratedDraft\n};\n", "\n/**\n * @mixvideo/jianying - 剪映草稿文件工具包\n *\n * 提供剪映（CapCut）草稿文件的解析、分析和生成功能\n */\n\n// 解析功能\nexport {\n  parseJianyingDraft,\n  formatVideoInfo,\n  exportToJson,\n  microsecondsToSeconds,\n  secondsToMicroseconds,\n  calculateAspectRatio,\n  type VideoInfo,\n  type VideoClip,\n  type AudioClip,\n  type TrackSegment,\n  type Track,\n  type AppInfo,\n  type Statistics,\n  type CanvasConfig,\n  type Transform,\n  type TimeRange\n} from './parse-draft';\n\n// 增强分析功能\nexport {\n  analyzeJianyingProject,\n  analyzeJianyingProjectFromData,\n  generateRecommendationsFromAnalysis,\n  performEnhancedAnalysis,\n  formatEnhancedAnalysis,\n  type EnhancedAnalysis,\n  type ComplexityAnalysis,\n  type TimelineAnalysis,\n  type MaterialUsage,\n  type EditingFeatures,\n  type Recommendation\n} from './enhanced-parser';\n\n// 生成功能\nexport {\n  generateDraft,\n  scanDirectory,\n  checkFFProbe,\n  getVideoInfoWithFFProbe,\n  generateVideoClips,\n  generateAudioClips,\n  generateTracks,\n  type VideoFileInfo,\n  type GeneratedVideoClip,\n  type GeneratedAudioClip,\n  type GeneratedTrack,\n  type GeneratedTrackSegment,\n  type GeneratedDraft\n} from './generate-draft';\n\n// 工具函数\nexport const JianyingUtils = {\n  // 时间转换\n  microsecondsToSeconds: (microseconds: number): number => microseconds / 1000000,\n  secondsToMicroseconds: (seconds: number): number => seconds * 1000000,\n\n  // 宽高比计算\n  calculateAspectRatio: (width: number, height: number): string => {\n    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);\n    const divisor = gcd(width, height);\n    return `${width / divisor}:${height / divisor}`;\n  },\n\n  // 支持的文件格式\n  VIDEO_EXTENSIONS: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'],\n  AUDIO_EXTENSIONS: ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg'],\n\n  // 检查文件类型\n  isVideoFile: (filename: string): boolean => {\n    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n    return JianyingUtils.VIDEO_EXTENSIONS.includes(ext);\n  },\n\n  isAudioFile: (filename: string): boolean => {\n    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n    return JianyingUtils.AUDIO_EXTENSIONS.includes(ext);\n  },\n\n  isMediaFile: (filename: string): boolean => {\n    return JianyingUtils.isVideoFile(filename) || JianyingUtils.isAudioFile(filename);\n  }\n};\n\n// 常量\nexport const JIANYING_CONSTANTS = {\n  // 默认配置\n  DEFAULT_FPS: 30,\n  DEFAULT_CANVAS_WIDTH: 1080,\n  DEFAULT_CANVAS_HEIGHT: 1920,\n  DEFAULT_ASPECT_RATIO: '9:16',\n\n  // 时间单位\n  MICROSECONDS_PER_SECOND: 1000000,\n\n  // 剪映版本信息\n  SUPPORTED_VERSIONS: ['13.2.0', '110.0.0'],\n  DEFAULT_VERSION: '13.2.0',\n\n  // 平台信息\n  PLATFORM_INFO: {\n    app_id: 3704,\n    app_source: 'auto-generator',\n    platform: 'auto'\n  }\n};"]}