/**
 * 剪映草稿文件解析器
 * 解析 draft_content.json 文件，提取视频的详细信息
 */
interface CanvasConfig {
    height: number;
    width: number;
    ratio: string;
}
interface CropInfo {
    lower_left_x: number;
    lower_left_y: number;
    lower_right_x: number;
    lower_right_y: number;
    upper_left_x: number;
    upper_left_y: number;
    upper_right_x: number;
    upper_right_y: number;
}
interface VideoClip {
    id: string;
    material_name: string;
    path: string;
    duration: number;
    width: number;
    height: number;
    has_audio: boolean;
    crop: CropInfo;
    crop_ratio: string;
    crop_scale: number;
    type: string;
    source: number;
    is_ai_generate_content: boolean;
    is_copyright: boolean;
}
interface AudioClip {
    id: string;
    name: string;
    path: string;
    duration: number;
    type: string;
    source_platform: number;
    is_ai_clone_tone: boolean;
    is_text_edit_overdub: boolean;
}
interface TrackSegment {
    clip?: {
        alpha: number;
        flip: {
            horizontal: boolean;
            vertical: boolean;
        };
        rotation: number;
        scale: {
            x: number;
            y: number;
        };
        transform: {
            x: number;
            y: number;
        };
    };
    material_id: string;
    target_timerange: {
        duration: number;
        start: number;
    };
    source_timerange?: {
        duration: number;
        start: number;
    } | null;
}
interface Track {
    id: string;
    name: string;
    attribute: number;
    flag: number;
    is_default_name: boolean;
    segments: TrackSegment[];
}
interface Transform {
    position: {
        x: number;
        y: number;
    };
    rotation: number;
    scale: {
        x: number;
        y: number;
    };
}
interface TimeRange {
    start: number;
    duration: number;
    durationSeconds: number;
}
interface AppInfo {
    appId: string;
    version: string;
    platform: string;
}
interface Statistics {
    totalVideoClips: number;
    totalAudioClips: number;
    totalTracks: number;
    totalSegments: number;
    uniqueVideoFiles: number;
    uniqueAudioFiles: number;
}
interface VideoInfo {
    projectId: string;
    projectDuration: number;
    projectDurationSeconds: number;
    fps: number;
    canvasSize: {
        width: number;
        height: number;
        ratio: string;
    };
    videoClips: Array<{
        id: string;
        fileName: string;
        filePath: string;
        duration: number;
        durationSeconds: number;
        resolution: {
            width: number;
            height: number;
            aspectRatio: string;
        };
        hasAudio: boolean;
        cropInfo: {
            ratio: string;
            scale: number;
            coordinates: CropInfo;
        };
        isAIGenerated: boolean;
        isCopyrighted: boolean;
    }>;
    audioClips: Array<{
        id: string;
        name: string;
        filePath: string;
        duration: number;
        durationSeconds: number;
        type: string;
        isAIClone: boolean;
        isTextOverdub: boolean;
    }>;
    tracks: Array<{
        id: string;
        name: string;
        type: string;
        segmentCount: number;
        segments: Array<{
            materialId: string;
            timeRange: {
                start: number;
                duration: number;
                startSeconds: number;
                durationSeconds: number;
            };
            sourceTimeRange: {
                start: number;
                duration: number;
                startSeconds: number;
                durationSeconds: number;
            };
            transform: {
                alpha: number;
                rotation: number;
                scale: {
                    x: number;
                    y: number;
                };
                position: {
                    x: number;
                    y: number;
                };
                flip: {
                    horizontal: boolean;
                    vertical: boolean;
                };
            };
        }>;
    }>;
    appInfo: {
        appId: number;
        appSource: string;
        appVersion: string;
        platform: string;
        os: string;
        deviceId: string;
    };
    statistics: {
        totalVideoClips: number;
        totalAudioClips: number;
        totalTracks: number;
        totalSegments: number;
        uniqueVideoFiles: string[];
        uniqueAudioFiles: string[];
    };
}
/**
 * 微秒转换为秒
 */
declare function microsecondsToSeconds(microseconds: number): number;
/**
 * 计算宽高比
 */
declare function calculateAspectRatio(width: number, height: number): string;
/**
 * 解析剪映草稿文件
 */
declare function parseJianyingDraft(filePath: string): VideoInfo;
/**
 * 格式化输出视频信息
 */
declare function formatVideoInfo(info: VideoInfo): void;
/**
 * 导出为JSON文件
 */
declare function exportToJson(info: VideoInfo, outputPath: string): void;

declare function secondsToMicroseconds(seconds: number): number;

/**
 * 增强版剪映草稿文件解析器
 * 提供更多分析功能和可视化输出
 */

interface EnhancedAnalysis {
    basicInfo: VideoInfo;
    analysis: {
        complexity: {
            score: number;
            level: 'Simple' | 'Medium' | 'Complex' | 'Very Complex';
            factors: string[];
        };
        timeline: {
            totalDuration: number;
            segmentCount: number;
            averageSegmentLength: number;
            shortestSegment: number;
            longestSegment: number;
        };
        materials: {
            videoFileUsage: Array<{
                filePath: string;
                usageCount: number;
                totalDuration: number;
                segments: Array<{
                    trackId: string;
                    startTime: number;
                    duration: number;
                }>;
            }>;
            audioFileUsage: Array<{
                filePath: string;
                usageCount: number;
                totalDuration: number;
            }>;
        };
        editing: {
            hasTransformations: boolean;
            hasScaling: boolean;
            hasRotation: boolean;
            hasPositionChanges: boolean;
            hasFlipping: boolean;
            transformationCount: number;
        };
    };
    recommendations: Array<{
        type: string;
        description: string;
    }>;
}
/**
 * 执行增强分析
 */
declare function performEnhancedAnalysis(filePath: string): EnhancedAnalysis;
/**
 * 格式化增强分析结果
 */
declare function formatEnhancedAnalysis(analysis: EnhancedAnalysis): void;

declare function analyzeJianyingProject(filePath: string): EnhancedAnalysis;
declare function analyzeJianyingProjectFromData(videoInfo: VideoInfo): EnhancedAnalysis;
declare function generateRecommendationsFromAnalysis(analysis: any): any[];

type ComplexityAnalysis = EnhancedAnalysis['analysis']['complexity'];
type TimelineAnalysis = EnhancedAnalysis['analysis']['timeline'];
type MaterialUsage = EnhancedAnalysis['analysis']['materials'];
type EditingFeatures = EnhancedAnalysis['analysis']['editing'];
type Recommendation = {
    type: string;
    description: string;
};

/**
 * 自动生成剪映草稿文件生成器
 * 扫描指定目录的视频文件，自动生成 draft_content.json
 */
interface VideoFileInfo {
    filePath: string;
    fileName: string;
    extension: string;
    size: number;
    isVideo: boolean;
    isAudio: boolean;
}
interface GeneratedVideoClip {
    aigc_type: string;
    audio_fade: null;
    cartoon_path: string;
    category_id: string;
    category_name: string;
    check_flag: number;
    crop: {
        lower_left_x: number;
        lower_left_y: number;
        lower_right_x: number;
        lower_right_y: number;
        upper_left_x: number;
        upper_left_y: number;
        upper_right_x: number;
        upper_right_y: number;
    };
    crop_ratio: string;
    crop_scale: number;
    duration: number;
    extra_type_option: number;
    formula_id: string;
    freeze: null;
    has_audio: boolean;
    height: number;
    id: string;
    intensifies_audio_path: string;
    intensifies_path: string;
    is_ai_generate_content: boolean;
    is_copyright: boolean;
    is_text_edit_overdub: boolean;
    is_unified_beauty_mode: boolean;
    local_id: string;
    local_material_id: string;
    material_id: string;
    material_name: string;
    material_url: string;
    matting: {
        flag: number;
        has_use_quick_brush: boolean;
        has_use_quick_eraser: boolean;
        interactiveTime: any[];
        path: string;
        strokes: any[];
    };
    media_path: string;
    object_locked: null;
    origin_material_id: string;
    path: string;
    picture_from: string;
    picture_set_category_id: string;
    picture_set_category_name: string;
    request_id: string;
    reverse_intensifies_path: string;
    reverse_path: string;
    smart_motion: null;
    source: number;
    source_platform: number;
    stable: {
        matrix_path: string;
        stable_level: number;
        time_range: {
            duration: number;
            start: number;
        };
    };
    team_id: string;
    type: string;
    video_algorithm: {
        algorithms: any[];
        deflicker: null;
        motion_blur_config: null;
        noise_reduction: null;
        path: string;
        time_range: null;
    };
    width: number;
}
interface GeneratedAudioClip {
    app_id: number;
    category_id: string;
    category_name: string;
    check_flag: number;
    copyright_limit_type: string;
    duration: number;
    effect_id: string;
    formula_id: string;
    id: string;
    intensifies_path: string;
    is_ai_clone_tone: boolean;
    is_text_edit_overdub: boolean;
    is_ugc: boolean;
    local_material_id: string;
    music_id: string;
    name: string;
    path: string;
    query: string;
    request_id: string;
    resource_id: string;
    search_id: string;
    source_from: string;
    source_platform: number;
    team_id: string;
    text_id: string;
    tone_category_id: string;
    tone_category_name: string;
    tone_effect_id: string;
    tone_effect_name: string;
    tone_platform: string;
    tone_second_category_id: string;
    tone_second_category_name: string;
    tone_speaker: string;
    tone_type: string;
    type: string;
    video_id: string;
    wave_points: any[];
}
interface GeneratedTrackSegment {
    clip?: {
        alpha: number;
        flip: {
            horizontal: boolean;
            vertical: boolean;
        };
        rotation: number;
        scale: {
            x: number;
            y: number;
        };
        transform: {
            x: number;
            y: number;
        };
    };
    material_id: string;
    target_timerange: {
        duration: number;
        start: number;
    };
    source_timerange?: {
        duration: number;
        start: number;
    } | null;
}
interface GeneratedTrack {
    attribute: number;
    flag: number;
    id: string;
    segments: GeneratedTrackSegment[];
    type: string;
}
interface GeneratedDraft {
    canvas_config: {
        height: number;
        ratio: string;
        width: number;
    };
    color_space: number;
    config: {
        adjust_max_index: number;
        attachment_info: any[];
        combination_max_index: number;
        export_range: null;
        extract_audio_last_index: number;
        lyrics_recognition_id: string;
        lyrics_sync: boolean;
        lyrics_taskinfo: any[];
        maintrack_adsorb: boolean;
        material_save_mode: number;
        multi_language_current: string;
        multi_language_list: any[];
        multi_language_main: string;
        multi_language_mode: string;
        original_sound_last_index: number;
        record_audio_last_index: number;
        sticker_max_index: number;
        subtitle_keywords_config: null;
        subtitle_recognition_id: string;
        subtitle_sync: boolean;
        subtitle_taskinfo: any[];
        system_font_list: any[];
        text_recognition_id: string;
        text_sync: boolean;
        text_taskinfo: any[];
        video_recognition_id: string;
        video_sync: boolean;
        video_taskinfo: any[];
    };
    create_time: number;
    draft_fold_path: string;
    draft_id: string;
    draft_name: string;
    draft_removable_storage_device: string;
    duration: number;
    fps: number;
    id: string;
    last_modified_platform: {
        app_id: number;
        app_source: string;
        app_version: string;
        device_id: string;
        hard_disk_id: string;
        mac_address: string;
        os_version: string;
        platform: string;
        screen_height: number;
        screen_width: number;
    };
    materials: {
        audio_effects: any[];
        audio_fades: any[];
        audio_track_indexes: any[];
        audios: any[];
        beats: any[];
        canvases: any[];
        chromas: any[];
        color_curves: any[];
        color_wheels: any[];
        effects: any[];
        flowers: any[];
        handwrites: any[];
        hsl: any[];
        images: any[];
        keyframes: any[];
        masks: any[];
        material_animations: any[];
        placeholders: any[];
        plugin_effects: any[];
        shapes: any[];
        sounds: any[];
        stickers: any[];
        texts: any[];
        videos: any[];
    };
    mutable_config: null;
    name: string;
    new_version: string;
    platform: {
        app_id: number;
        app_source: string;
        app_version: string;
        device_id: string;
        hard_disk_id: string;
        mac_address: string;
        os_version: string;
        platform: string;
        screen_height: number;
        screen_width: number;
    };
    relationships: any[];
    revert_generate_segment_config: null;
    source: string;
    tracks: GeneratedTrack[];
    update_time: number;
    version: string;
}
/**
 * 扫描目录获取所有媒体文件
 */
declare function scanDirectory(dirPath: string): VideoFileInfo[];
/**
 * 检查是否安装了 ffprobe
 */
declare function checkFFProbe(): boolean;
/**
 * 使用 ffprobe 获取真实的视频信息
 */
declare function getVideoInfoWithFFProbe(filePath: string): {
    duration: number;
    width: number;
    height: number;
    hasAudio: boolean;
} | null;
/**
 * 生成视频素材
 */
declare function generateVideoClips(videoFiles: VideoFileInfo[], useFFProbe?: boolean): GeneratedVideoClip[];
/**
 * 生成音频素材
 */
declare function generateAudioClips(audioFiles: VideoFileInfo[], useFFProbe?: boolean): GeneratedAudioClip[];
/**
 * 生成轨道和片段
 */
declare function generateTracks(videoClips: GeneratedVideoClip[], audioClips: GeneratedAudioClip[]): GeneratedTrack[];
/**
 * 生成完整的草稿文件
 */
declare function generateDraft(files: VideoFileInfo[], options: {
    canvasWidth?: number;
    canvasHeight?: number;
    fps?: number;
    projectName?: string;
    useFFProbe?: boolean;
}): GeneratedDraft;

/**
 * @mixvideo/jianying - 剪映草稿文件工具包
 *
 * 提供剪映（CapCut）草稿文件的解析、分析和生成功能
 */

declare const JianyingUtils: {
    microsecondsToSeconds: (microseconds: number) => number;
    secondsToMicroseconds: (seconds: number) => number;
    calculateAspectRatio: (width: number, height: number) => string;
    VIDEO_EXTENSIONS: string[];
    AUDIO_EXTENSIONS: string[];
    isVideoFile: (filename: string) => boolean;
    isAudioFile: (filename: string) => boolean;
    isMediaFile: (filename: string) => boolean;
};
declare const JIANYING_CONSTANTS: {
    DEFAULT_FPS: number;
    DEFAULT_CANVAS_WIDTH: number;
    DEFAULT_CANVAS_HEIGHT: number;
    DEFAULT_ASPECT_RATIO: string;
    MICROSECONDS_PER_SECOND: number;
    SUPPORTED_VERSIONS: string[];
    DEFAULT_VERSION: string;
    PLATFORM_INFO: {
        app_id: number;
        app_source: string;
        platform: string;
    };
};

export { type AppInfo, type AudioClip, type CanvasConfig, type ComplexityAnalysis, type EditingFeatures, type EnhancedAnalysis, type GeneratedAudioClip, type GeneratedDraft, type GeneratedTrack, type GeneratedTrackSegment, type GeneratedVideoClip, JIANYING_CONSTANTS, JianyingUtils, type MaterialUsage, type Recommendation, type Statistics, type TimeRange, type TimelineAnalysis, type Track, type TrackSegment, type Transform, type VideoClip, type VideoFileInfo, type VideoInfo, analyzeJianyingProject, analyzeJianyingProjectFromData, calculateAspectRatio, checkFFProbe, exportToJson, formatEnhancedAnalysis, formatVideoInfo, generateAudioClips, generateDraft, generateRecommendationsFromAnalysis, generateTracks, generateVideoClips, getVideoInfoWithFFProbe, microsecondsToSeconds, parseJianyingDraft, performEnhancedAnalysis, scanDirectory, secondsToMicroseconds };
