{"lastValidatedTimestamp": 1751006970480, "projects": {"/home/<USER>/mixvideo": {"name": "mixvideo", "version": "1.0.0"}, "/home/<USER>/mixvideo/packages/gemini": {"name": "@mixvideo/gemini", "version": "1.0.0"}, "/home/<USER>/mixvideo/packages/shared": {"name": "@mixvideo/shared", "version": "1.0.0"}, "/home/<USER>/mixvideo/packages/video-analyzer": {"name": "@mixvideo/video-analyzer", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": true}