# Installation
> `npm install --save @types/mime-types`

# Summary
This package contains type definitions for mime-types (https://github.com/jshttp/mime-types#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime-types.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime-types/index.d.ts)
````ts
export function lookup(filenameOrExt: string): string | false;

export function contentType(filenameOrExt: string): string | false;

export function extension(typeString: string): string | false;

export function charset(typeString: string): string | false;

export namespace charsets {
    const lookup: typeof charset;
}

export const types: { [key: string]: string };

export const extensions: { [key: string]: string[] };

````

### Additional Details
 * Last updated: Sat, 07 Jun 2025 02:15:25 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/Perlmint), and [<PERSON>](https://github.com/bjohansebas).
