#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/mixvideo/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/bin/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/ejs@3.1.10/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/mixvideo/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/bin/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/ejs@3.1.10/node_modules:/home/<USER>/mixvideo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../ejs/bin/cli.js" "$@"
else
  exec node  "$basedir/../../../ejs/bin/cli.js" "$@"
fi
