/**
 * @mixvideo/gemini 是大模型gemini使用工具包
 * 
 * 现在要实现一个功能 扫描指定文件夹内的所有的视频 
 * 然后将视频喂给 gemini (需要先上传)
 * 
 * 视频内容分析
自动扫描文件夹中的所有MP4视频文件
提供两种分析模式：
GPT-4模式：逐帧提取分析，对每秒的关键帧进行详细描述
Gemini模式：整体视频分析，结合音视频内容
2. 产品特征识别
重点分析产品相关内容：外观、材质、功能特征
识别展示的功能和使用场景
分析人物动作、场景描述、整体氛围
3. 智能文件夹匹配
基于视频分析结果，自动推荐合适的分组文件夹
使用AI分析视频内容与文件夹名称的语义相关性
提供置信度评分和匹配原因说明
4. 结果输出
生成详细的XML格式分析报告
可视化展示匹配结果
支持保存分析结果到本地
🛠️ 使用场景
这个工具特别适用于：
电商产品视频管理：自动分析产品展示视频的特征
营销素材整理：根据视频内容自动归类到对应文件夹
内容运营：批量分析视频素材，提取关键信息
视频库管理：智能化视频分类和检索
💡 工作流程
选择包含视频文件的文件夹
选择分析模型（GPT或Gemini）
程序自动分析所有视频内容
生成分析报告
可选择进行文件夹匹配，获得分类建议
这是一个很实用的视频内容自动化分析工具，可以大大提高视频素材管理的效率！
 */