import axios from "axios";
import { createDefaultGoogleGenaiClient } from "./google-genai-client";
export function useGeminiAxios() {
    return axios.create({
        baseURL: `https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run`,
        headers: {
            Authorization: `Bearer bowong7777`,
        }
    });
}
export async function useGeminiAccessToken() {
    const geminiAxios = useGeminiAxios();
    const token = await geminiAxios.request({
        method: `get`,
        url: `/google/access-token`
    }).then(res => res.data);
    return token;
}
export async function uploadFileToGemini(bucket, prefix, file) {
    const genminiAxios = useGeminiAxios();
    const result = await genminiAxios.request({
        method: `post`,
        url: `/google/vertex-ai/upload`,
        params: {
            bucket: bucket,
            prefix: prefix
        },
        data: {
            file: file
        },
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
    console.log({ data: result.data });
    return result.data;
}
export async function useGemini() {
    const token = await useGeminiAccessToken();
    return createDefaultGoogleGenaiClient(token.access_token);
}
//# sourceMappingURL=createGeminiAxios.js.map