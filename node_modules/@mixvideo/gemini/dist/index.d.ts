import FormData from 'form-data';

/**
 * TypeScript 版本的 GoogleGenaiClient
 * 对应 Python 版本的 GoogleAuthUtils.GoogleGenaiClient
 */
interface GoogleGenaiClientConfig {
    cloudflareProjectId: string;
    cloudflareGatewayId: string;
    googleProjectId: string;
    regions: string[];
    accessToken: string;
}
interface ContentPart {
    text?: string;
    inlineData?: {
        mimeType: string;
        data: string;
    };
    fileData?: {
        mimeType: string;
        fileUri: string;
    };
}
interface Content {
    role?: 'user' | 'model';
    parts: ContentPart[];
}
interface GenerationConfig {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens?: number;
    stopSequences?: string[];
    candidateCount?: number;
}
interface GenerateContentCandidate {
    content: Content;
    finishReason?: string;
    index?: number;
    safetyRatings?: Array<{
        category: string;
        probability: string;
    }>;
}
interface GenerateContentResponse {
    candidates?: GenerateContentCandidate[];
    promptFeedback?: {
        safetyRatings?: Array<{
            category: string;
            probability: string;
        }>;
        blockReason?: string;
    };
    usageMetadata?: {
        promptTokenCount?: number;
        candidatesTokenCount?: number;
        totalTokenCount?: number;
    };
}
interface GenerateContentResult {
    response: GenerateContentResponse | null;
    statusCode: number;
}
/**
 * Google Generative AI 客户端
 * 通过 Cloudflare Gateway 调用 Google Vertex AI API
 */
declare class GoogleGenaiClient {
    private config;
    constructor(config: GoogleGenaiClientConfig);
    /**
     * 计算属性：通过 Cloudflare Gateway 调用的 URL
     */
    get gatewayUrl(): string;
    /**
     * 生成内容
     * @param modelId 模型 ID，如 'gemini-2.5-flash'
     * @param contents 内容数组
     * @param config 生成配置
     * @param timeout 超时时间（秒）
     * @returns 生成结果和状态码
     */
    generateContent(contents: Content[], modelId?: string, config?: GenerationConfig, timeout?: number): Promise<GenerateContentResult>;
    /**
     * 简化的文本生成方法
     * @param modelId 模型 ID
     * @param prompt 文本提示
     * @param config 生成配置
     * @returns 生成的文本内容
     */
    generateText(prompt: string, modelId?: string, config?: GenerationConfig): Promise<string | null>;
    /**
     * 获取配置信息
     */
    getConfig(): GoogleGenaiClientConfig;
    /**
     * 更新访问令牌
     */
    updateAccessToken(accessToken: string): void;
}

declare function useGeminiAccessToken(): Promise<{
    access_token: string;
    expires_in: number;
    token_type: string;
}>;

interface GeminiUploadResult {
    kind: string;
    id: string;
    selfLink: string;
    mediaLink: string;
    name: string;
    bucket: string;
    generation: string;
    metageneration: string;
    contentType: string;
    storageClass: string;
    size: 11825150;
    md5Hash: string;
    crc32c: string;
    etag: string;
    timeCreated: string;
    updated: string;
    timeStorageClassUpdated: string;
    timeFinalized: string;
    urn: string;
}
declare function uploadFileToGemini(bucket: string, prefix: string, formData: FormData): Promise<GeminiUploadResult>;
declare function useGemini(): Promise<GoogleGenaiClient>;

export { type GeminiUploadResult, GoogleGenaiClient, uploadFileToGemini, useGemini, useGeminiAccessToken };
