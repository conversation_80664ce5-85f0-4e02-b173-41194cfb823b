export declare function useGeminiAxios(): import("axios").AxiosInstance;
export declare function useGeminiAccessToken(): Promise<{
    access_token: string;
    expires_in: number;
    token_type: string;
}>;
export declare function uploadFileToGemini(bucket: string, prefix: string, file: Buffer): Promise<any>;
export declare function useGemini(): Promise<import("./google-genai-client").GoogleGenaiClient>;
//# sourceMappingURL=createGeminiAxios.d.ts.map