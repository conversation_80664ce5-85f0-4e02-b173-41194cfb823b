hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.7':
    '@babel/compat-data': private
  '@babel/core@7.27.7':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.7)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.7':
    '@babel/traverse': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.29.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@google/generative-ai@0.21.0':
    '@google/generative-ai': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(ts-node@10.9.2(@types/node@20.19.1)(typescript@5.8.3))':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.7':
    '@pkgr/core': private
  '@rollup/plugin-commonjs@25.0.8(rollup@4.44.1)':
    '@rollup/plugin-commonjs': private
  '@rollup/plugin-node-resolve@15.3.1(rollup@4.44.1)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-typescript@11.1.6(rollup@4.44.1)(tslib@2.8.1)(typescript@5.8.3)':
    '@rollup/plugin-typescript': private
  '@rollup/pluginutils@5.2.0(rollup@4.44.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@29.5.14':
    '@types/jest': private
  '@types/jsdom@20.0.1':
    '@types/jsdom': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mime-types@3.0.1':
    '@types/mime-types': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/project-service@8.35.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.35.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.35.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.35.0(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.35.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.35.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.35.0(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.35.0':
    '@typescript-eslint/visitor-keys': private
  abab@2.0.6:
    abab: private
  acorn-globals@7.0.1:
    acorn-globals: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  axios@1.10.0:
    axios: private
  babel-jest@29.7.0(@babel/core@7.27.7):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.7):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.27.7):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-from@1.1.2:
    buffer-from: private
  bundle-require@5.1.0(esbuild@0.25.5):
    bundle-require: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chokidar@4.0.3:
    chokidar: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  cliui@8.0.1:
    cliui: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@4.1.1:
    commander: private
  commondir@1.0.1:
    commondir: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  create-jest@29.7.0(@types/node@20.19.1)(ts-node@10.9.2(@types/node@20.19.1)(typescript@5.8.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssom@0.5.0:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  data-urls@3.0.2:
    data-urls: private
  debug@4.4.1:
    debug: private
  decimal.js@10.5.0:
    decimal.js: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  delayed-stream@1.0.0:
    delayed-stream: private
  detect-newline@3.1.0:
    detect-newline: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  domexception@4.0.0:
    domexception: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.177:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  entities@6.0.1:
    entities: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  fix-dts-default-cjs-exports@1.0.1:
    fix-dts-default-cjs-exports: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.3:
    form-data: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@8.1.0:
    glob: private
  globals@14.0.0:
    globals: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  html-encoding-sniffer@3.0.0:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@7.0.5:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-module@1.0.0:
    is-module: private
  is-number@7.0.0:
    is-number: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-reference@1.2.1:
    is-reference: private
  is-stream@2.0.1:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@20.19.1)(ts-node@10.9.2(@types/node@20.19.1)(typescript@5.8.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@24.0.4)(ts-node@10.9.2(@types/node@20.19.1)(typescript@5.8.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-jsdom@29.7.0:
    jest-environment-jsdom: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jest@29.7.0(@types/node@20.19.1)(ts-node@10.9.2(@types/node@20.19.1)(typescript@5.8.3)):
    jest: private
  joycon@3.1.1:
    joycon: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdom@20.0.3:
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  locate-path@6.0.0:
    locate-path: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  mlly@1.7.4:
    mlly: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  natural-compare@1.4.0:
    natural-compare: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nwsapi@2.2.20:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  packages/gemini:
    '@mixvideo/gemini': private
  packages/shared:
    '@mixvideo/shared': private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5@7.3.0:
    parse5: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  pathe@2.0.3:
    pathe: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss-load-config@6.0.1(tsx@4.20.3):
    postcss-load-config: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  pretty-format@29.7.0:
    pretty-format: private
  prompts@2.4.2:
    prompts: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@18.3.1:
    react-is: private
  readdirp@4.1.2:
    readdirp: private
  require-directory@2.1.1:
    require-directory: private
  requires-port@1.0.0:
    requires-port: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rollup-plugin-dts@6.2.1(rollup@4.44.1)(typescript@5.8.3):
    rollup-plugin-dts: private
  rollup@4.44.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@3.0.7:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  source-map-support@0.5.13:
    source-map-support: private
  source-map@0.8.0-beta.0:
    source-map: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  symbol-tree@3.2.4:
    symbol-tree: private
  synckit@0.11.8:
    synckit: private
  test-exclude@6.0.0:
    test-exclude: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@3.0.0:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-jest@29.4.0(@babel/core@7.27.7)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.27.7))(jest-util@29.7.0)(jest@29.7.0(@types/node@20.19.1)(ts-node@10.9.2(@types/node@20.19.1)(typescript@5.8.3)))(typescript@5.8.3):
    ts-jest: private
  tslib@2.8.1:
    tslib: private
  tsup@8.5.0(tsx@4.20.3)(typescript@5.8.3):
    tsup: private
  tsx@4.20.3:
    tsx: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  undici-types@6.21.0:
    undici-types: private
  universalify@0.2.0:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  w3c-xmlserializer@4.0.0:
    w3c-xmlserializer: private
  walker@1.0.8:
    walker: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-encoding@2.0.0:
    whatwg-encoding: private
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: private
  whatwg-url@11.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.2:
    ws: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.2.1
pendingBuilds: []
prunedAt: Fri, 27 Jun 2025 01:21:42 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.44.1'
  - '@rollup/rollup-android-arm64@4.44.1'
  - '@rollup/rollup-darwin-arm64@4.44.1'
  - '@rollup/rollup-darwin-x64@4.44.1'
  - '@rollup/rollup-freebsd-arm64@4.44.1'
  - '@rollup/rollup-freebsd-x64@4.44.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.1'
  - '@rollup/rollup-linux-arm64-gnu@4.44.1'
  - '@rollup/rollup-linux-arm64-musl@4.44.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-musl@4.44.1'
  - '@rollup/rollup-linux-s390x-gnu@4.44.1'
  - '@rollup/rollup-win32-arm64-msvc@4.44.1'
  - '@rollup/rollup-win32-ia32-msvc@4.44.1'
  - '@rollup/rollup-win32-x64-msvc@4.44.1'
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
