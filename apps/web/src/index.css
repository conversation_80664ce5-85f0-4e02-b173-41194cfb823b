@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply box-border;
  }

  body {
    @apply font-sans bg-gradient-to-br from-primary-600 via-secondary-500 to-accent-500 min-h-screen text-gray-800 overflow-x-hidden;
    background-attachment: fixed;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

@layer components {
  .btn {
    @apply bg-gradient-to-r from-primary-500 via-primary-600 to-secondary-500 text-white border-none px-8 py-4 rounded-2xl cursor-pointer text-base font-bold transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-primary-500/40 mx-1 relative overflow-hidden;
    box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn:hover {
    @apply scale-105;
    box-shadow: 0 12px 40px rgba(14, 165, 233, 0.5);
  }

  .btn:active {
    @apply scale-95;
    transition: transform 0.1s ease;
  }

  .btn-secondary {
    @apply bg-white/95 backdrop-blur-sm text-gray-700 border-2 border-white/40 hover:bg-white hover:shadow-xl hover:shadow-white/30 hover:scale-105 hover:border-primary-300 px-6 py-3 rounded-xl font-semibold;
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
  }

  .btn-success {
    @apply bg-gradient-to-r from-success-500 to-success-600 text-white border-none px-8 py-4 rounded-2xl cursor-pointer text-base font-bold transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-success-500/40 mx-1 relative overflow-hidden;
    box-shadow: 0 8px 32px rgba(34, 197, 94, 0.3);
  }

  .btn-danger {
    @apply bg-gradient-to-r from-danger-500 to-danger-600 text-white border-none px-8 py-4 rounded-2xl cursor-pointer text-base font-bold transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-danger-500/40 mx-1 relative overflow-hidden;
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
  }

  .btn-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600 text-white border-none px-8 py-4 rounded-2xl cursor-pointer text-base font-bold transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-accent-500/40 mx-1 relative overflow-hidden;
    box-shadow: 0 8px 32px rgba(249, 115, 22, 0.3);
  }

  .card {
    @apply bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl shadow-black/20 border border-white/30;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.3);
  }

  .card:hover {
    @apply transform scale-[1.02];
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
  }
  
  .upload-area {
    @apply border-4 border-dashed border-white/50 rounded-3xl p-12 text-center bg-white/15 backdrop-blur-sm transition-all duration-500 cursor-pointer hover:border-primary-300 hover:bg-white/25 hover:shadow-2xl hover:scale-[1.02];
    box-shadow: 0 16px 48px rgba(255, 255, 255, 0.1);
  }

  .upload-area:hover {
    box-shadow: 0 20px 60px rgba(14, 165, 233, 0.2);
  }

  .upload-area.dragover {
    @apply border-primary-400 bg-primary-100/30 transform scale-105;
    box-shadow: 0 25px 80px rgba(14, 165, 233, 0.3);
  }

  .video-item {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl p-5 my-4 flex items-center justify-between animate-fade-in cursor-pointer hover:bg-white/90 hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border border-white/30;
  }
  
  .timeline {
    @apply bg-white/80 backdrop-blur-sm rounded-3xl p-8 my-8 min-h-[200px] border border-white/30 shadow-xl;
  }

  .preview-area {
    @apply bg-black/90 backdrop-blur-sm rounded-3xl aspect-video flex items-center justify-center text-white text-xl my-6 border border-white/20 shadow-2xl overflow-hidden;
  }

  .progress-bar {
    @apply w-full h-3 bg-white/20 rounded-full overflow-hidden my-4 backdrop-blur-sm;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 transition-all duration-500 rounded-full;
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.5);
  }

  /* 新增动画效果 */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 8s ease infinite;
  }
}

/* 自定义动画关键帧 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .card {
    @apply p-4 mx-2;
  }

  .upload-area {
    @apply p-6;
  }

  .btn {
    @apply px-4 py-2 text-sm;
  }
}
