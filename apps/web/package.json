{"name": "@mixvideo/web", "version": "1.0.0", "description": "MixVideo Web Application", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "npm run preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@mixvideo/shared": "file:../../packages/shared", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.4.19"}}