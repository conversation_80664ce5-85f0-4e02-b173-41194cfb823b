{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "outDir": "./dist", "esModuleInterop": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@mixvideo/*": ["./packages/*/src"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "dist", "build", ".next"]}