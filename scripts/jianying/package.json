{"name": "jianying-parser", "version": "1.0.0", "description": "剪映草稿文件解析器 - 提取视频项目的详细信息", "main": "parse-draft.js", "bin": {"jianying-parse": "./parse-draft.ts"}, "scripts": {"test": "ts-node test-parser.ts", "parse": "ts-node parse-draft.ts", "analyze": "ts-node enhanced-parser.ts", "build": "tsc parse-draft.ts", "demo": "npm run parse draft_content.json && npm run analyze draft_content.json"}, "keywords": ["jianying", "video", "parser", "draft", "剪映"], "author": "", "license": "MIT", "dependencies": {"@types/node": "^24.0.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}