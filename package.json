{"name": "mixvideo", "version": "1.0.0", "description": "TypeScript Monorepo for MixVideo", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"build": "npm run build --workspaces && tsc", "build:shared": "npm run build --workspace=packages/shared", "build:web": "npm run build --workspace=apps/web", "build:video-analyzer": "npm run build --workspace=packages/video-analyzer", "dev": "npm run dev --workspaces", "dev:shared": "npm run dev --workspace=packages/shared", "dev:web": "npm run dev --workspace=apps/web", "start": "node dist/main.js", "dev:main": "ts-node src/main.ts", "analyze": "npm run build:video-analyzer && npm run dev:main", "type-check": "npm run type-check --workspaces && tsc --noEmit", "clean": "npm run clean --workspaces && rm -rf dist", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format:lint": "npm run format && npm run lint:fix"}, "dependencies": {"@mixvideo/video-analyzer": "workspace:*"}, "devDependencies": {"@types/node": "^24.0.4", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "ts-node": "^10.9.1", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}