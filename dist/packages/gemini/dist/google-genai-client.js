"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleGenaiClient = void 0;
exports.createGoogleGenaiClient = createGoogleGenaiClient;
exports.createDefaultGoogleGenaiClient = createDefaultGoogleGenaiClient;
const axios_1 = __importDefault(require("axios"));
/**
 * Google Generative AI 客户端
 * 通过 Cloudflare Gateway 调用 Google Vertex AI API
 */
class GoogleGenaiClient {
    config;
    constructor(config) {
        this.config = config;
    }
    /**
     * 计算属性：通过 Cloudflare Gateway 调用的 URL
     */
    get gatewayUrl() {
        // 随机选择一个区域
        const randomRegion = this.config.regions[Math.floor(Math.random() * this.config.regions.length)];
        return `https://gateway.ai.cloudflare.com/v1/${this.config.cloudflareProjectId}/${this.config.cloudflareGatewayId}/google-vertex-ai/v1/projects/${this.config.googleProjectId}/locations/${randomRegion}/publishers/google/models`;
    }
    /**
     * 生成内容
     * @param modelId 模型 ID，如 'gemini-2.5-flash'
     * @param contents 内容数组
     * @param config 生成配置
     * @param timeout 超时时间（秒）
     * @returns 生成结果和状态码
     */
    async generateContent(contents, modelId = 'gemini-2.5-flash', config, timeout = 30) {
        try {
            // 构建请求体
            const requestBody = {
                contents,
                ...(config && { generationConfig: config })
            };
            // 排除空值并转换为 JSON
            const jsonBody = JSON.stringify(requestBody, (key, value) => {
                if (value === null || value === undefined) {
                    return undefined;
                }
                return value;
            }, 2);
            const url = `${this.gatewayUrl}/${modelId}:generateContent`;
            const response = await axios_1.default.post(url, jsonBody, {
                timeout: timeout * 1000, // 转换为毫秒
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.accessToken}`
                }
            });
            if (response.status !== 200) {
                return {
                    response: null,
                    statusCode: response.status
                };
            }
            return {
                response: response.data,
                statusCode: response.status
            };
        }
        catch (error) {
            if (error.response) {
                return {
                    response: null,
                    statusCode: error.response.status
                };
            }
            else if (error.request) {
                return {
                    response: null,
                    statusCode: 0
                };
            }
            else {
                return {
                    response: null,
                    statusCode: -1
                };
            }
        }
    }
    /**
     * 简化的文本生成方法
     * @param modelId 模型 ID
     * @param prompt 文本提示
     * @param config 生成配置
     * @returns 生成的文本内容
     */
    async generateText(prompt, modelId = 'gemini-2.5-flash', config) {
        const contents = [
            {
                role: 'user',
                parts: [{ text: prompt }]
            }
        ];
        const result = await this.generateContent(contents, modelId, config);
        if (result.statusCode === 200 && result.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
            return result.response.candidates[0].content.parts[0].text;
        }
        return null;
    }
    /**
     * 获取配置信息
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * 更新访问令牌
     */
    updateAccessToken(accessToken) {
        this.config.accessToken = accessToken;
    }
}
exports.GoogleGenaiClient = GoogleGenaiClient;
/**
 * 创建 GoogleGenaiClient 实例的工厂函数
 */
function createGoogleGenaiClient(config) {
    return new GoogleGenaiClient(config);
}
/**
 * 使用默认配置创建客户端
 */
function createDefaultGoogleGenaiClient(accessToken) {
    return new GoogleGenaiClient({
        cloudflareProjectId: '67720b647ff2b55cf37ba3ef9e677083',
        cloudflareGatewayId: 'bowong-dev',
        googleProjectId: 'gen-lang-client-0413414134',
        regions: ['us-central1'], // 使用稳定的区域
        accessToken
    });
}
//# sourceMappingURL=google-genai-client.js.map
