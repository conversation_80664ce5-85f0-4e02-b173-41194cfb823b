"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useGeminiAxios = useGeminiAxios;
exports.useGeminiAccessToken = useGeminiAccessToken;
exports.uploadFileToGemini = uploadFileToGemini;
exports.useGemini = useGemini;
const axios_1 = __importDefault(require("axios"));
const google_genai_client_1 = require("./google-genai-client");
const MODAL_URL = `https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run`;
function useGeminiAxios() {
    return axios_1.default.create({
        baseURL: MODAL_URL,
        headers: {
            Authorization: `Bearer bowong7777`,
        }
    });
}
async function useGeminiAccessToken() {
    const geminiAxios = useGeminiAxios();
    const token = await geminiAxios.request({
        method: `get`,
        url: `/google/access-token`
    }).then(res => res.data);
    return token;
}
async function uploadFileToGemini(bucket, prefix, formData) {
    const token = await useGeminiAccessToken();
    const genminiAxios = axios_1.default.create({
        baseURL: MODAL_URL,
        headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${token.access_token}`,
            [`x-google-api-key`]: token.access_token,
        }
    });
    const result = await genminiAxios.request({
        method: `post`,
        url: `/google/vertex-ai/upload`,
        params: {
            bucket: bucket,
            prefix: prefix
        },
        data: formData,
    });
    return result.data;
}
async function useGemini() {
    const token = await useGeminiAccessToken();
    return (0, google_genai_client_1.createDefaultGoogleGenaiClient)(token.access_token);
}
