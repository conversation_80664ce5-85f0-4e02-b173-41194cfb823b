"use strict";
/**
 * Video file uploader for Gemini AI
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VideoUploader = exports.DEFAULT_UPLOAD_CONFIG = void 0;
exports.uploadVideoToGemini = uploadVideoToGemini;
exports.uploadVideosToGemini = uploadVideosToGemini;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const util_1 = require("util");
const gemini_1 = require("@mixvideo/gemini");
const types_1 = require("./types");
const form_data_1 = __importDefault(require("form-data"));
const path_1 = require("path");
const mime_types_1 = __importDefault(require("mime-types"));
const readFile = (0, util_1.promisify)(fs.readFile);
const writeFile = (0, util_1.promisify)(fs.writeFile);
const stat = (0, util_1.promisify)(fs.stat);
const access = (0, util_1.promisify)(fs.access);
/**
 * Default upload configuration
 */
exports.DEFAULT_UPLOAD_CONFIG = {
    chunkSize: 10 * 1024 * 1024, // 10MB chunks
    maxRetries: 3,
    timeout: 300000, // 5 minutes
    onProgress: () => { },
    enableCache: true,
    cacheDir: '.video-upload-cache',
    cacheExpiry: 24 * 60 * 60 * 1000 // 24 hours
};
/**
 * Video uploader class
 */
class VideoUploader {
    config;
    constructor(config) {
        this.config = {
            ...exports.DEFAULT_UPLOAD_CONFIG,
            ...config
        };
    }
    /**
     * Upload a single video file to Gemini
     */
    async uploadVideo(videoFile) {
        const startTime = Date.now();
        const uploadId = this.generateUploadId(videoFile);
        const progress = {
            step: 'Preparing upload',
            progress: 0,
            currentFile: videoFile.name,
            bytesUploaded: 0,
            totalBytes: videoFile.size
        };
        this.config.onProgress(progress);
        try {
            // 检查本地缓存
            progress.step = 'Checking cache';
            progress.progress = 5;
            this.config.onProgress(progress);
            const cachedResult = await this.checkLocalCache(videoFile);
            if (cachedResult) {
                const uploadTime = Date.now() - startTime;
                progress.step = 'Using cached result';
                progress.progress = 100;
                progress.bytesUploaded = videoFile.size;
                this.config.onProgress(progress);
                return {
                    videoFile,
                    success: true,
                    gcsPath: cachedResult.urn,
                    uploadTime,
                    bytesUploaded: videoFile.size,
                    metadata: {
                        uploadId,
                        timestamp: new Date(),
                        checksum: await this.calculateChecksum(await this.readVideoFile(videoFile))
                    }
                };
            }
            // Validate file before upload
            await this.validateFileForUpload(videoFile);
            progress.step = 'Reading file';
            progress.progress = 10;
            this.config.onProgress(progress);
            // Read file content
            const fileBuffer = await this.readVideoFile(videoFile);
            const mimeType = this.getMimeType(videoFile.path);
            progress.step = 'Uploading to Gemini';
            progress.progress = 20;
            this.config.onProgress(progress);
            // Generate GCS path
            const gcsPath = this.generateGcsPath(videoFile);
            // Upload with retry logic
            const uploadResult = await this.uploadWithRetry(fileBuffer, videoFile, progress, mimeType, gcsPath);
            const uploadTime = Date.now() - startTime;
            progress.step = 'Upload completed';
            progress.progress = 100;
            progress.bytesUploaded = videoFile.size;
            this.config.onProgress(progress);
            return {
                videoFile,
                success: true,
                gcsPath: uploadResult.urn,
                uploadTime,
                bytesUploaded: videoFile.size,
                metadata: {
                    uploadId,
                    timestamp: new Date(),
                    checksum: await this.calculateChecksum(fileBuffer)
                }
            };
        }
        catch (error) {
            const uploadTime = Date.now() - startTime;
            return {
                videoFile,
                success: false,
                uploadTime,
                bytesUploaded: progress.bytesUploaded,
                error: this.getErrorMessage(error),
                metadata: {
                    uploadId,
                    timestamp: new Date()
                }
            };
        }
    }
    getErrorMessage(error) {
        return error instanceof Error ? error.message : String(error);
    }
    /**
     * Upload multiple video files
     */
    async uploadVideos(videoFiles) {
        const results = [];
        for (let i = 0; i < videoFiles.length; i++) {
            const videoFile = videoFiles[i];
            try {
                const result = await this.uploadVideo(videoFile);
                results.push(result);
                // Update overall progress
                const overallProgress = ((i + 1) / videoFiles.length) * 100;
                this.config.onProgress({
                    step: `Uploaded ${i + 1}/${videoFiles.length} files`,
                    progress: overallProgress,
                    currentFile: videoFile.name,
                    bytesUploaded: results.reduce((sum, r) => sum + r.bytesUploaded, 0),
                    totalBytes: videoFiles.reduce((sum, f) => sum + f.size, 0)
                });
            }
            catch (error) {
                results.push({
                    videoFile,
                    success: false,
                    uploadTime: 0,
                    bytesUploaded: 0,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        return results;
    }
    /**
     * Validate file before upload
     */
    async validateFileForUpload(videoFile) {
        // Check if file exists
        try {
            await stat(videoFile.path);
        }
        catch (error) {
            throw new types_1.VideoAnalyzerError(`File not found: ${videoFile.path}`, 'FILE_NOT_FOUND');
        }
        // Check file size
        if (videoFile.size === 0) {
            throw new types_1.VideoAnalyzerError(`File is empty: ${videoFile.path}`, 'EMPTY_FILE');
        }
        // Check if file is too large (Gemini has limits)
        const maxSize = 500 * 1024 * 1024; // 500MB
        if (videoFile.size > maxSize) {
            throw new types_1.VideoAnalyzerError(`File too large: ${videoFile.size} bytes (max: ${maxSize} bytes)`, 'FILE_TOO_LARGE');
        }
    }
    /**
     * Read video file into buffer
     */
    async readVideoFile(videoFile) {
        try {
            return await readFile(videoFile.path);
        }
        catch (error) {
            throw new types_1.VideoAnalyzerError(`Failed to read file: ${videoFile.path}`, 'READ_FAILED', error);
        }
    }
    getMimeType(fileName) {
        const extension = (0, path_1.extname)(fileName).slice(1); // 去掉点号
        /**
         * 'video/mp4', 'video/mpeg', 'video/mov', 'video/avi', 'video/x-flv', 'video/mpg',
                                    'video/webm', 'video/wmv', 'video/3gpp'
         */
        switch (extension) {
            case 'mp4':
                return `video/mp4`;
            default:
                return mime_types_1.default.lookup(extension) || 'application/octet-stream';
        }
    }
    /**
     * 将上传结果保存到本地缓存
     */
    async saveToLocalDb(videoFile, result) {
        if (!this.config.enableCache) {
            return;
        }
        try {
            // 确保缓存目录存在
            await this.ensureCacheDir();
            // 计算文件校验和
            const fileBuffer = await readFile(videoFile.path);
            const checksum = await this.calculateChecksum(fileBuffer);
            // 创建缓存条目
            const cacheEntry = {
                videoFile,
                result,
                timestamp: Date.now(),
                checksum
            };
            // 生成缓存文件路径
            const cacheKey = this.generateCacheKey(videoFile.path);
            const cacheFilePath = path.join(this.config.cacheDir, `${cacheKey}.json`);
            // 保存到缓存文件
            await writeFile(cacheFilePath, JSON.stringify(cacheEntry, null, 2), 'utf8');
            console.log(`✅ 缓存已保存: ${videoFile.name} -> ${cacheFilePath}`);
        }
        catch (error) {
            console.warn(`⚠️ 保存缓存失败: ${videoFile.name}`, error);
            // 缓存失败不应该影响主流程
        }
    }
    /**
     * 从本地缓存检查是否已上传
     */
    async checkLocalCache(videoFile) {
        if (!this.config.enableCache) {
            return null;
        }
        try {
            // 生成缓存文件路径
            const cacheKey = this.generateCacheKey(videoFile.path);
            const cacheFilePath = path.join(this.config.cacheDir, `${cacheKey}.json`);
            // 检查缓存文件是否存在
            await access(cacheFilePath, fs.constants.F_OK);
            // 读取缓存内容
            const cacheContent = await readFile(cacheFilePath, 'utf8');
            const cacheEntry = JSON.parse(cacheContent);
            // 检查缓存是否过期
            const now = Date.now();
            if (now - cacheEntry.timestamp > this.config.cacheExpiry) {
                console.log(`⏰ 缓存已过期: ${videoFile.name}`);
                // 删除过期缓存
                await fs.promises.unlink(cacheFilePath).catch(() => { });
                return null;
            }
            // 验证文件是否发生变化
            const fileBuffer = await readFile(videoFile.path);
            const currentChecksum = await this.calculateChecksum(fileBuffer);
            if (currentChecksum !== cacheEntry.checksum) {
                console.log(`🔄 文件已变更: ${videoFile.name}`);
                // 删除无效缓存
                await fs.promises.unlink(cacheFilePath).catch(() => { });
                return null;
            }
            console.log(`🎯 使用缓存结果: ${videoFile.name}`);
            return cacheEntry.result;
        }
        catch (error) {
            // 缓存不存在或读取失败
            return null;
        }
    }
    /**
     * 确保缓存目录存在
     */
    async ensureCacheDir() {
        try {
            await access(this.config.cacheDir, fs.constants.F_OK);
        }
        catch {
            // 目录不存在，创建它
            await fs.promises.mkdir(this.config.cacheDir, { recursive: true });
            console.log(`📁 创建缓存目录: ${this.config.cacheDir}`);
        }
    }
    /**
     * 生成缓存键
     */
    generateCacheKey(filePath) {
        // 使用文件路径的哈希作为缓存键
        const crypto = require('crypto');
        return crypto.createHash('md5').update(filePath).digest('hex');
    }
    /**
     * 清理过期的缓存文件
     */
    async cleanExpiredCache() {
        if (!this.config.enableCache) {
            return;
        }
        try {
            await this.ensureCacheDir();
            const files = await fs.promises.readdir(this.config.cacheDir);
            const now = Date.now();
            let cleanedCount = 0;
            for (const file of files) {
                if (!file.endsWith('.json'))
                    continue;
                const filePath = path.join(this.config.cacheDir, file);
                try {
                    const content = await readFile(filePath, 'utf8');
                    const cacheEntry = JSON.parse(content);
                    if (now - cacheEntry.timestamp > this.config.cacheExpiry) {
                        await fs.promises.unlink(filePath);
                        cleanedCount++;
                    }
                }
                catch (error) {
                    // 删除损坏的缓存文件
                    await fs.promises.unlink(filePath).catch(() => { });
                    cleanedCount++;
                }
            }
            if (cleanedCount > 0) {
                console.log(`🧹 清理了 ${cleanedCount} 个过期缓存文件`);
            }
        }
        catch (error) {
            console.warn('清理缓存失败:', error);
        }
    }
    /**
     * 获取缓存统计信息
     */
    async getCacheStats() {
        if (!this.config.enableCache) {
            return { totalFiles: 0, totalSize: 0, oldestEntry: null, newestEntry: null };
        }
        try {
            await this.ensureCacheDir();
            const files = await fs.promises.readdir(this.config.cacheDir);
            let totalFiles = 0;
            let totalSize = 0;
            let oldestTimestamp = Infinity;
            let newestTimestamp = 0;
            for (const file of files) {
                if (!file.endsWith('.json'))
                    continue;
                const filePath = path.join(this.config.cacheDir, file);
                try {
                    const stats = await stat(filePath);
                    const content = await readFile(filePath, 'utf8');
                    const cacheEntry = JSON.parse(content);
                    totalFiles++;
                    totalSize += stats.size;
                    oldestTimestamp = Math.min(oldestTimestamp, cacheEntry.timestamp);
                    newestTimestamp = Math.max(newestTimestamp, cacheEntry.timestamp);
                }
                catch (error) {
                    // 忽略损坏的文件
                }
            }
            return {
                totalFiles,
                totalSize,
                oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),
                newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)
            };
        }
        catch (error) {
            return { totalFiles: 0, totalSize: 0, oldestEntry: null, newestEntry: null };
        }
    }
    /**
     * Upload with retry logic
     */
    async uploadWithRetry(fileBuffer, videoFile, progress, mimeType, gcsPath) {
        let lastError = null;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                progress.step = `Upload attempt ${attempt}/${this.config.maxRetries}`;
                this.config.onProgress(progress);
                // Create FormData for file upload
                const formData = new form_data_1.default();
                formData.append('file', fileBuffer, {
                    filename: gcsPath,
                    contentType: mimeType
                });
                // Use the uploadFileToGemini function from @mixvideo/gemini
                const result = await (0, gemini_1.uploadFileToGemini)(this.config.bucketName, this.config.filePrefix, formData);
                // 保存到数据库
                await this.saveToLocalDb(videoFile, result);
                // Return the full upload result
                return result;
            }
            catch (error) {
                console.log(`Upload attempt ${attempt} failed:`, error);
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt < this.config.maxRetries) {
                    // Wait before retry with exponential backoff
                    const delay = Math.pow(2, attempt - 1) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw new types_1.VideoAnalyzerError(`Upload failed after ${this.config.maxRetries} attempts: ${lastError?.message}`, 'UPLOAD_FAILED', lastError);
    }
    /**
     * Generate GCS path for video file
     */
    generateGcsPath(videoFile) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const sanitizedName = videoFile.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        return `${this.config.filePrefix}${timestamp}_${sanitizedName}.${videoFile.format}`;
    }
    /**
     * Generate unique upload ID
     */
    generateUploadId(videoFile) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        const sanitizedName = videoFile.name.replace(/[^a-zA-Z0-9]/g, '_');
        return `upload_${timestamp}_${sanitizedName}_${random}`;
    }
    /**
     * Calculate file checksum for verification
     */
    async calculateChecksum(buffer) {
        const crypto = await Promise.resolve().then(() => __importStar(require('crypto')));
        return crypto.createHash('md5').update(buffer).digest('hex');
    }
    /**
     * Get upload statistics
     */
    getUploadStatistics(results) {
        const totalFiles = results.length;
        const successfulUploads = results.filter(r => r.success).length;
        const failedUploads = totalFiles - successfulUploads;
        const totalBytesUploaded = results.reduce((sum, r) => sum + r.bytesUploaded, 0);
        const totalUploadTime = results.reduce((sum, r) => sum + r.uploadTime, 0);
        const averageUploadTime = totalFiles > 0 ? totalUploadTime / totalFiles : 0;
        const successRate = totalFiles > 0 ? (successfulUploads / totalFiles) * 100 : 0;
        return {
            totalFiles,
            successfulUploads,
            failedUploads,
            totalBytesUploaded,
            averageUploadTime,
            successRate
        };
    }
    /**
     * Update upload configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Get current upload configuration
     */
    getConfig() {
        return { ...this.config };
    }
}
exports.VideoUploader = VideoUploader;
/**
 * Convenience function to upload a single video
 */
async function uploadVideoToGemini(videoFile, config) {
    const uploader = new VideoUploader(config);
    return uploader.uploadVideo(videoFile);
}
/**
 * Convenience function to upload multiple videos
 */
async function uploadVideosToGemini(videoFiles, config) {
    const uploader = new VideoUploader(config);
    return uploader.uploadVideos(videoFiles);
}
