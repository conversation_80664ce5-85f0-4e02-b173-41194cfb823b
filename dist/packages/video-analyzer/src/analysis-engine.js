"use strict";
/**
 * Core video analysis engine using Gemini AI
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisEngine = exports.ANALYSIS_PROMPTS = exports.DEFAULT_ANALYSIS_CACHE_CONFIG = void 0;
exports.analyzeVideoWithGemini = analyzeVideoWithGemini;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const util_1 = require("util");
const gemini_1 = require("@mixvideo/gemini");
const types_1 = require("./types");
const readFile = (0, util_1.promisify)(fs.readFile);
const writeFile = (0, util_1.promisify)(fs.writeFile);
const access = (0, util_1.promisify)(fs.access);
/**
 * Default analysis cache configuration
 */
exports.DEFAULT_ANALYSIS_CACHE_CONFIG = {
    enableCache: true,
    cacheDir: '.video-analysis-cache',
    cacheExpiry: 7 * 24 * 60 * 60 * 1000 // 7 days
};
/**
 * Analysis prompts for different types of content analysis
 */
exports.ANALYSIS_PROMPTS = {
    COMPREHENSIVE: `请对这个视频进行全面分析，包括：
1. 场景检测：识别视频中的不同场景，包括开始时间、结束时间和场景描述
2. 物体识别：识别视频中出现的主要物体、人物和元素
3. 内容总结：提供视频的整体描述、关键亮点和主要主题
4. 情感基调：分析视频的情感氛围和风格
5. 关键词提取：提取最相关的关键词

请以JSON格式返回结果，包含scenes、objects、summary等字段。`,
    PRODUCT_FOCUSED: `请专门分析这个视频中的产品相关内容：
1. 产品外观：颜色、形状、尺寸、风格
2. 材质分析：识别产品使用的材料
3. 功能特征：产品展示的功能和特性
4. 使用场景：产品的使用环境和场景
5. 目标受众：分析产品的目标用户群体
6. 品牌元素：识别品牌标识、logo等元素

请以JSON格式返回详细的产品分析结果。`,
    SCENE_DETECTION: `请详细分析视频中的场景变化：
1. 识别每个独立的场景
2. 标记场景的开始和结束时间
3. 描述每个场景的内容和特征
4. 评估场景转换的流畅性
5. 识别关键帧时间点

请以JSON格式返回场景分析结果。`,
    OBJECT_DETECTION: `请识别和分析视频中的所有重要物体：
1. 物体名称和类别
2. 物体在视频中出现的时间范围
3. 物体的重要性和相关性评分
4. 物体之间的关系和交互
5. 物体的属性和特征

请以JSON格式返回物体检测结果。`
};
/**
 * Video analysis engine class
 */
class AnalysisEngine {
    geminiClient = null;
    cacheConfig;
    constructor(cacheConfig = {}) {
        // Gemini client will be initialized when needed
        this.cacheConfig = {
            ...exports.DEFAULT_ANALYSIS_CACHE_CONFIG,
            ...cacheConfig
        };
    }
    /**
     * Initialize Gemini client
     */
    async initializeGeminiClient() {
        if (!this.geminiClient) {
            this.geminiClient = await (0, gemini_1.useGemini)();
        }
    }
    /**
     * Analyze video using Gemini AI
     */
    async analyzeVideo(videoFile, gcsPath, mode, options = {}, onProgress) {
        const startTime = Date.now();
        const progress = {
            step: 'Initializing analysis',
            progress: 0,
            currentFile: videoFile.name,
            stage: 'processing'
        };
        onProgress?.(progress);
        try {
            await this.initializeGeminiClient();
            progress.step = 'Preparing analysis prompts';
            progress.progress = 10;
            onProgress?.(progress);
            // Generate analysis prompts based on options
            const prompts = this.generateAnalysisPrompts(options);
            progress.step = 'Analyzing video content';
            progress.progress = 20;
            progress.stage = 'analysis';
            onProgress?.(progress);
            // Perform comprehensive analysis
            const analysisResults = await this.performComprehensiveAnalysis(videoFile, gcsPath, prompts, options, onProgress);
            progress.step = 'Processing analysis results';
            progress.progress = 90;
            onProgress?.(progress);
            // Build final result
            const result = {
                metadata: await this.buildVideoMetadata(videoFile),
                analysisMode: mode,
                scenes: analysisResults.scenes || [],
                objects: analysisResults.objects || [],
                productFeatures: analysisResults.productFeatures,
                summary: analysisResults.summary || this.createDefaultSummary(),
                frameAnalysis: analysisResults.frameAnalysis,
                analyzedAt: new Date(),
                processingTime: Date.now() - startTime,
                qualityMetrics: this.calculateQualityMetrics(analysisResults)
            };
            progress.step = 'Analysis completed';
            progress.progress = 100;
            progress.stage = 'complete';
            onProgress?.(progress);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new types_1.VideoAnalyzerError(`Analysis failed for ${videoFile.name}: ${errorMessage}`, 'ANALYSIS_FAILED', error);
        }
    }
    /**
     * Generate analysis prompts based on options
     */
    generateAnalysisPrompts(options) {
        const prompts = [];
        if (options.enableSceneDetection) {
            prompts.push(exports.ANALYSIS_PROMPTS.SCENE_DETECTION);
        }
        if (options.enableObjectDetection) {
            prompts.push(exports.ANALYSIS_PROMPTS.OBJECT_DETECTION);
        }
        if (options.enableProductAnalysis) {
            prompts.push(exports.ANALYSIS_PROMPTS.PRODUCT_FOCUSED);
        }
        if (options.enableSummarization || prompts.length === 0) {
            prompts.push(exports.ANALYSIS_PROMPTS.COMPREHENSIVE);
        }
        // Add custom prompts if provided
        if (options.customPrompts) {
            prompts.push(...options.customPrompts);
        }
        return prompts;
    }
    /**
     * Perform comprehensive analysis using multiple prompts
     */
    async performComprehensiveAnalysis(videoFile, gcsPath, prompts, options, onProgress) {
        const results = {
            scenes: [],
            objects: [],
            summary: null,
            productFeatures: null
        };
        for (let i = 0; i < prompts.length; i++) {
            const prompt = prompts[i];
            const progressPercent = 20 + ((i + 1) / prompts.length) * 60;
            onProgress?.({
                step: `Running analysis ${i + 1}/${prompts.length}`,
                progress: progressPercent,
                stage: 'analysis'
            });
            try {
                const analysisResult = await this.runSingleAnalysis(gcsPath, prompt, options, videoFile.path);
                this.mergeAnalysisResults(results, analysisResult);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.warn(`Analysis prompt ${i + 1} failed:`, errorMessage);
            }
        }
        return results;
    }
    /**
     * Run a single analysis with a specific prompt
     */
    async runSingleAnalysis(gcsPath, prompt, options, videoPath) {
        try {
            // 检查缓存
            if (videoPath) {
                const cachedResult = await this.checkAnalysisCache(videoPath, gcsPath, prompt, options);
                if (cachedResult) {
                    console.log(`🎯 使用缓存的分析结果: ${path.basename(videoPath)}`);
                    return cachedResult;
                }
            }
            // Create content array with video reference
            const contents = [
                {
                    role: 'user',
                    parts: [
                        {
                            text: prompt
                        },
                        {
                            fileData: {
                                mimeType: 'video/mp4', // Adjust based on actual video format
                                fileUri: gcsPath
                            }
                        }
                    ]
                }
            ];
            const response = await this.geminiClient.generateContent(contents, 'gemini-2.5-flash', {
                temperature: 0.3,
                maxOutputTokens: 4096,
                topP: 0.8
            });
            if (response.statusCode === 200 && response.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
                const responseText = response.response.candidates[0].content.parts[0].text;
                const parsedResult = this.parseAnalysisResponse(responseText);
                // 保存到缓存
                if (videoPath) {
                    await this.saveAnalysisCache(videoPath, gcsPath, prompt, options, parsedResult);
                }
                return parsedResult;
            }
            throw new Error('Invalid response from Gemini API');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new types_1.VideoAnalyzerError(`Gemini analysis failed: ${errorMessage}`, 'GEMINI_ANALYSIS_FAILED', error);
        }
    }
    /**
     * Parse analysis response from Gemini
     */
    parseAnalysisResponse(responseText) {
        try {
            // Try to extract JSON from the response
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            // If no JSON found, create structured response from text
            return this.parseTextResponse(responseText);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.warn('Failed to parse JSON response, using text parsing:', errorMessage);
            return this.parseTextResponse(responseText);
        }
    }
    /**
     * Parse text response when JSON parsing fails
     */
    parseTextResponse(text) {
        const result = {
            scenes: [],
            objects: [],
            summary: {
                description: text.substring(0, 500),
                highlights: [],
                topics: [],
                keywords: []
            }
        };
        // Extract key information using regex patterns
        const sceneMatches = text.match(/场景\s*\d+[：:](.*?)(?=场景\s*\d+|$)/g);
        if (sceneMatches) {
            result.scenes = sceneMatches.map((match, index) => ({
                startTime: index * 10, // Estimate timing
                endTime: (index + 1) * 10,
                duration: 10,
                description: match.replace(/场景\s*\d+[：:]/, '').trim(),
                confidence: 0.7
            }));
        }
        // Extract objects/keywords
        const objectMatches = text.match(/(?:物体|对象|元素)[：:]([^。\n]+)/g);
        if (objectMatches) {
            result.objects = objectMatches.map(match => ({
                name: match.replace(/(?:物体|对象|元素)[：:]/, '').trim(),
                category: 'general',
                confidence: 0.6
            }));
        }
        return result;
    }
    /**
     * Merge multiple analysis results
     */
    mergeAnalysisResults(target, source) {
        if (source.scenes && Array.isArray(source.scenes)) {
            target.scenes.push(...source.scenes);
        }
        if (source.objects && Array.isArray(source.objects)) {
            target.objects.push(...source.objects);
        }
        if (source.summary && !target.summary) {
            target.summary = source.summary;
        }
        if (source.productFeatures && !target.productFeatures) {
            target.productFeatures = source.productFeatures;
        }
    }
    /**
     * Build video metadata
     */
    async buildVideoMetadata(videoFile) {
        return {
            file: videoFile,
            technical: {
                codec: 'unknown',
                container: videoFile.format || 'unknown',
                hasAudio: true, // Assume has audio
                audioCodec: 'unknown'
            },
            content: {
                title: videoFile.name,
                description: `Video analysis for ${videoFile.name}`,
                tags: []
            }
        };
    }
    /**
     * Create default summary when none is provided
     */
    createDefaultSummary() {
        return {
            description: 'Video content analysis completed',
            highlights: [],
            topics: [],
            keywords: []
        };
    }
    /**
     * Calculate quality metrics for analysis results
     */
    calculateQualityMetrics(results) {
        let overallScore = 0.5; // Base score
        let detectionAccuracy = 0.5;
        let analysisDepth = 0.5;
        // Increase scores based on available data
        if (results.scenes && results.scenes.length > 0) {
            overallScore += 0.2;
            detectionAccuracy += 0.2;
        }
        if (results.objects && results.objects.length > 0) {
            overallScore += 0.2;
            detectionAccuracy += 0.2;
        }
        if (results.summary && results.summary.description) {
            overallScore += 0.1;
            analysisDepth += 0.3;
        }
        if (results.productFeatures) {
            analysisDepth += 0.2;
        }
        return {
            overallScore: Math.min(1.0, overallScore),
            detectionAccuracy: Math.min(1.0, detectionAccuracy),
            analysisDepth: Math.min(1.0, analysisDepth)
        };
    }
    /**
     * 确保缓存目录存在
     */
    async ensureCacheDir() {
        try {
            await access(this.cacheConfig.cacheDir);
        }
        catch {
            await fs.promises.mkdir(this.cacheConfig.cacheDir, { recursive: true });
        }
    }
    /**
     * 生成缓存键
     */
    generateAnalysisCacheKey(videoPath, gcsPath, prompt, options) {
        const crypto = require('crypto');
        const keyData = {
            videoPath,
            gcsPath,
            prompt: prompt.substring(0, 100), // 只取前100个字符避免键过长
            options: JSON.stringify(options)
        };
        return crypto.createHash('md5').update(JSON.stringify(keyData)).digest('hex');
    }
    /**
     * 计算文件校验和
     */
    async calculateFileChecksum(filePath) {
        try {
            const crypto = require('crypto');
            const fileBuffer = await readFile(filePath);
            return crypto.createHash('md5').update(fileBuffer).digest('hex');
        }
        catch (error) {
            // 如果无法读取文件，使用路径和修改时间作为校验和
            const stats = await fs.promises.stat(filePath);
            const crypto = require('crypto');
            return crypto.createHash('md5').update(`${filePath}-${stats.mtime.getTime()}`).digest('hex');
        }
    }
    /**
     * 检查分析缓存
     */
    async checkAnalysisCache(videoPath, gcsPath, prompt, options) {
        if (!this.cacheConfig.enableCache) {
            return null;
        }
        try {
            await this.ensureCacheDir();
            const cacheKey = this.generateAnalysisCacheKey(videoPath, gcsPath, prompt, options);
            const cacheFilePath = path.join(this.cacheConfig.cacheDir, `${cacheKey}.json`);
            // 检查缓存文件是否存在
            try {
                await access(cacheFilePath);
            }
            catch {
                return null; // 缓存文件不存在
            }
            // 读取缓存内容
            const cacheContent = await readFile(cacheFilePath, 'utf8');
            const cacheEntry = JSON.parse(cacheContent);
            // 检查缓存是否过期
            const now = Date.now();
            if (now - cacheEntry.timestamp > this.cacheConfig.cacheExpiry) {
                // 删除过期缓存
                await fs.promises.unlink(cacheFilePath).catch(() => { });
                return null;
            }
            // 检查文件是否发生变化
            const currentChecksum = await this.calculateFileChecksum(videoPath);
            if (currentChecksum !== cacheEntry.checksum) {
                // 文件已变化，删除缓存
                await fs.promises.unlink(cacheFilePath).catch(() => { });
                return null;
            }
            // 验证缓存条目的完整性
            if (cacheEntry.videoPath === videoPath &&
                cacheEntry.gcsPath === gcsPath &&
                cacheEntry.prompt === prompt) {
                return cacheEntry.result;
            }
            return null;
        }
        catch (error) {
            console.warn('检查分析缓存失败:', error);
            return null;
        }
    }
    /**
     * 保存分析结果到缓存
     */
    async saveAnalysisCache(videoPath, gcsPath, prompt, options, result) {
        if (!this.cacheConfig.enableCache) {
            return;
        }
        try {
            await this.ensureCacheDir();
            const cacheKey = this.generateAnalysisCacheKey(videoPath, gcsPath, prompt, options);
            const cacheFilePath = path.join(this.cacheConfig.cacheDir, `${cacheKey}.json`);
            const checksum = await this.calculateFileChecksum(videoPath);
            const cacheEntry = {
                videoPath,
                gcsPath,
                prompt,
                options,
                result,
                timestamp: Date.now(),
                checksum
            };
            await writeFile(cacheFilePath, JSON.stringify(cacheEntry, null, 2), 'utf8');
            console.log(`💾 分析结果已缓存: ${path.basename(videoPath)}`);
        }
        catch (error) {
            console.warn('保存分析缓存失败:', error);
            // 缓存失败不应该影响主流程
        }
    }
    /**
     * 清理过期的分析缓存
     */
    async cleanExpiredAnalysisCache() {
        if (!this.cacheConfig.enableCache) {
            return;
        }
        try {
            await this.ensureCacheDir();
            const files = await fs.promises.readdir(this.cacheConfig.cacheDir);
            const now = Date.now();
            let cleanedCount = 0;
            for (const file of files) {
                if (!file.endsWith('.json'))
                    continue;
                const filePath = path.join(this.cacheConfig.cacheDir, file);
                try {
                    const content = await readFile(filePath, 'utf8');
                    const cacheEntry = JSON.parse(content);
                    if (now - cacheEntry.timestamp > this.cacheConfig.cacheExpiry) {
                        await fs.promises.unlink(filePath);
                        cleanedCount++;
                    }
                }
                catch (error) {
                    // 删除损坏的缓存文件
                    await fs.promises.unlink(filePath).catch(() => { });
                    cleanedCount++;
                }
            }
            if (cleanedCount > 0) {
                console.log(`🧹 清理了 ${cleanedCount} 个过期分析缓存文件`);
            }
        }
        catch (error) {
            console.warn('清理分析缓存失败:', error);
        }
    }
    /**
     * 获取分析缓存统计信息
     */
    async getAnalysisCacheStats() {
        if (!this.cacheConfig.enableCache) {
            return { totalFiles: 0, totalSize: 0, oldestEntry: null, newestEntry: null };
        }
        try {
            await this.ensureCacheDir();
            const files = await fs.promises.readdir(this.cacheConfig.cacheDir);
            let totalFiles = 0;
            let totalSize = 0;
            let oldestTimestamp = Infinity;
            let newestTimestamp = 0;
            for (const file of files) {
                if (!file.endsWith('.json'))
                    continue;
                const filePath = path.join(this.cacheConfig.cacheDir, file);
                try {
                    const stats = await fs.promises.stat(filePath);
                    const content = await readFile(filePath, 'utf8');
                    const cacheEntry = JSON.parse(content);
                    totalFiles++;
                    totalSize += stats.size;
                    oldestTimestamp = Math.min(oldestTimestamp, cacheEntry.timestamp);
                    newestTimestamp = Math.max(newestTimestamp, cacheEntry.timestamp);
                }
                catch (error) {
                    // 忽略损坏的文件
                }
            }
            return {
                totalFiles,
                totalSize,
                oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),
                newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)
            };
        }
        catch (error) {
            return { totalFiles: 0, totalSize: 0, oldestEntry: null, newestEntry: null };
        }
    }
}
exports.AnalysisEngine = AnalysisEngine;
/**
 * Convenience function to analyze a video
 */
async function analyzeVideoWithGemini(videoFile, gcsPath, mode, options, onProgress, cacheConfig) {
    const engine = new AnalysisEngine(cacheConfig);
    return engine.analyzeVideo(videoFile, gcsPath, mode, options, onProgress);
}
