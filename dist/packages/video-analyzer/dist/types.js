"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VideoAnalyzerError = void 0;
/**
 * Core types and interfaces for video analysis
 */
class VideoAnalyzerError extends Error {
    constructor(message, code, details, file, stage) {
        super(message);
        this.name = 'VideoAnalyzerError';
        this.code = code;
        this.details = details;
        this.file = file;
        this.stage = stage;
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, VideoAnalyzerError);
        }
    }
}
exports.VideoAnalyzerError = VideoAnalyzerError;
//# sourceMappingURL=types.js.map
