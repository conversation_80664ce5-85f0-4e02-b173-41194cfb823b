"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VideoUploader = exports.DEFAULT_UPLOAD_CONFIG = void 0;
exports.uploadVideoToGemini = uploadVideoToGemini;
exports.uploadVideosToGemini = uploadVideosToGemini;
/**
 * Video file uploader for Gemini AI
 */
const fs = __importStar(require("fs"));
const util_1 = require("util");
const gemini_1 = require("@mixvideo/gemini");
const types_1 = require("./types");
const readFile = (0, util_1.promisify)(fs.readFile);
const stat = (0, util_1.promisify)(fs.stat);
/**
 * Default upload configuration
 */
exports.DEFAULT_UPLOAD_CONFIG = {
    chunkSize: 10 * 1024 * 1024, // 10MB chunks
    maxRetries: 3,
    timeout: 300000, // 5 minutes
    onProgress: () => { }
};
/**
 * Video uploader class
 */
class VideoUploader {
    constructor(config) {
        this.config = {
            ...exports.DEFAULT_UPLOAD_CONFIG,
            ...config
        };
    }
    /**
     * Upload a single video file to Gemini
     */
    async uploadVideo(videoFile) {
        const startTime = Date.now();
        const uploadId = this.generateUploadId(videoFile);
        const progress = {
            step: 'Preparing upload',
            progress: 0,
            currentFile: videoFile.name,
            bytesUploaded: 0,
            totalBytes: videoFile.size
        };
        this.config.onProgress(progress);
        try {
            // Validate file before upload
            await this.validateFileForUpload(videoFile);
            progress.step = 'Reading file';
            progress.progress = 10;
            this.config.onProgress(progress);
            // Read file content
            const fileBuffer = await this.readVideoFile(videoFile);
            progress.step = 'Uploading to Gemini';
            progress.progress = 20;
            this.config.onProgress(progress);
            // Generate GCS path
            const gcsPath = this.generateGcsPath(videoFile);
            // Upload with retry logic
            await this.uploadWithRetry(fileBuffer, gcsPath, progress);
            const uploadTime = Date.now() - startTime;
            progress.step = 'Upload completed';
            progress.progress = 100;
            progress.bytesUploaded = videoFile.size;
            this.config.onProgress(progress);
            return {
                videoFile,
                success: true,
                gcsPath,
                uploadTime,
                bytesUploaded: videoFile.size,
                metadata: {
                    uploadId,
                    timestamp: new Date(),
                    checksum: await this.calculateChecksum(fileBuffer)
                }
            };
        }
        catch (error) {
            const uploadTime = Date.now() - startTime;
            return {
                videoFile,
                success: false,
                uploadTime,
                bytesUploaded: progress.bytesUploaded,
                error: this.getErrorMessage(error),
                metadata: {
                    uploadId,
                    timestamp: new Date()
                }
            };
        }
    }
    getErrorMessage(error) {
        return error instanceof Error ? error.message : String(error);
    }
    /**
     * Upload multiple video files
     */
    async uploadVideos(videoFiles) {
        const results = [];
        for (let i = 0; i < videoFiles.length; i++) {
            const videoFile = videoFiles[i];
            try {
                const result = await this.uploadVideo(videoFile);
                results.push(result);
                // Update overall progress
                const overallProgress = ((i + 1) / videoFiles.length) * 100;
                this.config.onProgress({
                    step: `Uploaded ${i + 1}/${videoFiles.length} files`,
                    progress: overallProgress,
                    currentFile: videoFile.name,
                    bytesUploaded: results.reduce((sum, r) => sum + r.bytesUploaded, 0),
                    totalBytes: videoFiles.reduce((sum, f) => sum + f.size, 0)
                });
            }
            catch (error) {
                results.push({
                    videoFile,
                    success: false,
                    uploadTime: 0,
                    bytesUploaded: 0,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        return results;
    }
    /**
     * Validate file before upload
     */
    async validateFileForUpload(videoFile) {
        // Check if file exists
        try {
            await stat(videoFile.path);
        }
        catch (error) {
            throw new types_1.VideoAnalyzerError(`File not found: ${videoFile.path}`, 'FILE_NOT_FOUND');
        }
        // Check file size
        if (videoFile.size === 0) {
            throw new types_1.VideoAnalyzerError(`File is empty: ${videoFile.path}`, 'EMPTY_FILE');
        }
        // Check if file is too large (Gemini has limits)
        const maxSize = 500 * 1024 * 1024; // 500MB
        if (videoFile.size > maxSize) {
            throw new types_1.VideoAnalyzerError(`File too large: ${videoFile.size} bytes (max: ${maxSize} bytes)`, 'FILE_TOO_LARGE');
        }
    }
    /**
     * Read video file into buffer
     */
    async readVideoFile(videoFile) {
        try {
            return await readFile(videoFile.path);
        }
        catch (error) {
            throw new types_1.VideoAnalyzerError(`Failed to read file: ${videoFile.path}`, 'READ_FAILED', error);
        }
    }
    /**
     * Upload with retry logic
     */
    async uploadWithRetry(fileBuffer, gcsPath, progress) {
        let lastError = null;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                progress.step = `Upload attempt ${attempt}/${this.config.maxRetries}`;
                this.config.onProgress(progress);
                await (0, gemini_1.uploadFileToGemini)(this.config.bucketName, gcsPath, fileBuffer);
                // Upload successful
                return;
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt < this.config.maxRetries) {
                    // Wait before retry with exponential backoff
                    const delay = Math.pow(2, attempt - 1) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw new types_1.VideoAnalyzerError(`Upload failed after ${this.config.maxRetries} attempts: ${lastError?.message}`, 'UPLOAD_FAILED', lastError);
    }
    /**
     * Generate GCS path for video file
     */
    generateGcsPath(videoFile) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const sanitizedName = videoFile.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        return `${this.config.filePrefix}${timestamp}_${sanitizedName}.${videoFile.format}`;
    }
    /**
     * Generate unique upload ID
     */
    generateUploadId(videoFile) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        return `upload_${timestamp}_${random}`;
    }
    /**
     * Calculate file checksum for verification
     */
    async calculateChecksum(buffer) {
        const crypto = await Promise.resolve().then(() => __importStar(require('crypto')));
        return crypto.createHash('md5').update(buffer).digest('hex');
    }
    /**
     * Get upload statistics
     */
    getUploadStatistics(results) {
        const totalFiles = results.length;
        const successfulUploads = results.filter(r => r.success).length;
        const failedUploads = totalFiles - successfulUploads;
        const totalBytesUploaded = results.reduce((sum, r) => sum + r.bytesUploaded, 0);
        const totalUploadTime = results.reduce((sum, r) => sum + r.uploadTime, 0);
        const averageUploadTime = totalFiles > 0 ? totalUploadTime / totalFiles : 0;
        const successRate = totalFiles > 0 ? (successfulUploads / totalFiles) * 100 : 0;
        return {
            totalFiles,
            successfulUploads,
            failedUploads,
            totalBytesUploaded,
            averageUploadTime,
            successRate
        };
    }
    /**
     * Update upload configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Get current upload configuration
     */
    getConfig() {
        return { ...this.config };
    }
}
exports.VideoUploader = VideoUploader;
/**
 * Convenience function to upload a single video
 */
async function uploadVideoToGemini(videoFile, config) {
    const uploader = new VideoUploader(config);
    return uploader.uploadVideo(videoFile);
}
/**
 * Convenience function to upload multiple videos
 */
async function uploadVideosToGemini(videoFiles, config) {
    const uploader = new VideoUploader(config);
    return uploader.uploadVideos(videoFiles);
}
//# sourceMappingURL=video-uploader.js.map
