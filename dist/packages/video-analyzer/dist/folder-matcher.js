"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FolderMatcher = exports.DEFAULT_FOLDER_MATCH_CONFIG = void 0;
exports.findMatchingFoldersForVideo = findMatchingFoldersForVideo;
/**
 * Smart folder matching system for video categorization
 */
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const util_1 = require("util");
const gemini_1 = require("@mixvideo/gemini");
const types_1 = require("./types");
const readdir = (0, util_1.promisify)(fs.readdir);
const stat = (0, util_1.promisify)(fs.stat);
/**
 * Default folder match configuration
 */
exports.DEFAULT_FOLDER_MATCH_CONFIG = {
    maxDepth: 3,
    minConfidence: 0.3,
    maxMatches: 5,
    enableSemanticAnalysis: true
};
/**
 * Smart folder matcher class
 */
class FolderMatcher {
    constructor(config) {
        this.geminiClient = null;
        this.folderCache = new Map();
        this.config = { ...exports.DEFAULT_FOLDER_MATCH_CONFIG, ...config };
    }
    /**
     * Initialize Gemini client
     */
    async initializeGeminiClient() {
        if (!this.geminiClient) {
            this.geminiClient = await (0, gemini_1.useGemini)();
        }
    }
    /**
     * Find matching folders for video analysis result
     */
    async findMatchingFolders(analysisResult) {
        try {
            // Get available folders
            const folders = await this.scanFolders();
            // Generate content description for matching
            const contentDescription = this.generateContentDescription(analysisResult);
            // Perform folder matching
            const matches = await this.performFolderMatching(contentDescription, folders);
            // Sort by confidence and return top matches
            return matches
                .filter(match => match.confidence >= this.config.minConfidence)
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, this.config.maxMatches);
        }
        catch (error) {
            throw new types_1.VideoAnalyzerError(`Folder matching failed: ${this.getErrorMessage(error)}`, 'FOLDER_MATCHING_FAILED', error);
        }
    }
    /**
     * Scan for available folders
     */
    async scanFolders() {
        const cacheKey = this.config.baseDirectory;
        if (this.folderCache.has(cacheKey)) {
            return this.folderCache.get(cacheKey);
        }
        const folders = [];
        await this.scanFoldersRecursive(this.config.baseDirectory, folders, 0);
        this.folderCache.set(cacheKey, folders);
        return folders;
    }
    /**
     * Recursively scan folders
     */
    async scanFoldersRecursive(dirPath, folders, currentDepth) {
        if (currentDepth >= this.config.maxDepth) {
            return;
        }
        try {
            const entries = await readdir(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                if (entry.isDirectory()) {
                    const fullPath = path.join(dirPath, entry.name);
                    folders.push(fullPath);
                    // Continue scanning subdirectories
                    await this.scanFoldersRecursive(fullPath, folders, currentDepth + 1);
                }
            }
        }
        catch (error) {
            console.warn(`Failed to scan directory ${dirPath}:`, this.getErrorMessage(error));
        }
    }
    /**
     * Generate content description for matching
     */
    generateContentDescription(analysisResult) {
        const parts = [];
        // Add summary description
        if (analysisResult.summary.description) {
            parts.push(`内容描述：${analysisResult.summary.description}`);
        }
        // Add keywords
        if (analysisResult.summary.keywords.length > 0) {
            parts.push(`关键词：${analysisResult.summary.keywords.join('、')}`);
        }
        // Add topics
        if (analysisResult.summary.topics.length > 0) {
            parts.push(`主题：${analysisResult.summary.topics.join('、')}`);
        }
        // Add product features if available
        if (analysisResult.productFeatures) {
            const features = analysisResult.productFeatures;
            if (features.appearance.colors.length > 0) {
                parts.push(`颜色：${features.appearance.colors.join('、')}`);
            }
            if (features.materials.length > 0) {
                parts.push(`材质：${features.materials.join('、')}`);
            }
            if (features.functionality.length > 0) {
                parts.push(`功能：${features.functionality.join('、')}`);
            }
        }
        // Add scene information
        if (analysisResult.scenes.length > 0) {
            const sceneDescriptions = analysisResult.scenes
                .map(scene => scene.description)
                .slice(0, 3); // Take first 3 scenes
            parts.push(`场景：${sceneDescriptions.join('、')}`);
        }
        // Add object information
        if (analysisResult.objects.length > 0) {
            const objectNames = analysisResult.objects
                .map(obj => obj.name)
                .slice(0, 5); // Take first 5 objects
            parts.push(`物体：${objectNames.join('、')}`);
        }
        return parts.join('\n');
    }
    /**
     * Perform folder matching using AI analysis
     */
    async performFolderMatching(contentDescription, folders) {
        const matches = [];
        // First, perform rule-based matching
        const ruleBasedMatches = this.performRuleBasedMatching(contentDescription, folders);
        matches.push(...ruleBasedMatches);
        // Then, perform semantic matching if enabled
        if (this.config.enableSemanticAnalysis) {
            const semanticMatches = await this.performSemanticMatching(contentDescription, folders);
            matches.push(...semanticMatches);
        }
        // Merge and deduplicate matches
        return this.mergeMatches(matches);
    }
    /**
     * Perform rule-based folder matching
     */
    performRuleBasedMatching(contentDescription, folders) {
        const matches = [];
        const contentLower = contentDescription.toLowerCase();
        for (const folderPath of folders) {
            const folderName = path.basename(folderPath).toLowerCase();
            const confidence = this.calculateRuleBasedConfidence(contentLower, folderName);
            if (confidence > 0) {
                matches.push({
                    folderPath,
                    folderName: path.basename(folderPath),
                    confidence,
                    reasons: this.generateRuleBasedReasons(contentLower, folderName),
                    semanticScore: 0,
                    relevanceScore: confidence,
                    action: this.determineAction(confidence)
                });
            }
        }
        return matches;
    }
    /**
     * Calculate rule-based confidence score
     */
    calculateRuleBasedConfidence(content, folderName) {
        let confidence = 0;
        // Direct keyword matching
        const keywords = this.extractKeywords(content);
        for (const keyword of keywords) {
            if (folderName.includes(keyword)) {
                confidence += 0.3;
            }
        }
        // Category matching
        const categories = this.extractCategories(content);
        for (const category of categories) {
            if (folderName.includes(category)) {
                confidence += 0.4;
            }
        }
        // Color matching
        const colors = this.extractColors(content);
        for (const color of colors) {
            if (folderName.includes(color)) {
                confidence += 0.2;
            }
        }
        return Math.min(1.0, confidence);
    }
    /**
     * Generate reasons for rule-based matching
     */
    generateRuleBasedReasons(content, folderName) {
        const reasons = [];
        const keywords = this.extractKeywords(content);
        for (const keyword of keywords) {
            if (folderName.includes(keyword)) {
                reasons.push(`关键词匹配：${keyword}`);
            }
        }
        const categories = this.extractCategories(content);
        for (const category of categories) {
            if (folderName.includes(category)) {
                reasons.push(`类别匹配：${category}`);
            }
        }
        return reasons;
    }
    /**
     * Perform semantic matching using Gemini AI
     */
    async performSemanticMatching(contentDescription, folders) {
        try {
            await this.initializeGeminiClient();
            const folderNames = folders.map(f => path.basename(f));
            const prompt = `请分析以下视频内容描述，并为其推荐最合适的文件夹：

视频内容描述：
${contentDescription}

可选文件夹：
${folderNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}

请为每个文件夹评分（0-1），并说明匹配原因。返回JSON格式：
{
  "matches": [
    {
      "folderName": "文件夹名称",
      "score": 0.8,
      "reasons": ["匹配原因1", "匹配原因2"]
    }
  ]
}`;
            const response = await this.geminiClient.generateText(prompt, 'gemini-2.5-flash', {
                temperature: 0.3,
                maxOutputTokens: 2048
            });
            if (response) {
                return this.parseSemanticMatchingResponse(response, folders);
            }
            return [];
        }
        catch (error) {
            console.warn('Semantic matching failed:', this.getErrorMessage(error));
            return [];
        }
    }
    /**
     * Parse semantic matching response
     */
    parseSemanticMatchingResponse(response, folders) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                return [];
            const data = JSON.parse(jsonMatch[0]);
            const matches = [];
            if (data.matches && Array.isArray(data.matches)) {
                for (const match of data.matches) {
                    const folderPath = folders.find(f => path.basename(f) === match.folderName);
                    if (folderPath && match.score > 0) {
                        matches.push({
                            folderPath,
                            folderName: match.folderName,
                            confidence: match.score,
                            reasons: match.reasons || [],
                            semanticScore: match.score,
                            relevanceScore: match.score,
                            action: this.determineAction(match.score)
                        });
                    }
                }
            }
            return matches;
        }
        catch (error) {
            console.warn('Failed to parse semantic matching response:', this.getErrorMessage(error));
            return [];
        }
    }
    getErrorMessage(error) {
        return error instanceof Error ? error.message : String(error);
    }
    /**
     * Merge and deduplicate matches
     */
    mergeMatches(matches) {
        const mergedMap = new Map();
        for (const match of matches) {
            const existing = mergedMap.get(match.folderPath);
            if (existing) {
                // Merge with existing match
                existing.confidence = Math.max(existing.confidence, match.confidence);
                existing.semanticScore = Math.max(existing.semanticScore, match.semanticScore);
                existing.relevanceScore = Math.max(existing.relevanceScore, match.relevanceScore);
                existing.reasons = [...new Set([...existing.reasons, ...match.reasons])];
                existing.action = this.determineAction(existing.confidence);
            }
            else {
                mergedMap.set(match.folderPath, { ...match });
            }
        }
        return Array.from(mergedMap.values());
    }
    /**
     * Determine recommended action based on confidence
     */
    determineAction(confidence) {
        if (confidence >= 0.8)
            return 'move';
        if (confidence >= 0.6)
            return 'copy';
        if (confidence >= 0.4)
            return 'link';
        return 'ignore';
    }
    /**
     * Extract keywords from content
     */
    extractKeywords(content) {
        const keywords = [];
        // Common product keywords
        const productKeywords = ['产品', '商品', '物品', '设备', '工具', '装置'];
        for (const keyword of productKeywords) {
            if (content.includes(keyword)) {
                keywords.push(keyword);
            }
        }
        return keywords;
    }
    /**
     * Extract categories from content
     */
    extractCategories(content) {
        const categories = [];
        // Common categories
        const categoryMap = {
            '电子': ['电子', '数码', '手机', '电脑', '相机'],
            '服装': ['服装', '衣服', '鞋子', '包包', '配饰'],
            '家居': ['家居', '家具', '装饰', '厨具', '床品'],
            '美妆': ['美妆', '化妆品', '护肤', '香水', '彩妆'],
            '食品': ['食品', '零食', '饮料', '茶叶', '咖啡']
        };
        for (const [category, keywords] of Object.entries(categoryMap)) {
            for (const keyword of keywords) {
                if (content.includes(keyword)) {
                    categories.push(category);
                    break;
                }
            }
        }
        return categories;
    }
    /**
     * Extract colors from content
     */
    extractColors(content) {
        const colors = ['红', '蓝', '绿', '黄', '黑', '白', '灰', '紫', '粉', '橙'];
        return colors.filter(color => content.includes(color));
    }
    /**
     * Clear folder cache
     */
    clearCache() {
        this.folderCache.clear();
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
        this.clearCache(); // Clear cache when config changes
    }
}
exports.FolderMatcher = FolderMatcher;
/**
 * Convenience function to find matching folders
 */
async function findMatchingFoldersForVideo(analysisResult, config) {
    const matcher = new FolderMatcher(config);
    return matcher.findMatchingFolders(analysisResult);
}
//# sourceMappingURL=folder-matcher.js.map
