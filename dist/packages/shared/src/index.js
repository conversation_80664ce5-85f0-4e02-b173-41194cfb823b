"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VIDEO_FORMATS = exports.API_ENDPOINTS = exports.validateEmail = exports.formatDuration = void 0;
// Shared utilities
const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
exports.formatDuration = formatDuration;
const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.validateEmail = validateEmail;
// Constants
exports.API_ENDPOINTS = {
    USERS: '/api/users',
    VIDEOS: '/api/videos',
    AUTH: '/api/auth',
};
exports.VIDEO_FORMATS = ['mp4', 'webm', 'avi', 'mov'];
